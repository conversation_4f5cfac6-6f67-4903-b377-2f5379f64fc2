<svg width="132" height="107" viewBox="0 0 132 107" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_b_27922_9362)">
<rect x="117" y="5" width="94" height="104" rx="8" transform="rotate(90 117 5)" fill="url(#paint0_linear_27922_9362)"/>
</g>
<path d="M2 94.4863L2 98.8942C2 101.103 3.79086 102.894 6 102.894H9.19648" stroke="#D82D8D" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.19653 2.00049L6.00006 2.00049C3.79092 2.00049 2.00005 3.79135 2.00005 6.00049L2.00005 10.4083" stroke="#D82D8D" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M129.138 10.4077L129.138 5.99988C129.138 3.79074 127.347 1.99988 125.138 1.99988L121.941 1.99988" stroke="#D82D8D" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M121.941 104.295L125.138 104.295C127.347 104.295 129.138 102.505 129.138 100.295L129.138 95.8876" stroke="#D82D8D" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
<g filter="url(#filter1_b_27922_9362)">
<mask id="path-6-outside-1_27922_9362" maskUnits="userSpaceOnUse" x="31.0674" y="8" width="69" height="84" fill="black">
<rect fill="white" x="31.0674" y="8" width="69" height="84"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M43.7219 30.583C43.7219 42.5643 53.5263 52.1659 65.7608 52.1659C77.9908 52.1659 87.7997 42.5643 87.7997 30.583C87.7997 18.6016 77.9908 9 65.7608 9C53.5263 9 43.7219 18.6016 43.7219 30.583ZM99.0674 76.4496C99.0674 65.3585 83.7244 62.5835 65.7607 62.5835C47.6994 62.5835 32.4539 65.454 32.4539 76.5538C32.4539 87.6449 47.7969 90.4199 65.7607 90.4199C83.8219 90.4199 99.0674 87.5494 99.0674 76.4496Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M43.7219 30.583C43.7219 42.5643 53.5263 52.1659 65.7608 52.1659C77.9908 52.1659 87.7997 42.5643 87.7997 30.583C87.7997 18.6016 77.9908 9 65.7608 9C53.5263 9 43.7219 18.6016 43.7219 30.583ZM99.0674 76.4496C99.0674 65.3585 83.7244 62.5835 65.7607 62.5835C47.6994 62.5835 32.4539 65.454 32.4539 76.5538C32.4539 87.6449 47.7969 90.4199 65.7607 90.4199C83.8219 90.4199 99.0674 87.5494 99.0674 76.4496Z" fill="url(#paint1_linear_27922_9362)"/>
<path d="M65.7608 53.1659C52.9938 53.1659 42.7219 43.1362 42.7219 30.583H44.7219C44.7219 41.9924 54.0588 51.1659 65.7608 51.1659V53.1659ZM88.7997 30.583C88.7997 43.1363 78.5232 53.1659 65.7608 53.1659V51.1659C77.4585 51.1659 86.7997 41.9923 86.7997 30.583H88.7997ZM65.7608 8C78.5232 8 88.7997 18.0296 88.7997 30.583H86.7997C86.7997 19.1736 77.4585 10 65.7608 10V8ZM42.7219 30.583C42.7219 18.0297 52.9938 8 65.7608 8V10C54.0588 10 44.7219 19.1735 44.7219 30.583H42.7219ZM65.7607 61.5835C74.7644 61.5835 83.2292 62.2751 89.4741 64.4112C92.6022 65.4812 95.2462 66.9375 97.1163 68.9173C99.009 70.9209 100.067 73.4189 100.067 76.4496H98.0674C98.0674 73.9348 97.2079 71.9267 95.6624 70.2907C94.0943 68.6307 91.7798 67.3137 88.8268 66.3036C82.9092 64.2794 74.7206 63.5835 65.7607 63.5835V61.5835ZM31.4539 76.5538C31.4539 73.5233 32.5045 71.019 34.3879 69.0052C36.2494 67.0148 38.8837 65.5447 42.0066 64.4615C48.2406 62.2992 56.7065 61.5835 65.7607 61.5835V63.5835C56.7535 63.5835 48.5661 64.3031 42.662 66.351C39.7159 67.3729 37.4101 68.7017 35.8486 70.3713C34.309 72.0175 33.4539 74.0345 33.4539 76.5538H31.4539ZM65.7607 91.4199C56.7569 91.4199 48.2921 90.7283 42.0473 88.5922C38.9191 87.5222 36.2751 86.0659 34.405 84.0862C32.5124 82.0826 31.4539 79.5845 31.4539 76.5538H33.4539C33.4539 79.0687 34.3134 81.0767 35.8589 82.7128C37.427 84.3727 39.7415 85.6897 42.6946 86.6999C48.6121 88.724 56.8007 89.4199 65.7607 89.4199V91.4199ZM100.067 76.4496C100.067 79.4802 99.0168 81.9844 97.1334 83.9982C95.272 85.9887 92.6376 87.4587 89.5147 88.542C83.2808 90.7043 74.8148 91.4199 65.7607 91.4199V89.4199C74.7678 89.4199 82.9552 88.7003 88.8593 86.6524C91.8054 85.6305 94.1113 84.3017 95.6727 82.6321C97.2123 80.9859 98.0674 78.9689 98.0674 76.4496H100.067Z" fill="url(#paint2_linear_27922_9362)" mask="url(#path-6-outside-1_27922_9362)"/>
</g>
<defs>
<filter id="filter0_b_27922_9362" x="-1" y="-9" width="132" height="122" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="7"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_27922_9362"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_27922_9362" result="shape"/>
</filter>
<filter id="filter1_b_27922_9362" x="16.454" y="-7" width="98.6134" height="113.42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="7.5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_27922_9362"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_27922_9362" result="shape"/>
</filter>
<linearGradient id="paint0_linear_27922_9362" x1="207.475" y1="9.72728" x2="97.7442" y2="47.8839" gradientUnits="userSpaceOnUse">
<stop offset="0.197917" stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_27922_9362" x1="65.7607" y1="9" x2="65.7607" y2="90.4199" gradientUnits="userSpaceOnUse">
<stop offset="0.203125" stop-color="#8D56E8"/>
<stop offset="1" stop-color="#E47AFF"/>
</linearGradient>
<linearGradient id="paint2_linear_27922_9362" x1="88.4636" y1="18.4855" x2="35.3381" y2="72.3173" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.25"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
