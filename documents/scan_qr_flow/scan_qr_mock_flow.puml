@startuml
'https://plantuml.com/sequence-diagram

autonumber
actor User as user
participant E<PERSON><PERSON><PERSON> as app
participant EvoGateway as gateway

title Scan QR Code - Mock Flow

user -> app: open Scan QR Code screen
activate app
app --> user: Scan QR Code screen displayed
app --> app: check if MockTestFlow is enabled
deactivate app

alt #Salmon mockTestFlow is NOT enabled
    app --> user: back to normal flow
    activate user
    user -> app: do scanQRCode - normal flow
    deactivate user
else #beige mockTestFlow is enabled
    app --> user: show popup to request Storage permission
    activate user
    user -> app: User perform Grant or Deny
    deactivate user

    alt #Salmon deny Storage permission OR MockQRData is NOT available
        app --> user: back to normal flow
        activate user
        user -> app: do scanQRCode - normal flow
        deactivate user

    else #LightGreen  grant Storage permission AND MockQRData is available
        app --> app: check if checkPaymentMethod is loaded
        activate app
                alt #LightCyan PaymentMethod is not loaded
                    app -> gateway: GET user/payment-methods
                    note left
                    Ref: **[[https://portal-evo-vn-staging-internal.tsengineering.io/docs/#/Users/<USER>
                    end note
                    deactivate app
                    activate gateway

                    gateway --> app: return
                    deactivate gateway

                    alt #Salmon has failure
                        app --> user: show popup to inform failure
                        deactivate app

                    else #LightCyan User have NO PaymentMethod
                        app --> user: show popup to request user to Manual Link Card
                        deactivate app
                        activate user

                        user -> app: do Manual Link Card Flow
                        deactivate user

                    else #LightGreen User have a PaymentMethod
                        note over app
                             go to step #16
                        end note
                    deactivate app
                    end

                else #springgreen PaymentMethod is Loaded
                    app --> app: parse MockQRData
                    activate app

                    app -> gateway: GET /qr-code
                    deactivate app
                    activate gateway

                    note over of gateway
                        query param:
                            * qr-code: rawData
                        Ref: **[[https://portal-evo-vn-staging-internal.tsengineering.io/docs/#/QRCode/parse_qr_code API docs]]**
                    end note

                    gateway --> app: return
                    deactivate gateway

                    alt #Salmon qr-code invalid & not type PA02
                        app --> user: show popup to inform invalid qr-code

                    else #beige qr-code is type PA02
                        note over of app
                            response:
                                * merchantInfo
                        end note

                        app -> gateway: GET /store/{id}
                        activate gateway

                        note over of gateway
                            param:
                                * id: merchantInfo.id
                            Ref: **[[https://portal-evo-vn-staging-internal.tsengineering.io/docs/#/Merchant/get_evo_store API docs]]**
                        end note

                        gateway --> app: return
                        deactivate gateway

                        alt #Salmon store is not found or invalid
                            app --> user: show popup to inform store not found

                        else #LightGreen store found
                            note over of app
                                response:
                                    * storeInfo
                            end note

                            app --> user: open **PaymentInputAmountScreen**

                            note over of user
                                Go to Payment Flow
                            end note
                        end

                    end
                end
    end
end
@enduml
