@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"
actor "**User**" as user
participant "**EvoApp**" as app
participant "**EvoGateWay**" as be

note right user
    Refer ticket: [[https://trustingsocial1.atlassian.net/browse/EMA-4877 EMA-4877]]
end note

title EvoApp - Activate Card Introduction Screen

app --> user: Redirect to **ActivateCardIntroductionScreen**
opt #cyan user press Back button
    user -> app: press **back** button
    app --> user: redirect to **HomeScreen**
end

opt #cyan user press Tiếp tục
    user -> app: press **Tiếp tục** button
    app --> user: navigate to **ActivatePOSLimitScreen**
end

opt #cyan user press Đã kích hoạt thẻ
    user -> app : press **Đã kích hoạt thẻ** CTA
    app -> be: Call API PATCH /confirm-activation
    note right
        body:
            { platform: user_bypassed }
        Refer: [[https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3787620432/ES+CO+Dive-in+Card+activation+within+payment+flow#2.-Bypass-card-activation-checking-for-subsequent-transactions BE dive-in]]
    end note

    be --> app: API response

    alt #LightGreen status_code == 200
        app -> app: clear Activated POS Limit State
        app -> user: Pop to entry point, continue process payment
    else #Salmon status_code != 200
        app -> user: Show Toast Message
    end alt
end

@enduml