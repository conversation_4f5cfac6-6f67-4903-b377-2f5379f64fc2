import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'flutter_test_config.dart';

class MockBuildContext extends Mock implements BuildContext {}

class MockScaffoldMessengerState extends Mock
    implements ScaffoldMessengerState {
  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return super.toString();
  }
}

/// A mock implementation of GlobalKey that allows us to control the currentState
class MockGlobalKey<T extends State<StatefulWidget>> extends Mock
    implements GlobalKey<T> {
  final T? _currentState;

  MockGlobalKey(this._currentState);

  @override
  T? get currentState => _currentState;
}

void main() {
  late final SnackBarWrapper snackBarWrapper;
  late MockBuildContext mockNavigatorContext;
  late FeatureToggle mockFeatureToggle;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    mockNavigatorContext = MockBuildContext();
    setUpMockGlobalKeyProvider(mockNavigatorContext);

    getItRegisterFeatureToggle();

    mockFeatureToggle = getIt.get<FeatureToggle>();

    snackBarWrapper = SnackBarWrapper(mockFeatureToggle);
  });

  group('Test function getMarginBottom', () {
    testWidgets(
        'Give customMarginBottom null, screen size = (50, 100), should return margin bottom = 8',
        (WidgetTester tester) async {
      tester.view.physicalSize = const Size(50, 100);
      tester.view.devicePixelRatio = 1.0;

      /// Resets the screen to its original size after the test end
      addTearDown(() => tester.view.resetPhysicalSize());

      final double? result = snackBarWrapper.getMarginBottom(view: tester.view);
      expect(result, 8);
    });

    testWidgets(
        'Give marginBottomRatio = 0.5, screen size = (50, 100), should return margin bottom = 50',
        (WidgetTester tester) async {
      tester.view.physicalSize = const Size(50, 100);
      tester.view.devicePixelRatio = 1.0;

      /// Resets the screen to its original size after the test end
      addTearDown(() => tester.view.resetPhysicalSize());

      final double? result = snackBarWrapper.getMarginBottom(
          marginBottomRatio: 0.5, view: tester.view);
      expect(result, 50);
    });
  });

  test('Test constants', () {
    expect(SnackBarWrapper.defaultMarginBottomRatio, 0.08);
  });

  group('Test function cancelSnackBar', () {
    test('should call removeCurrentSnackBar on ScaffoldMessengerState', () {
      // Arrange
      final MockScaffoldMessengerState mockScaffoldMessengerState =
          MockScaffoldMessengerState();

      // Create a custom GlobalKey that we can control
      final MockGlobalKey<ScaffoldMessengerState> testScaffoldMessengerKey =
          MockGlobalKey<ScaffoldMessengerState>(mockScaffoldMessengerState);

      // Mock the removeCurrentSnackBar method
      when(() => mockScaffoldMessengerState.removeCurrentSnackBar())
          .thenReturn(null);

      // Override the existing mock to return our test key
      final GlobalKeyProvider globalKeyProvider =
          getIt.get<GlobalKeyProvider>();
      when(() => globalKeyProvider.scaffoldMessengerKey)
          .thenReturn(testScaffoldMessengerKey);

      // Act
      snackBarWrapper.cancelSnackBar();

      // Assert
      verify(() => mockScaffoldMessengerState.removeCurrentSnackBar())
          .called(1);
    });

    test('should handle null currentState gracefully', () {
      // Arrange
      final MockGlobalKey<ScaffoldMessengerState> testScaffoldMessengerKey =
          MockGlobalKey<ScaffoldMessengerState>(null);

      // Override the existing mock to return our test key
      final GlobalKeyProvider globalKeyProvider =
          getIt.get<GlobalKeyProvider>();
      when(() => globalKeyProvider.scaffoldMessengerKey)
          .thenReturn(testScaffoldMessengerKey);

      // Act & Assert - should not throw any exception
      expect(() => snackBarWrapper.cancelSnackBar(), returnsNormally);
    });
  });
}
