import 'dart:convert';
import 'dart:ui';

import 'package:evoapp/data/response/user_app_browsing_info_entity.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper_impl.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../feature/delete_account/attention_notes/widget/attention_notes_content_test.dart';
import '../flutter_test_config.dart';

class MockFlutterSecureStorage extends Mock implements FlutterSecureStorage {}

/// Class for testing [EvoSecureStorageHelperImpl]
/// And by pass the [containsKey] method
class TestEvoSecureStorageHelperImpl extends EvoSecureStorageHelperImpl {
  TestEvoSecureStorageHelperImpl({required super.secureStorage});

  @override
  Future<bool> containsKey({required String key}) async {
    return true;
  }
}

void main() {
  late FlutterSecureStorage secureStorage;
  late EvoSecureStorageHelperImpl dataSource;

  setUpAll(() {
    secureStorage = testFlutterSecureStorageExecutable();
    dataSource =
        testEvoSecureStorageHelperExecutable(secureStorage: secureStorage);
  });

  tearDownAll(() => getIt.reset());

  test('verify constant key', () {
    expect(EvoSecureStorageHelperImpl.selectedLanguageCodeKey,
        'selected_language_code');
    expect(EvoSecureStorageHelperImpl.accessTokenKey, 'access_token');
    expect(EvoSecureStorageHelperImpl.latestVersionIgnore,
        'latest_version_ignore');
    expect(EvoSecureStorageHelperImpl.deviceId, 'device_id');
    expect(EvoSecureStorageHelperImpl.userPhoneNumberKey, 'user_phone_number');
    expect(EvoSecureStorageHelperImpl.biometricTokenKey, 'biometric_token');
    expect(EvoSecureStorageHelperImpl.refreshTokenKey, 'refresh_token');
    expect(EvoSecureStorageHelperImpl.paymentTokenKey, 'payment_token');
    expect(EvoSecureStorageHelperImpl.deviceTokenKey, 'device_token');
    expect(EvoSecureStorageHelperImpl.enableBiometricAuthenticatorKey,
        'enable_biometric_authenticator');
    expect(EvoSecureStorageHelperImpl.timeShowBiometricActiveKey,
        'time_show_biometric_active');
    expect(EvoSecureStorageHelperImpl.decreeConsentStatusKey,
        'decree_consent_status');
    expect(EvoSecureStorageHelperImpl.isNewDeviceKey, 'new_device');
    expect(EvoSecureStorageHelperImpl.isCreditLimitMaskedKey,
        'credit_limit_masked_status');
    expect(EvoSecureStorageHelperImpl.chatwootIdForNonEvoUserKey,
        'chatwoot_id_for_non_evo_user');
    expect(EvoSecureStorageHelperImpl.enablePosLimitWarningKey,
        'enable_pos_limit_warning');
    expect(EvoSecureStorageHelperImpl.consentAgreedKey, 'consent_agreed');
    expect(EvoSecureStorageHelperImpl.hasShowedRequestNotifyPermissionKey,
        'has_showed_request_notify_permission');
    expect(EvoSecureStorageHelperImpl.userAppBrowsingInfoEntityKey,
        'user_app_browsing_info_entity');
    expect(EvoSecureStorageHelperImpl.lastTimeRequest3DSCardActivationKey,
        'last_time_request_3ds_card_activation');
    expect(
        EvoSecureStorageHelperImpl.reviewStorePopupKey, 'review_store_popup');
  });

  group('Test get/set RefreshToken', () {
    const String refreshTokenKey = EvoSecureStorageHelperImpl.refreshTokenKey;
    const String refreshTokenValue = 'evo_refresh_token_value_test';

    test('should_setRefreshToken()_correctly', () async {
      expect(await secureStorage.containsKey(key: refreshTokenKey), false);
      await dataSource.setRefreshToken(refreshTokenValue);
      expect(await secureStorage.containsKey(key: refreshTokenKey), true);
      expect(dataSource.memoryData.containsKey(refreshTokenKey), true);

      final String? getRefreshTokenValue =
          await secureStorage.read(key: refreshTokenKey);
      expect(getRefreshTokenValue, refreshTokenValue);
      expect(dataSource.memoryData[refreshTokenKey], refreshTokenValue);

      // remove key test
      await secureStorage.delete(key: refreshTokenKey);
    });

    test('should_get_getRefreshToken_correctly', () async {
      expect(await secureStorage.containsKey(key: refreshTokenKey), false);
      await dataSource.write(key: refreshTokenKey, value: refreshTokenValue);
      expect(await secureStorage.containsKey(key: refreshTokenKey), true);
      expect(dataSource.memoryData.containsKey(refreshTokenKey), true);

      final String? getRefreshTokenValue = await dataSource.getRefreshToken();
      expect(getRefreshTokenValue, refreshTokenValue);
      expect(dataSource.memoryData[refreshTokenKey], refreshTokenValue);

      await dataSource.delete(key: refreshTokenKey);
      expect(await secureStorage.containsKey(key: refreshTokenKey), false);
      expect(await dataSource.read(key: refreshTokenKey), null);
      expect(await dataSource.containsKey(key: refreshTokenKey), false);
      expect(dataSource.memoryData.containsKey(refreshTokenKey), false);
    });

    group('test supported methods', () {
      test('test methods that related to access token', () async {
        const String accessToken = 'access_token';
        await dataSource.setAccessToken(accessToken);

        // local storage
        expect(
            await secureStorage.containsKey(
                key: EvoSecureStorageHelperImpl.accessTokenKey),
            true);
        expect(
            await secureStorage.read(
                key: EvoSecureStorageHelperImpl.accessTokenKey),
            accessToken);
        // memory
        expect(
            dataSource.memoryData
                .containsKey(EvoSecureStorageHelperImpl.accessTokenKey),
            true);
        expect(dataSource.memoryData[EvoSecureStorageHelperImpl.accessTokenKey],
            accessToken);
        // return to outside
        expect(
            await dataSource.containsKey(
                key: EvoSecureStorageHelperImpl.accessTokenKey),
            true);
        expect(await dataSource.getAccessToken(), accessToken);
      });

      test('test other methods', () async {
        final String timeShowBiometric = DateTime.now().toIso8601String();
        await dataSource.saveTimeShowBiometric(timeShowBiometric);
        await dataSource.setDecreeConsentStatus(true);

        expect(await dataSource.getTimeShowBiometric(), timeShowBiometric);
        expect(await dataSource.getDecreeConsentStatus(), true);
      });
    });
  });

  group('test set/get NewDevice', () {
    const String newDeviceKey = EvoSecureStorageHelperImpl.isNewDeviceKey;
    const String newDeviceDefaultValue = 'true';

    setUp(() async {
      // remove key test
      await secureStorage.delete(key: newDeviceKey);
    });

    test('should set newDevice correct', () async {
      expect(await secureStorage.containsKey(key: newDeviceKey), false);
      await dataSource.setNewDevice(false);
      expect(await secureStorage.containsKey(key: newDeviceKey), true);
      expect(dataSource.memoryData.containsKey(newDeviceKey), true);

      final String? isNewDevice = await secureStorage.read(key: newDeviceKey);
      expect(isNewDevice, false.toString());
      expect(dataSource.memoryData[newDeviceKey], isNewDevice);
    });

    test('should get newDevice correct', () async {
      expect(await secureStorage.containsKey(key: newDeviceKey), false);
      await dataSource.write(key: newDeviceKey, value: newDeviceDefaultValue);
      expect(await secureStorage.containsKey(key: newDeviceKey), true);
      expect(dataSource.memoryData.containsKey(newDeviceKey), true);

      final String getRefreshTokenValue =
          (await dataSource.isNewDevice()).toString();
      expect(getRefreshTokenValue, newDeviceDefaultValue);
      expect(dataSource.memoryData[newDeviceKey], newDeviceDefaultValue);

      await dataSource.delete(key: newDeviceKey);
      expect(await secureStorage.containsKey(key: newDeviceKey), false);
      expect(await dataSource.read(key: newDeviceKey), null);
      expect(await dataSource.containsKey(key: newDeviceKey), false);
      expect(dataSource.memoryData.containsKey(newDeviceKey), false);
    });
  });

  group('test set/get creditLimitMaskedStatusKey', () {
    const String maskedStatusKey =
        EvoSecureStorageHelperImpl.isCreditLimitMaskedKey;
    const bool newValueMaskedStatus = true;
    final String newValueMaskedStatusString = newValueMaskedStatus.toString();

    tearDown(() async {
      await secureStorage.delete(key: maskedStatusKey);
    });

    test('verify key not exits', () async {
      expect(await secureStorage.containsKey(key: maskedStatusKey), false);
    });

    test('should set masked status correct', () async {
      expect(await secureStorage.containsKey(key: maskedStatusKey), false);
      await dataSource.setCreditLimitMasked(newValueMaskedStatus);
      expect(await dataSource.isCreditLimitMasked(), newValueMaskedStatus);
      expect(await secureStorage.containsKey(key: maskedStatusKey), true);
      expect(dataSource.memoryData.containsKey(maskedStatusKey), true);

      final String? getMaskedStatus =
          await secureStorage.read(key: maskedStatusKey);
      expect(getMaskedStatus, newValueMaskedStatusString);
      expect(dataSource.memoryData[maskedStatusKey], getMaskedStatus);
    });

    test('should get masked status correct', () async {
      expect(await secureStorage.containsKey(key: maskedStatusKey), false);
      await dataSource.write(
          key: maskedStatusKey, value: newValueMaskedStatusString);
      expect(await secureStorage.containsKey(key: maskedStatusKey), true);
      expect(dataSource.memoryData.containsKey(maskedStatusKey), true);

      final String getMaskedStatus =
          (await dataSource.isNewDevice()).toString();
      expect(getMaskedStatus, newValueMaskedStatusString);
      expect(
          dataSource.memoryData[maskedStatusKey], newValueMaskedStatusString);

      await dataSource.delete(key: maskedStatusKey);
      expect(await secureStorage.containsKey(key: maskedStatusKey), false);
      expect(await dataSource.read(key: maskedStatusKey), null);
      expect(await dataSource.containsKey(key: maskedStatusKey), false);
      expect(dataSource.memoryData.containsKey(maskedStatusKey), false);
    });
  });

  group('test set/get Chatwoot ID Storage', () {
    const String chatwootIdKey =
        EvoSecureStorageHelperImpl.chatwootIdForNonEvoUserKey;
    const String testChatwootId = 'test-chatwoot-id';

    setUp(() async {
      // remove key test
      await secureStorage.delete(key: chatwootIdKey);
    });

    test('should set ChatwootIdForNonEvoUser correct', () async {
      expect(await secureStorage.containsKey(key: chatwootIdKey), false);
      await dataSource.setChatwootIdForNonEvoUser(testChatwootId);

      expect(await secureStorage.containsKey(key: chatwootIdKey), true);
      expect(dataSource.memoryData.containsKey(chatwootIdKey), true);

      final String? chatWoodId = await secureStorage.read(key: chatwootIdKey);
      expect(chatWoodId, testChatwootId);
      expect(dataSource.memoryData[chatwootIdKey], testChatwootId);
    });

    test('should get ChatwootIdForNonEvoUser correct', () async {
      expect(await secureStorage.containsKey(key: chatwootIdKey), false);
      await dataSource.write(key: chatwootIdKey, value: testChatwootId);
      expect(await secureStorage.containsKey(key: chatwootIdKey), true);
      expect(dataSource.memoryData.containsKey(chatwootIdKey), true);

      final String? chatWoodId = await dataSource.getChatwootIdForNonEvoUser();
      expect(chatWoodId, testChatwootId);
      expect(dataSource.memoryData[chatwootIdKey], testChatwootId);

      await dataSource.delete(key: chatwootIdKey);
      expect(await secureStorage.containsKey(key: chatwootIdKey), false);
      expect(await dataSource.read(key: chatwootIdKey), null);
      expect(await dataSource.containsKey(key: chatwootIdKey), false);
      expect(dataSource.memoryData.containsKey(chatwootIdKey), false);
    });
  });

  group('test set/get Enable Pos Limit Warning', () {
    const String enablePosLimitWarningKey =
        EvoSecureStorageHelperImpl.enablePosLimitWarningKey;
    const bool valuePosLimitWarning = true;

    setUp(() async {
      // remove key test
      await secureStorage.delete(key: enablePosLimitWarningKey);
    });

    test('should set Enable Pos Limit Warning correct', () async {
      expect(await secureStorage.containsKey(key: enablePosLimitWarningKey),
          false);
      await dataSource.setEnablePosLimitWarning(valuePosLimitWarning);

      expect(
          await secureStorage.containsKey(key: enablePosLimitWarningKey), true);
      expect(dataSource.memoryData.containsKey(enablePosLimitWarningKey), true);

      final String? getPosLimitWarningValueStr =
          await secureStorage.read(key: enablePosLimitWarningKey);
      expect(getPosLimitWarningValueStr, valuePosLimitWarning.toString());
      expect(dataSource.memoryData[enablePosLimitWarningKey],
          valuePosLimitWarning.toString());
    });

    test('should get Enable Pos Limit Warning correct', () async {
      expect(await secureStorage.containsKey(key: enablePosLimitWarningKey),
          false);
      await dataSource.write(
          key: enablePosLimitWarningKey,
          value: valuePosLimitWarning.toString());
      expect(
          await secureStorage.containsKey(key: enablePosLimitWarningKey), true);
      expect(dataSource.memoryData.containsKey(enablePosLimitWarningKey), true);

      final bool? enablePosLimitWarning =
          await dataSource.getEnablePosLimitWarning();
      expect(enablePosLimitWarning, valuePosLimitWarning);
      expect(dataSource.memoryData[enablePosLimitWarningKey],
          valuePosLimitWarning.toString());

      await dataSource.delete(key: enablePosLimitWarningKey);
      expect(await secureStorage.containsKey(key: enablePosLimitWarningKey),
          false);
      expect(await dataSource.read(key: enablePosLimitWarningKey), null);
      expect(
          await dataSource.containsKey(key: enablePosLimitWarningKey), false);
      expect(
          dataSource.memoryData.containsKey(enablePosLimitWarningKey), false);
    });
  });

  group('test set/get ConsentAgreed', () {
    const String consentAgreedKey = EvoSecureStorageHelperImpl.consentAgreedKey;
    const bool testConsentAgreed = false;

    setUp(() async {
      // remove key test
      await secureStorage.delete(key: consentAgreedKey);
    });

    test('should set ConsentAgreed correct', () async {
      expect(await secureStorage.containsKey(key: consentAgreedKey), false);
      await dataSource.setConsentAgreed(testConsentAgreed);

      expect(await secureStorage.containsKey(key: consentAgreedKey), true);
      expect(dataSource.memoryData.containsKey(consentAgreedKey), true);

      final String? consentAgreedResult =
          await secureStorage.read(key: consentAgreedKey);
      expect(consentAgreedResult?.toBool(), testConsentAgreed);
      expect(
          dataSource.memoryData[consentAgreedKey]?.toBool(), testConsentAgreed);
    });

    test('should get ConsentAgreed correct', () async {
      expect(await secureStorage.containsKey(key: consentAgreedKey), false);
      await dataSource.write(
          key: consentAgreedKey, value: testConsentAgreed.toString());
      expect(await secureStorage.containsKey(key: consentAgreedKey), true);
      expect(dataSource.memoryData.containsKey(consentAgreedKey), true);

      final bool? consentAgreedResult = await dataSource.getConsentAgreed();
      expect(consentAgreedResult, testConsentAgreed);
      expect(
          dataSource.memoryData[consentAgreedKey]?.toBool(), testConsentAgreed);

      await dataSource.delete(key: consentAgreedKey);
      expect(await secureStorage.containsKey(key: consentAgreedKey), false);
      expect(await dataSource.read(key: consentAgreedKey), null);
      expect(await dataSource.containsKey(key: consentAgreedKey), false);
      expect(dataSource.memoryData.containsKey(consentAgreedKey), false);
    });
  });

  group('test set/get UserAppBrowsingInfoEntityKey', () {
    const String userAppBrowsingInfoEntityKey =
        EvoSecureStorageHelperImpl.userAppBrowsingInfoEntityKey;

    setUp(() async {
      // remove key test
      await secureStorage.delete(key: userAppBrowsingInfoEntityKey);
    });

    test('should set userAppBrowsingInfoEntityKey correct', () async {
      expect(await secureStorage.containsKey(key: userAppBrowsingInfoEntityKey),
          false);
      const UserAppBrowsingInfoEntity entity = UserAppBrowsingInfoEntity();
      await dataSource.setUserAppBrowsingInfoEntity(entity);

      expect(await secureStorage.containsKey(key: userAppBrowsingInfoEntityKey),
          true);
      expect(dataSource.memoryData.containsKey(userAppBrowsingInfoEntityKey),
          true);

      final String? entityResult =
          await secureStorage.read(key: userAppBrowsingInfoEntityKey);
      expect(entityResult, jsonEncode(entity.toJson()));
      expect(dataSource.memoryData[userAppBrowsingInfoEntityKey],
          jsonEncode(entity.toJson()));
    });

    test('should set userAppBrowsingInfoEntityKey with value is null',
        () async {
      expect(await secureStorage.containsKey(key: userAppBrowsingInfoEntityKey),
          false);
      await dataSource.setUserAppBrowsingInfoEntity(null);

      expect(await secureStorage.containsKey(key: userAppBrowsingInfoEntityKey),
          false);
      expect(dataSource.memoryData.containsKey(userAppBrowsingInfoEntityKey),
          true);

      final String? entityResult =
          await secureStorage.read(key: userAppBrowsingInfoEntityKey);
      expect(entityResult, null);
      expect(dataSource.memoryData[userAppBrowsingInfoEntityKey], null);
    });

    test('should get getUserAppBrowsingInfoEntity correct', () async {
      expect(await secureStorage.containsKey(key: userAppBrowsingInfoEntityKey),
          false);
      const UserAppBrowsingInfoEntity entity = UserAppBrowsingInfoEntity();
      await dataSource.write(
          key: userAppBrowsingInfoEntityKey,
          value: jsonEncode(entity.toJson()));
      expect(await secureStorage.containsKey(key: userAppBrowsingInfoEntityKey),
          true);
      expect(dataSource.memoryData.containsKey(userAppBrowsingInfoEntityKey),
          true);

      final UserAppBrowsingInfoEntity? userAppBrowsingInfoEntity =
          await dataSource.getUserAppBrowsingInfoEntity();
      expect(userAppBrowsingInfoEntity, entity);
      expect(dataSource.memoryData[userAppBrowsingInfoEntityKey],
          jsonEncode(entity.toJson()));

      await dataSource.delete(key: userAppBrowsingInfoEntityKey);
      expect(await secureStorage.containsKey(key: userAppBrowsingInfoEntityKey),
          false);
      expect(await dataSource.read(key: userAppBrowsingInfoEntityKey), null);
      expect(await dataSource.containsKey(key: userAppBrowsingInfoEntityKey),
          false);
      expect(dataSource.memoryData.containsKey(userAppBrowsingInfoEntityKey),
          false);
    });
  });

  group('test set/get ValueToCheckReviewPopupShown', () {
    const String reviewStorePopupKey =
        EvoSecureStorageHelperImpl.reviewStorePopupKey;
    const bool testValueToCheckReviewPopupShown = false;

    setUp(() async {
      // remove key test
      await secureStorage.delete(key: reviewStorePopupKey);
    });

    test('should set ValueToCheckReviewPopupShown correct', () async {
      expect(await secureStorage.containsKey(key: reviewStorePopupKey), false);
      await dataSource
          .setValueToCheckReviewPopupShown(testValueToCheckReviewPopupShown);

      expect(await secureStorage.containsKey(key: reviewStorePopupKey), true);
      expect(dataSource.memoryData.containsKey(reviewStorePopupKey), true);

      final String? result = await secureStorage.read(key: reviewStorePopupKey);
      expect(result?.toBool(), testValueToCheckReviewPopupShown);
      expect(dataSource.memoryData[reviewStorePopupKey]?.toBool(),
          testValueToCheckReviewPopupShown);
    });

    test('should get ValueToCheckReviewPopupShown correct', () async {
      expect(await secureStorage.containsKey(key: reviewStorePopupKey), false);
      await dataSource.write(
          key: reviewStorePopupKey,
          value: testValueToCheckReviewPopupShown.toString());
      expect(await secureStorage.containsKey(key: reviewStorePopupKey), true);
      expect(dataSource.memoryData.containsKey(reviewStorePopupKey), true);

      final bool? result = await dataSource.getValueToCheckReviewPopupShown();
      expect(result, testValueToCheckReviewPopupShown);
      expect(dataSource.memoryData[reviewStorePopupKey]?.toBool(),
          testValueToCheckReviewPopupShown);

      await dataSource.delete(key: reviewStorePopupKey);
      expect(await secureStorage.containsKey(key: reviewStorePopupKey), false);
      expect(await dataSource.read(key: reviewStorePopupKey), null);
      expect(await dataSource.containsKey(key: reviewStorePopupKey), false);
      expect(dataSource.memoryData.containsKey(reviewStorePopupKey), false);
    });
  });

  group('verify clearAllUserData()', () {
    final MockFlutterSecureStorage secureStorage = MockFlutterSecureStorage();
    late TestEvoSecureStorageHelperImpl dataSource;

    setUp(() {
      dataSource = TestEvoSecureStorageHelperImpl(secureStorage: secureStorage);

      when(() => secureStorage.delete(key: any(named: 'key')))
          .thenAnswer((_) async {
        return Future<void>.value();
      });
    });

    test('verify call delete()', () async {
      await dataSource.clearAllUserData();

      verify(() => secureStorage.delete(
          key: EvoSecureStorageHelperImpl.accessTokenKey)).called(1);
      verify(() => secureStorage.delete(
          key: EvoSecureStorageHelperImpl.userPhoneNumberKey)).called(1);
      verify(() => secureStorage.delete(
          key: EvoSecureStorageHelperImpl.refreshTokenKey)).called(1);
      verify(() => secureStorage.delete(
          key: EvoSecureStorageHelperImpl.biometricTokenKey)).called(1);
      verify(() => secureStorage.delete(
              key: EvoSecureStorageHelperImpl.enableBiometricAuthenticatorKey))
          .called(1);
      verify(() => secureStorage.delete(
              key: EvoSecureStorageHelperImpl.timeShowBiometricActiveKey))
          .called(1);
      verify(() => secureStorage.delete(
          key: EvoSecureStorageHelperImpl.deviceTokenKey)).called(1);
      verify(() => secureStorage.delete(
          key: EvoSecureStorageHelperImpl.decreeConsentStatusKey)).called(1);
      verify(() => secureStorage.delete(
          key: EvoSecureStorageHelperImpl.isNewDeviceKey)).called(1);
      verify(() => secureStorage.delete(
          key: EvoSecureStorageHelperImpl.isCreditLimitMaskedKey)).called(1);
      verify(() => secureStorage.delete(
          key: EvoSecureStorageHelperImpl.enablePosLimitWarningKey)).called(1);
    });
  });

  group('test set/get deleteAllSecureStorageData()', () {
    final MockFlutterSecureStorage secureStorage = MockFlutterSecureStorage();
    late TestEvoSecureStorageHelperImpl dataSource;

    setUp(() {
      dataSource = TestEvoSecureStorageHelperImpl(secureStorage: secureStorage);

      when(() => secureStorage.delete(key: any(named: 'key')))
          .thenAnswer((_) async {
        return Future<void>.value();
      });
    });

    test('verify deleteAllSecureStorageData()', () async {
      await dataSource.deleteAllSecureStorageData();

      verify(() => secureStorage.delete(
          key: EvoSecureStorageHelperImpl.accessTokenKey)).called(1);
      verify(() => secureStorage.delete(
          key: EvoSecureStorageHelperImpl.userPhoneNumberKey)).called(1);
      verify(() => secureStorage.delete(
          key: EvoSecureStorageHelperImpl.refreshTokenKey)).called(1);
      verify(() => secureStorage.delete(
          key: EvoSecureStorageHelperImpl.biometricTokenKey)).called(1);
      verify(() => secureStorage.delete(
              key: EvoSecureStorageHelperImpl.enableBiometricAuthenticatorKey))
          .called(1);
      verify(() => secureStorage.delete(
              key: EvoSecureStorageHelperImpl.timeShowBiometricActiveKey))
          .called(1);
      verify(() => secureStorage.delete(
          key: EvoSecureStorageHelperImpl.deviceTokenKey)).called(1);
      verify(() => secureStorage.delete(
          key: EvoSecureStorageHelperImpl.decreeConsentStatusKey)).called(1);
      verify(() => secureStorage.delete(
          key: EvoSecureStorageHelperImpl.isNewDeviceKey)).called(1);
      verify(() => secureStorage.delete(
          key: EvoSecureStorageHelperImpl.isCreditLimitMaskedKey)).called(1);
      verify(() => secureStorage.delete(
              key: EvoSecureStorageHelperImpl.userAppBrowsingInfoEntityKey))
          .called(1);

      verify(() => secureStorage.delete(
          key: EvoSecureStorageHelperImpl.selectedLanguageCodeKey)).called(1);
      verify(() => secureStorage.delete(
          key: EvoSecureStorageHelperImpl.latestVersionIgnore)).called(1);
      verify(() => secureStorage.delete(
          key: EvoSecureStorageHelperImpl.paymentTokenKey)).called(1);
      verify(() => secureStorage.delete(
              key: EvoSecureStorageHelperImpl.chatwootIdForNonEvoUserKey))
          .called(1);
      verifyNever(() => secureStorage.delete(
          key: EvoSecureStorageHelperImpl.consentAgreedKey));
    });
  });

  group('test set/get enable show request notification permission', () {
    const String hasShowRequestNotifyPermissionKey =
        EvoSecureStorageHelperImpl.hasShowedRequestNotifyPermissionKey;
    const bool valueShowRequestNotify = false;

    setUp(() async {
      await secureStorage.delete(key: hasShowRequestNotifyPermissionKey);
    });

    test('should set hasShowRequestNotifyPermission correct', () async {
      expect(
          await secureStorage.containsKey(
              key: hasShowRequestNotifyPermissionKey),
          false);
      await dataSource.setShowedRequestNotifyPermission(valueShowRequestNotify);

      expect(
          await secureStorage.containsKey(
              key: hasShowRequestNotifyPermissionKey),
          true);
      expect(
          dataSource.memoryData.containsKey(hasShowRequestNotifyPermissionKey),
          true);

      final String? getPosLimitWarningValueStr =
          await secureStorage.read(key: hasShowRequestNotifyPermissionKey);
      expect(getPosLimitWarningValueStr, valueShowRequestNotify.toString());
      expect(dataSource.memoryData[hasShowRequestNotifyPermissionKey],
          valueShowRequestNotify.toString());
    });

    test('should get hasShowRequestNotifyPermission correct', () async {
      expect(
          await secureStorage.containsKey(
              key: hasShowRequestNotifyPermissionKey),
          false);
      await dataSource.write(
          key: hasShowRequestNotifyPermissionKey,
          value: valueShowRequestNotify.toString());
      expect(
          await secureStorage.containsKey(
              key: hasShowRequestNotifyPermissionKey),
          true);
      expect(
          dataSource.memoryData.containsKey(hasShowRequestNotifyPermissionKey),
          true);

      final bool isShowRequest =
          await dataSource.hasShowRequestNotifyPermission();
      expect(isShowRequest, valueShowRequestNotify);
      expect(dataSource.memoryData[hasShowRequestNotifyPermissionKey],
          valueShowRequestNotify.toString());

      await dataSource.delete(key: hasShowRequestNotifyPermissionKey);
      expect(
          await secureStorage.containsKey(
              key: hasShowRequestNotifyPermissionKey),
          false);
      expect(
          await dataSource.read(key: hasShowRequestNotifyPermissionKey), null);
      expect(
          await dataSource.containsKey(key: hasShowRequestNotifyPermissionKey),
          false);
      expect(
          dataSource.memoryData.containsKey(hasShowRequestNotifyPermissionKey),
          false);
    });
  });

  group('test set/get Locale', () {
    const String selectedLanguageCodeKey =
        EvoSecureStorageHelperImpl.selectedLanguageCodeKey;
    const String testLanguageCode = 'vi';

    setUp(() async {
      await secureStorage.delete(key: selectedLanguageCodeKey);
    });

    test('should set Locale correct', () async {
      expect(
          await secureStorage.containsKey(key: selectedLanguageCodeKey), false);
      final Locale locale = await dataSource.setLocale(testLanguageCode);

      expect(
          await secureStorage.containsKey(key: selectedLanguageCodeKey), true);
      expect(dataSource.memoryData.containsKey(selectedLanguageCodeKey), true);

      final String? languageCode =
          await secureStorage.read(key: selectedLanguageCodeKey);
      expect(languageCode, testLanguageCode);
      expect(dataSource.memoryData[selectedLanguageCodeKey], testLanguageCode);
      expect(locale.languageCode, testLanguageCode);
    });

    test('should get Locale correct', () async {
      expect(
          await secureStorage.containsKey(key: selectedLanguageCodeKey), false);
      await dataSource.write(
          key: selectedLanguageCodeKey, value: testLanguageCode);
      expect(
          await secureStorage.containsKey(key: selectedLanguageCodeKey), true);
      expect(dataSource.memoryData.containsKey(selectedLanguageCodeKey), true);

      final Locale locale = await dataSource.getLocale();
      expect(locale.languageCode, testLanguageCode);
      expect(dataSource.memoryData[selectedLanguageCodeKey], testLanguageCode);

      await dataSource.delete(key: selectedLanguageCodeKey);
      expect(
          await secureStorage.containsKey(key: selectedLanguageCodeKey), false);
      expect(await dataSource.read(key: selectedLanguageCodeKey), null);
      expect(await dataSource.containsKey(key: selectedLanguageCodeKey), false);
      expect(dataSource.memoryData.containsKey(selectedLanguageCodeKey), false);
    });

    test('should get default Locale when languageCode is null', () async {
      dataSource.changeLanguage(MockBuildContext(), '');
      final Locale locale = await dataSource.getLocale();
      expect(locale.languageCode, 'en');
    });
  });

  group('test set/get latestVersionIgnore', () {
    const String latestVersionIgnore =
        EvoSecureStorageHelperImpl.latestVersionIgnore;
    const String latestVersion = '1.0.0';

    setUp(() async {
      await secureStorage.delete(key: latestVersionIgnore);
    });

    test('should set latestVersionIgnore correct', () async {
      expect(await secureStorage.containsKey(key: latestVersionIgnore), false);
      await dataSource.setLatestVersionIgnore(latestVersion);

      expect(await secureStorage.containsKey(key: latestVersionIgnore), true);
      expect(dataSource.memoryData.containsKey(latestVersionIgnore), true);

      final String? version =
          await secureStorage.read(key: latestVersionIgnore);
      expect(version, latestVersion);
      expect(dataSource.memoryData[latestVersionIgnore], latestVersion);
    });

    test('should get latestVersionIgnore correct', () async {
      expect(await secureStorage.containsKey(key: latestVersionIgnore), false);
      await dataSource.write(key: latestVersionIgnore, value: latestVersion);
      expect(await secureStorage.containsKey(key: latestVersionIgnore), true);
      expect(dataSource.memoryData.containsKey(latestVersionIgnore), true);

      final String? version = await dataSource.getLatestVersionIgnore();
      expect(version, latestVersion);
      expect(dataSource.memoryData[latestVersionIgnore], latestVersion);

      await dataSource.delete(key: latestVersionIgnore);
      expect(await secureStorage.containsKey(key: latestVersionIgnore), false);
      expect(await dataSource.read(key: latestVersionIgnore), null);
      expect(await dataSource.containsKey(key: latestVersionIgnore), false);
      expect(dataSource.memoryData.containsKey(latestVersionIgnore), false);
    });
  });

  group('test set/get Start Time to Wait After Card Activation', () {
    const String key =
        EvoSecureStorageHelperImpl.lastTimeRequest3DSCardActivationKey;
    final int timeToWait = 1619200;

    setUp(() async {
      await secureStorage.delete(key: key);
    });

    test('should set time to wait after card activation correct', () async {
      expect(await secureStorage.containsKey(key: key), false);
      await dataSource.setLastTimeRequest3DSCardActivation(timeToWait);

      expect(await secureStorage.containsKey(key: key), true);
      expect(dataSource.memoryData.containsKey(key), true);

      final String? savedTime = await secureStorage.read(key: key);
      expect(savedTime, timeToWait.toString());
      expect(dataSource.memoryData[key], timeToWait.toString());
    });

    test('should get time to wait after card activation correct', () async {
      expect(await secureStorage.containsKey(key: key), false);
      await dataSource.write(key: key, value: timeToWait.toString());
      expect(await secureStorage.containsKey(key: key), true);
      expect(dataSource.memoryData.containsKey(key), true);

      final int? savedTime =
          await dataSource.getLastTimeRequest3DSCardActivation();
      expect(savedTime, timeToWait);
      expect(dataSource.memoryData[key], timeToWait.toString());

      await dataSource.delete(key: key);
      expect(await secureStorage.containsKey(key: key), false);
      expect(await dataSource.read(key: key), null);
      expect(await dataSource.containsKey(key: key), false);
      expect(dataSource.memoryData.containsKey(key), false);
    });
  });
}
