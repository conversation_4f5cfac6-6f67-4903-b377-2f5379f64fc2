import 'dart:async';

import 'package:evoapp/data/constants.dart' as constants;
import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/feature/alice/alice_utils.dart';
import 'package:evoapp/feature/appsflyer/appsflyer_handler.dart';
import 'package:evoapp/feature/appsflyer/one_link_utils.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/feature/deep_link/deep_link_utils.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/logging/evo_navigator_observer.dart';
import 'package:evoapp/feature/term_and_condition/term_and_condition_overlay.dart';
import 'package:evoapp/feature/term_and_condition/term_and_condition_utils.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/resources/button_dimensions.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_action_handler.dart';
import 'package:evoapp/util/evo_authentication_helper.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper_impl.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/dio_http_client_impl.dart';
import 'package:flutter_common_package/feature/onesignal/onesignal.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_common_package/feature/server_logging/firebase_analytics.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/button_dimensions.dart';
import 'package:flutter_common_package/resources/button_styles.dart';
import 'package:flutter_common_package/resources/colors.dart';
import 'package:flutter_common_package/resources/text_styles.dart';
import 'package:flutter_common_package/util/clear_all_notifications_wrapper.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/util/local_storage_helper.dart';
import 'package:flutter_common_package/util/secure_storage_helper.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:ts_bio_detect_changed/ts_bio_detect_changed.dart';

import '../base/evo_page_state_base_test.dart';

Future<void> testExecutable(FutureOr<void> Function() testMain) async {
  setUp(() {
    //
  });

  tearDown(() {
    //
  });

  await testMain();
}

// FlutterSecureStorage
FlutterSecureStorage testFlutterSecureStorageExecutable({Map<String, String>? mockInitialValue}) {
  const FlutterSecureStorage secureStorage = FlutterSecureStorage();
  FlutterSecureStorage.setMockInitialValues(mockInitialValue ?? <String, String>{});

  return secureStorage;
}

// SecureDataSource
CommonSecureStorageHelperImpl testSecureDataSourceExecutable(
    {required FlutterSecureStorage secureStorage}) {
  final CommonSecureStorageHelperImpl dataSource =
      CommonSecureStorageHelperImpl(secureStorage: secureStorage);
  getIt.registerLazySingleton<CommonLocalStorageHelper>(() => dataSource);

  return dataSource;
}

// Dio
DioClientImpl testDioClientImplExecutable(
    {Map<String, String?> initValueHeader = const <String, String?>{
      constants.HeaderKey.authorization: 'Bearer init_access_token_value_test'
    }}) {
  final Dio dio = Dio(BaseOptions(headers: initValueHeader));
  final DioClientImpl dioClientImpl = DioClientImpl(dio);
  getIt.registerLazySingleton<CommonHttpClient>(() => dioClientImpl);

  return dioClientImpl;
}

class MockOneSignal extends Mock implements OneSignal {}

// OneSignal
MockOneSignal testOneSignalExecutable(
    {Future<void>? valueSetAppId, Future<Map<String, dynamic>>? valueRemoveExternalUserId}) {
  final MockOneSignal mockOneSignal = MockOneSignal();
  when(() => mockOneSignal.setAppId(any()))
      .thenAnswer((_) => valueSetAppId ?? Future<void>.value());
  when(() => mockOneSignal.removeExternalUserId()).thenAnswer((_) =>
      valueRemoveExternalUserId ??
      Future<Map<String, dynamic>>.value(<String, dynamic>{'success': true}));
  when(() => mockOneSignal.clearOneSignalNotifications()).thenAnswer((_) => Future<void>.value());
  return mockOneSignal;
}

// EvoSecureDataSource
EvoSecureStorageHelperImpl testEvoSecureStorageHelperExecutable(
    {required FlutterSecureStorage secureStorage}) {
  final EvoSecureStorageHelperImpl dataSource =
      EvoSecureStorageHelperImpl(secureStorage: secureStorage);
  getIt.registerLazySingleton<EvoLocalStorageHelper>(() => dataSource);

  return dataSource;
}

/// Biometric token module
BiometricsTokenModule testBiometricTokenModuleExecutable({
  required BiometricsAuthenticate bioAuth,
  required UserRepo userRepo,
  required TsBioDetectChanged bioDetectChanged,
  required EvoLocalStorageHelper storageHelper,
  required JwtHelper jwtHelper,
}) {
  final BiometricsTokenModule biometricsTokenModule = BiometricsTokenModule(
    biometricsAuthenticate: bioAuth,
    userRepo: userRepo,
    secureStorageHelper: storageHelper,
    bioDetectChanged: bioDetectChanged,
    jwtHelper: jwtHelper,
  );
  getIt.registerLazySingleton<BiometricsTokenModule>(() => biometricsTokenModule);
  return biometricsTokenModule;
}

class MockClearAllNotificationsWrapper extends Mock implements ClearAllNotificationsWrapper {}

// clear all notifications
MockClearAllNotificationsWrapper testClearAllNotificationExecutable() {
  final MockClearAllNotificationsWrapper mockClearAllNotificationsWrapper =
      MockClearAllNotificationsWrapper();

  getIt.registerLazySingleton<ClearAllNotificationsWrapper>(() => mockClearAllNotificationsWrapper);

  when(() => mockClearAllNotificationsWrapper.clear()).thenAnswer((_) => Future<void>.value());

  return mockClearAllNotificationsWrapper;
}

class MockGlobalKeyProvider extends Mock implements GlobalKeyProvider {}

void setUpMockGlobalKeyProvider(BuildContext mockNavigatorContext) {
  getIt.registerSingleton<GlobalKeyProvider>(MockGlobalKeyProvider());
  final GlobalKeyProvider globalKeyProvider = getIt.get<GlobalKeyProvider>();
  when(() => globalKeyProvider.navigatorContext).thenReturn(mockNavigatorContext);
  when(() => globalKeyProvider.navigatorKey).thenReturn(GlobalKey<NavigatorState>());
  when(() => globalKeyProvider.scaffoldMessengerKey)
      .thenReturn(GlobalKey<ScaffoldMessengerState>());
}

class MockEvoImageProvider extends Mock implements CommonImageProvider {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

void getItRegisterMockCommonUtilFunctionAndImageProvider() {
  getIt.registerLazySingleton<CommonUtilFunction>(() => MockCommonUtilFunction());
  getIt.registerLazySingleton<EvoUtilFunction>(() => MockEvoUtilFunction());
  getIt.registerLazySingleton<CommonImageProvider>(() => MockEvoImageProvider());
}

void setupMockImageProvider() {
  final CommonImageProvider mockCommonImageProvider = getIt.get<CommonImageProvider>();
  when(() => mockCommonImageProvider.network(
        any(),
        width: any(named: 'width'),
        height: any(named: 'height'),
        color: any(named: 'color'),
        fit: any(named: 'fit'),
        cornerRadius: any(named: 'cornerRadius'),
        placeholder: any(named: 'placeholder'),
        errorWidget: any(named: 'errorWidget'),
        onLoadError: any(named: 'onLoadError'),
      )).thenAnswer((_) => Container());

  when(() => mockCommonImageProvider.asset(
        any(),
        width: any(named: 'width'),
        height: any(named: 'height'),
        color: any(named: 'color'),
        fit: any(named: 'fit'),
        cornerRadius: any(named: 'cornerRadius'),
        cacheWidth: any(named: 'cacheWidth'),
        cacheHeight: any(named: 'cacheHeight'),
        package: any(named: 'package'),
      )).thenAnswer((_) => Container());
}

void getItUnRegisterMockCommonUtilFunctionAndImageProvider() {
  getIt.unregister<CommonUtilFunction>();
  getIt.unregister<EvoUtilFunction>();
  getIt.unregister<CommonImageProvider>();
}

void getItRegisterColor() {
  getIt.registerLazySingleton<CommonColors>(() => EvoColors());
  getIt.registerLazySingleton<EvoColors>(() => EvoColors());
}

void getItRegisterColorV2() {
  getIt.registerLazySingleton<CommonColors>(() => EvoColorsV2());
  getIt.registerLazySingleton<EvoColors>(() => EvoColorsV2());
  getIt.registerLazySingleton<EvoColorsV2>(() => EvoColorsV2());
}

void initConfigChangeScreenSize(WidgetTester widgetTester, {required Size size}) {
  widgetTester.view.physicalSize = size;
  widgetTester.view.devicePixelRatio = 1;
}

void resetConfigChangeScreenSize(WidgetTester widgetTester) {
  // resets the screen to its original size after the test end
  addTearDown(widgetTester.view.resetPhysicalSize);
  addTearDown(widgetTester.view.resetDevicePixelRatio);
}

void getItUnregisterColor() {
  getIt.unregister<CommonColors>();
  getIt.unregister<EvoColors>();
}

void getItUnregisterColorV2() {
  getIt.unregister<CommonColors>();
  getIt.unregister<EvoColors>();
  getIt.unregister<EvoColorsV2>();
}

void getItRegisterTextStyle() {
  getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
  getIt.registerLazySingleton<EvoTextStyles>(() => EvoTextStyles());
}

void getItRegisterTextStyleV2() {
  getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStylesV2());
  getIt.registerLazySingleton<EvoTextStyles>(() => EvoTextStylesV2());
  getIt.registerLazySingleton<EvoTextStylesV2>(() => EvoTextStylesV2());
}

void getItUnRegisterTextStyle() {
  getIt.unregister<CommonTextStyles>();
  getIt.unregister<EvoTextStyles>();
}

void getItUnRegisterTextStyleV2() {
  getIt.unregister<CommonTextStyles>();
  getIt.unregister<EvoTextStyles>();
  getIt.unregister<EvoTextStylesV2>();
}

void getItRegisterButtonStyle() {
  getIt.registerLazySingleton<CommonButtonStyles>(() => CommonButtonStyles());
  getIt.registerLazySingleton<CommonButtonDimensions>(() => EvoButtonDimensions());
}

void getItUnRegisterButtonStyle() {
  getIt.unregister<CommonButtonStyles>();
  getIt.unregister<CommonButtonDimensions>();
}

class MockFirebaseAnalytics extends Mock implements FirebaseAnalytics {}

class MockFirebaseAnalyticsObserver extends Mock implements FirebaseAnalyticsObserver {}

void setupFirebaseForTest() {
  if (getIt.isRegistered<FirebaseAnalyticsWrapper>()) {
    getIt.unregister<FirebaseAnalyticsWrapper>();
  }
  final MockFirebaseAnalytics mockFirebaseAnalytics = MockFirebaseAnalytics();
  final MockFirebaseAnalyticsObserver mockFirebaseAnalyticsObserver =
      MockFirebaseAnalyticsObserver();
  when(() => mockFirebaseAnalytics.logEvent(
        name: any(named: 'name'),
        parameters: any(named: 'parameters'),
      )).thenAnswer((_) async {});
  when(() => mockFirebaseAnalyticsObserver.analytics).thenReturn(mockFirebaseAnalytics);

  getIt.registerSingleton<FirebaseAnalyticsWrapper>(FirebaseAnalyticsWrapper(
    mockFirebaseAnalytics,
    mockFirebaseAnalyticsObserver,
  ));
}

class MockAppsflyerHandler extends Mock implements AppsflyerHandler {}

void setupAppsflyerForTest() {
  if (getIt.isRegistered<AppsflyerHandler>()) {
    getIt.unregister<AppsflyerHandler>();
  }
  final MockAppsflyerHandler mockAppsflyerHandler = MockAppsflyerHandler();
  getIt.registerSingleton<AppsflyerHandler>(mockAppsflyerHandler);

  when(() => mockAppsflyerHandler.logEvent(
      eventName: any(named: 'eventName'),
      eventValues: any(named: 'eventValues'))).thenAnswer((_) => Future<bool>.value(true));
}

class MockCommonObserver extends Mock implements CommonNavigatorObserver {}

void setupNavigatorObserversForTest() {
  if (getIt.isRegistered<CommonNavigatorObserver>()) {
    getIt.unregister<CommonNavigatorObserver>();
  }
  final MockCommonObserver mockCommonObserver = MockCommonObserver();
  getIt.registerLazySingleton<CommonNavigatorObserver>(() => mockCommonObserver);

  setupFirebaseForTest();
}

void getItRegisterNavigatorObserver() {
  if (getIt.isRegistered<EvoNavigatorObserver>()) {
    getIt.unregister<EvoNavigatorObserver>();
  }
  getIt.registerLazySingleton<EvoNavigatorObserver>(() => EvoNavigatorObserver());

  setupNavigatorObserversForTest();
}

void getItUnRegisterNavigatorObserver() {
  getIt.unregister<EvoNavigatorObserver>();
}

class MockEvoAuthenticationHelper extends Mock implements EvoAuthenticationHelper {}

class MockEvoUiUtils extends Mock implements EvoUiUtils {}

class MockEvoDialogHelper extends Mock implements EvoDialogHelper {}

class MockEvoActionHandler extends Mock implements EvoActionHandler {}

class MockTermAndConditionUtils extends Mock implements TermAndConditionUtils {}

class MockAliceUtils extends Mock implements AliceUtils {}

class MockTermAndConditionOverlay extends Mock implements TermAndConditionOverlay {}

void setUtilsMockInstanceForTesting() {
  EvoAuthenticationHelper.setInstanceForTesting(MockEvoAuthenticationHelper());
  EvoUiUtils.setInstanceForTesting(MockEvoUiUtils());
  EvoDialogHelper.setInstanceForTesting(MockEvoDialogHelper());
  EvoActionHandler.setInstanceForTesting(MockEvoActionHandler());
}

void resetUtilMockToOriginalInstance() {
  EvoAuthenticationHelper.resetToOriginalInstance();
  EvoUiUtils.resetToOriginalInstance();
  EvoDialogHelper.resetToOriginalInstance();
  EvoActionHandler.resetToOriginalInstance();
}

class MockDeepLinkUtils extends Mock implements DeepLinkUtils {}

class MockOneLinkUtils extends Mock implements OneLinkUtils {}

void setUpOneLinkDeepLinkRegExForTest() {
  if (getIt.isRegistered<DeepLinkUtils>()) {
    getIt.unregister<DeepLinkUtils>();
  }
  if (getIt.isRegistered<OneLinkUtils>()) {
    getIt.unregister<OneLinkUtils>();
  }

  final MockDeepLinkUtils mockDeepLinkUtils = MockDeepLinkUtils();
  getIt.registerSingleton<DeepLinkUtils>(mockDeepLinkUtils);

  final MockOneLinkUtils mockOneLinkUtils = MockOneLinkUtils();
  getIt.registerSingleton<OneLinkUtils>(mockOneLinkUtils);

  when(() => deepLinkUtils.getRegExpOfDeeplink())
      .thenReturn(RegExp(r'^evoappvn://mobile/deeplinking(?:/[\w-]*)?(?:\?.*)?$'));

  when(() => oneLinkUtils.getRegExpOfOneLink())
      .thenReturn(RegExp(r'^https://(www\.)?evoappvn-stag\.onelink\.me(?:/.*)?(?:\?.*)?$'));
}

void setUpMockSnackBarForTest() {
  if (getIt.isRegistered<EvoSnackBar>()) {
    getIt.unregister<EvoSnackBar>();
  }
  final EvoSnackBar mockSnackBar = MockEvoSnackBar();
  getIt.registerSingleton<EvoSnackBar>(mockSnackBar);
  registerFallbackValue(SnackBarType.warning);
  when(() => mockSnackBar.show(
        any(),
        typeSnackBar: any(named: 'typeSnackBar'),
        durationInSec: any(named: 'durationInSec'),
        description: any(named: 'description'),
        marginBottomRatio: any(named: 'marginBottomRatio'),
      )).thenAnswer((_) async {
    return Future<bool?>.value();
  });
}

Future<BaseResponse> getMockBaseResponse(String mockFileName,
    [int statusCode = CommonHttpClient.SUCCESS]) async {
  //unregister the mock instance and re register a real instance of CommonUtilFunction
  if (getIt.isRegistered<CommonUtilFunction>()) {
    getIt.unregister<CommonUtilFunction>();
  }
  getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());
  final Map<String, dynamic>? jsonData =
      await getIt.get<CommonUtilFunction>().getJson(mockFileName);
  return BaseResponse(
    statusCode: statusCode,
    response: jsonData,
  );
}

void setupMockDialogHelper() {
  EvoDialogHelper.setInstanceForTesting(MockEvoDialogHelper());
  registerFallbackValue(EvoDialogId.expiredOrderBottomSheet);
  when(() => EvoDialogHelper().showDialogBottomSheet(
        header: any(named: 'header'),
        title: any(named: 'title'),
        titleTextStyle: any(named: 'titleTextStyle'),
        dialogId: any(named: 'dialogId'),
        isShowButtonClose: any(named: 'isShowButtonClose'),
        onClickClose: any(named: 'onClickClose'),
        textPositive: any(named: 'textPositive'),
        titleTextAlign: any(named: 'titleTextAlign'),
        contentSpacing: any(named: 'contentSpacing'),
        positiveButtonStyle: any(named: 'positiveButtonStyle'),
        contentTextStyle: any(named: 'contentTextStyle'),
        footer: any(named: 'footer'),
        onClickPositive: any(named: 'onClickPositive'),
        isDismissible: any(named: 'isDismissible'),
        content: any(named: 'content'),
        textNegative: any(named: 'textNegative'),
        buttonListOrientation: any(named: 'buttonListOrientation'),
        negativeButtonStyle: any(named: 'negativeButtonStyle'),
        onClickNegative: any(named: 'onClickNegative'),
      )).thenAnswer((_) async {});

  when(() => EvoDialogHelper().showDialogConfirm(
        title: any(named: 'title'),
        titleTextStyle: any(named: 'titleTextStyle'),
        dialogId: any(named: 'dialogId'),
        isShowButtonClose: any(named: 'isShowButtonClose'),
        textPositive: any(named: 'textPositive'),
        titleTextAlign: any(named: 'titleTextAlign'),
        positiveButtonStyle: any(named: 'positiveButtonStyle'),
        contentTextStyle: any(named: 'contentTextStyle'),
        footer: any(named: 'footer'),
        onClickPositive: any(named: 'onClickPositive'),
        isDismissible: any(named: 'isDismissible'),
        content: any(named: 'content'),
        textNegative: any(named: 'textNegative'),
        buttonListOrientation: any(named: 'buttonListOrientation'),
        negativeButtonStyle: any(named: 'negativeButtonStyle'),
        onClickNegative: any(named: 'onClickNegative'),
        contentTextAlign: any(named: 'contentTextAlign'),
        imageHeader: any(named: 'imageHeader'),
        loggingEventMetaData: any(named: 'loggingEventMetaData'),
        loggingEventOnShowMetaData: any(named: 'loggingEventOnShowMetaData'),
      )).thenAnswer((_) async {});
}

void setupCubit<T>(CommonCubit<dynamic> cubit, Object initialState) {
  final StreamController<T> controller = StreamController<T>.broadcast();
  when(() => cubit.stream).thenAnswer((_) => controller.stream);
  when(() => cubit.state).thenReturn(initialState);
  when(() => cubit.close()).thenAnswer((_) async {
    await controller.close();
  });
}

class MockFeatureToggle extends Mock implements FeatureToggle {}

void getItRegisterFeatureToggle() {
  if (!getIt.isRegistered<FeatureToggle>()) {
    getIt.registerLazySingleton<FeatureToggle>(() => MockFeatureToggle());
  }
}

void getItUnRegisterFeatureToggle() {
  getIt.unregister<FeatureToggle>();
}
