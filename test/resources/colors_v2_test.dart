// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:evoapp/resources/colors_v2.dart';

void main() {
  late EvoColorsV2 colors;

  setUp(() {
    colors = EvoColorsV2();
  });

  group('EvoColorsV2 Base Colors', () {
    test('should have correct primary color values', () {
      // Test all primary colors
      expect(colors.primary1, const Color(0xFFFAFEFA));
      expect(colors.primary2, const Color(0xFFF4FBF5));
      expect(colors.primary3, const Color(0xFFE6F8E7));
      expect(colors.primary4, const Color(0xFFD6F2D8));
      expect(colors.primary5, const Color(0xFFC3EBC6));
      expect(colors.primary6, const Color(0xFFAAE1AF));
      expect(colors.primary7, const Color(0xFF88D290));
      expect(colors.primary8, const Color(0xFF52BE64));
      expect(colors.primary9, const Color(0xFF059937));
      expect(colors.primary10, const Color(0xFF0B8A32));
      expect(colors.primary11, const Color(0xFF00822A));
      expect(colors.primary12, const Color(0xFF1A3E1F));
    });

    test('should have correct neutral color values', () {
      // Test all neutral colors
      expect(colors.neutral1, const Color(0xFFFCFDFC));
      expect(colors.neutral2, const Color(0xFFF9FBF9));
      expect(colors.neutral3, const Color(0xFFF1F3F1));
      expect(colors.neutral4, const Color(0xFFEAECEA));
      expect(colors.neutral5, const Color(0xFFE2E4E2));
      expect(colors.neutral6, const Color(0xFFDADDDA));
      expect(colors.neutral7, const Color(0xFFD3D5D2));
      expect(colors.neutral8, const Color(0xFFB9BCB8));
      expect(colors.neutral9, const Color(0xFF888D86));
      expect(colors.neutral10, const Color(0xFF7B7F7A));
      expect(colors.neutral11, const Color(0xFF5F645E));
      expect(colors.neutral12, const Color(0xFF1D211C));
    });

    test('should have correct neutral alpha color values', () {
      // Test all neutral alpha colors
      expect(colors.neutralAlpha1, const Color.fromRGBO(0, 0, 0, 1.2 / 100));
      expect(colors.neutralAlpha2, const Color.fromRGBO(0, 0, 0, 2.4 / 100));
      expect(colors.neutralAlpha3, const Color.fromRGBO(0, 0, 0, 5.5 / 100));
      expect(colors.neutralAlpha4, const Color.fromRGBO(0, 0, 0, 7.8 / 100));
      expect(colors.neutralAlpha5, const Color.fromRGBO(0, 0, 0, 10.6 / 100));
      expect(colors.neutralAlpha6, const Color.fromRGBO(0, 0, 0, 13.3 / 100));
      expect(colors.neutralAlpha7, const Color.fromRGBO(0, 0, 0, 16.9 / 100));
      expect(colors.neutralAlpha8, const Color.fromRGBO(0, 0, 0, 26.7 / 100));
      expect(colors.neutralAlpha9, const Color.fromRGBO(0, 0, 0, 44.7 / 100));
      expect(colors.neutralAlpha10, const Color.fromRGBO(0, 0, 0, 49.8 / 100));
      expect(colors.neutralAlpha11, const Color.fromRGBO(0, 0, 0, 60.8 / 100));
      expect(colors.neutralAlpha12, const Color.fromRGBO(0, 0, 0, 87.5 / 100));
    });
  });

  group('EvoColorsV2 Semantic Background Colors', () {
    test('should have correct background neutral colors', () {
      expect(colors.backgroundNeutralPage, colors.neutral1);
      expect(colors.backgroundNeutralBackground, colors.neutral12);
      expect(colors.backgroundNeutralContainer, colors.neutral3);
      expect(colors.backgroundNeutralElement, colors.neutral4);
      expect(colors.backgroundNeutralDisable, colors.neutralAlpha2);
      expect(colors.backgroundNeutralFlat, colors.neutralAlpha9);
      expect(colors.backgroundNeutralLoading, colors.neutralAlpha1);
    });

    test('should have correct background primary colors', () {
      expect(colors.backgroundPrimaryElement, colors.primary9);
      expect(colors.backgroundPrimaryElement2, colors.primary3);
      expect(colors.backgroundPrimaryBackground, colors.primary2);
      expect(colors.backgroundPrimaryContainer, colors.primary2);
    });

    test('should have correct background status success colors', () {
      expect(colors.backgroundStatusSuccessElement, colors.neutral1);
      expect(colors.backgroundStatusSuccessBackground, const Color(0xFF2B9A66));
    });

    test('should have correct background status warning colors', () {
      expect(colors.backgroundStatusWarningElement, const Color(0xFFFFBA1A));
      expect(colors.backgroundStatusWarningBackground, const Color(0xFFFFF7C2));
    });

    test('should have correct background status fail colors', () {
      expect(colors.backgroundStatusFailElement, const Color(0xFFE54D2E));
      expect(colors.backgroundStatusFailBackground, const Color(0xFFFEEBE7));
    });

    test('should have correct background status info colors', () {
      expect(colors.backgroundStatusInfoElement, const Color(0xFF007AFF));
      expect(colors.backgroundStatusInfoBackground, const Color(0xFFF0F1FE));
    });
  });

  group('EvoColorsV2 Border Colors', () {
    test('should have correct border colors', () {
      expect(colors.borderPrimary, colors.primary9);
      expect(colors.borderSubtle, colors.neutral4);
      expect(colors.borderLine, colors.neutral6);
      expect(colors.borderContainer, colors.neutral8);
      expect(colors.borderActive, colors.neutral12);
    });
  });

  group('EvoColorsV2 Text Colors', () {
    test('should have correct text colors', () {
      expect(colors.textPrimary, colors.primary9);
      expect(colors.textWhite, colors.neutral1);
      expect(colors.textTitle, colors.neutral12);
      expect(colors.textLabel, colors.neutral12);
      expect(colors.textSubtitle, colors.neutral11);
      expect(colors.textBody, colors.neutral11);
      expect(colors.textDisable, colors.neutral8);
    });
  });

  group('EvoColorsV2 Icon Colors', () {
    test('should have correct icon colors', () {
      expect(colors.iconGreenPrimary, colors.primary9);
      expect(colors.iconBlackPrimary, colors.neutral12);
      expect(colors.iconWhitePrimary, colors.neutral1);
      expect(colors.iconSecondary, colors.neutral10);
      expect(colors.iconDisable, colors.neutral10);
    });
  });

  group('EvoColorsV2 Status Colors', () {
    test('should have correct status colors', () {
      expect(colors.statusDanger, const Color(0xFFE54D2E));
      expect(colors.statusWarning, const Color(0xFFFFBA1A));
      expect(colors.statusInfo, const Color(0xFF007AFF));
      expect(colors.statusSuccess, const Color(0xFF30A66D));
    });
  });

  group('EvoColorsV2 Button Colors', () {
    test('should have correct button accent default colors', () {
      expect(colors.buttonAccentDefaultText, colors.neutral1);
      expect(colors.buttonAccentDefaultIcon, colors.neutral1);
      expect(colors.buttonAccentDefaultBackground, colors.neutral12);
    });

    test('should have correct button accent disable colors', () {
      expect(colors.buttonAccentDisableText, colors.neutral8);
      expect(colors.buttonAccentDisableIcon, colors.neutral8);
      expect(colors.buttonAccentDisableBackground, colors.neutralAlpha3);
    });

    test('should have correct button secondary default colors', () {
      expect(colors.buttonSecondaryDefaultText, colors.neutral12);
      expect(colors.buttonSecondaryDefaultIcon, colors.neutral12);
      expect(colors.buttonSecondaryDefaultBackground, colors.neutral4);
    });

    test('should have correct button secondary disable colors', () {
      expect(colors.buttonSecondaryDisableText, colors.neutral8);
      expect(colors.buttonSecondaryDisableIcon, colors.neutral8);
      expect(colors.buttonSecondaryDisableBackground, colors.neutralAlpha3);
    });

    test('should have correct button chip default colors', () {
      expect(colors.buttonChipDefaultText, colors.primary9);
      expect(colors.buttonChipDefaultBorder, colors.primary9);
    });

    test('should have correct button chip disable colors', () {
      expect(colors.buttonChipDisableText, colors.neutral8);
      expect(colors.buttonChipDisableBorder, colors.neutral8);
    });

    test('should have correct button link text default colors', () {
      expect(colors.buttonLinkTextDefaultText, colors.primary9);
      expect(colors.buttonLinkTextDefaultIcon, colors.primary9);
    });

    test('should have correct button link text disable colors', () {
      expect(colors.buttonLinkTextDisableText, colors.neutral8);
      expect(colors.buttonLinkTextDisableIcon, colors.neutral8);
    });
  });

  group('EvoColorsV2 Input and Checkbox Colors', () {
    test('should have correct input icon colors', () {
      expect(colors.inputIconDefault, colors.neutral12);
      expect(colors.inputIconDisable, colors.neutral7);
    });

    test('should have correct checkbox and radio colors', () {
      expect(colors.checkBoxRadioDefault, colors.neutral12);
      expect(colors.checkBoxRadioDisable, colors.neutral8);
    });
  });

  group('EvoColorsV2 Common Colors Interface', () {
    test('should implement Common Colors interface correctly', () {
      expect(colors.primary, colors.primary9);
      expect(colors.foreground, colors.neutral12);
      expect(colors.background, colors.backgroundNeutralPage);
      expect(colors.error, colors.statusDanger);
      expect(colors.highlighted, colors.primary);
      expect(colors.appBarShadow, colors.transparent);
      expect(colors.iconColor, colors.iconBlackPrimary);
      expect(colors.loadingViewColor, colors.primary);
    });
  });
}
