// Copyright (c) 2025 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/resources/evo_pin_code_theme.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/evo_pin_code/evo_pin_code_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/pin_code/common_pin_code_field_shape.dart';
import 'package:flutter_common_package/widget/pin_code/common_pin_theme.dart';
import 'package:flutter_test/flutter_test.dart';


import '../util/flutter_test_config.dart';

void main() {

  setUpAll(() {
    getItRegisterColorV2();
    getItRegisterTextStyleV2();
  });

  group('EvoPinCodeTheme', () {
    test('buildDefaultPinCodeTheme returns correct theme when hasError is false', () {
      final CommonPinTheme theme = EvoPinCodeTheme.buildDefaultPinCodeTheme();

      expect(theme, isA<CommonPinTheme>());
      expect(theme.fieldHeight, equals(EvoPinCodeConfig.defaultPinCodeFieldHeightV2));
      expect(theme.selectedColor, equals(evoColorsV2.borderActive));
      expect(theme.inactiveColor, equals(evoColorsV2.borderContainer));
      expect(theme.activeColor, equals(evoColorsV2.borderLine));
      expect(theme.shape, equals(CommonPinCodeFieldShape.box));
      expect(
        theme.borderRadius,
        equals(BorderRadius.circular(8)),
      );
      expect(theme.activeFillColor, equals(evoColorsV2.backgroundNeutralContainer));
      expect(theme.inactiveFillColor, equals(evoColorsV2.backgroundNeutralContainer));
      expect(theme.selectedFillColor, equals(evoColorsV2.backgroundNeutralContainer));
    });

    test('buildDefaultPinCodeTheme returns correct theme when hasError is true', () {
      final CommonPinTheme theme = EvoPinCodeTheme.buildDefaultPinCodeTheme(hasError: true);

      expect(theme, isA<CommonPinTheme>());
      expect(theme.fieldHeight, equals(EvoPinCodeConfig.defaultPinCodeFieldHeightV2));
      expect(theme.selectedColor, equals(evoColorsV2.borderActive));
      expect(theme.inactiveColor, equals(evoColorsV2.statusDanger));
      expect(theme.activeColor, equals(evoColorsV2.statusDanger));
      expect(theme.shape, equals(CommonPinCodeFieldShape.box));
      expect(
        theme.borderRadius,
        equals(BorderRadius.circular(8)),
      );
      expect(theme.activeFillColor, equals(evoColorsV2.backgroundNeutralContainer));
      expect(theme.inactiveFillColor, equals(evoColorsV2.backgroundNeutralContainer));
      expect(theme.selectedFillColor, equals(evoColorsV2.backgroundNeutralContainer));
    });

    test('buildDefaultPinCodeTheme returns theme with correct border radius', () {
      final CommonPinTheme theme = EvoPinCodeTheme.buildDefaultPinCodeTheme();

      // Testing border radius separately as it requires special handling for comparison
      expect(
        theme.borderRadius.toString(),
        equals(BorderRadius.circular(8).toString()),
      );
    });
  });
}
