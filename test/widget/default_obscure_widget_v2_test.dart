// test/widget/default_obscure_widget_v2_test.dart
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/default_obscure_widget_v2.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';

void main() {

  setUpAll(() {
    getIt.registerLazySingleton<EvoColorsV2>(() => EvoColorsV2());
  });

  testWidgets('renders circular container with correct color and size', (WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(body: DefaultObscureWidgetV2()),
      ),
    );

    final Container container = tester.widget<Container>(find.byType(Container));
    final BoxDecoration decoration = container.decoration as BoxDecoration;

    expect(decoration.shape, BoxShape.circle);
    expect(decoration.color, evoColorsV2.iconBlackPrimary);
  });
}