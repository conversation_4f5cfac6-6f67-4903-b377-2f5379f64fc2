import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../feature/activated_pos_limit/widgets/active_card_or_pos_limit_guide_widget_test.dart';
import '../util/flutter_test_config.dart';
import 'package:evoapp/widget/evo_checkbox_widget.dart';

void main() {
  setUpAll(() {
    registerFallbackValue(BoxFit.contain);
    getItRegisterColorV2();
    getItRegisterTextStyleV2();
    getIt.registerLazySingleton<CommonImageProvider>(() => MockCommonImageProvider());
    setupMockImageProvider();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('EvoCheckboxWidget', () {
    testWidgets('should render enabled and checked checkbox without title', (WidgetTester tester) async {
      // Arrange
      final bool value = true;
      bool onTapCalled = false;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoCheckboxWidget(
              value: value,
              onTap: () {
                onTapCalled = true;
              },
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(EvoCheckboxWidget), findsOneWidget);
      expect(find.byType(InkWell), findsOneWidget);
      expect(find.text(''), findsNothing); // No title

      // Verify checkbox is checked (has check icon)
      expect(find.byType(Container), findsWidgets);

      // Test interaction
      await tester.tap(find.byType(EvoCheckboxWidget));
      await tester.pumpAndSettle();
      expect(onTapCalled, isTrue);
    });

    testWidgets('should render enabled and unchecked checkbox with title', (WidgetTester tester) async {
      // Arrange
      final bool value = false;
      const String title = 'Test Checkbox';
      bool onTapCalled = false;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoCheckboxWidget(
              value: value,
              title: title,
              onTap: () {
                onTapCalled = true;
              },
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(EvoCheckboxWidget), findsOneWidget);
      expect(find.text(title), findsOneWidget);

      // Verify checkbox is unchecked (no check icon)
      // The SizedBox is used when unchecked

      // Test interaction
      await tester.tap(find.byType(EvoCheckboxWidget));
      await tester.pumpAndSettle();
      expect(onTapCalled, isTrue);
    });

    testWidgets('should render disabled and checked checkbox', (WidgetTester tester) async {
      // Arrange
      final bool value = true;
      bool onTapCalled = false;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoCheckboxWidget(
              value: value,
              enable: false,
              onTap: () {
                onTapCalled = true;
              },
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(EvoCheckboxWidget), findsOneWidget);

      // Disabled checkbox should have different style
      // But we can't easily test colors in widget tests

      // Test that onTap is not triggered when disabled
      await tester.tap(find.byType(EvoCheckboxWidget));
      await tester.pumpAndSettle();
      expect(onTapCalled, isFalse);
    });

    testWidgets('should render disabled and unchecked checkbox with title', (WidgetTester tester) async {
      // Arrange
      final bool value = false;
      const String title = 'Disabled Checkbox';
      bool onTapCalled = false;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoCheckboxWidget(
              value: value,
              title: title,
              enable: false,
              onTap: () {
                onTapCalled = true;
              },
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(EvoCheckboxWidget), findsOneWidget);
      expect(find.text(title), findsOneWidget);

      // Test that onTap is not triggered when disabled
      await tester.tap(find.byType(EvoCheckboxWidget));
      await tester.pumpAndSettle();
      expect(onTapCalled, isFalse);
    });

    testWidgets('should update when value changes', (WidgetTester tester) async {
      // Arrange
      bool checkboxValue = false;

      // Create a stateful wrapper to control the checkbox
      Widget buildTestWidget({required bool value}) {
        return MaterialApp(
          home: Scaffold(
            body: EvoCheckboxWidget(
              value: value,
              onTap: () {
                checkboxValue = !checkboxValue;
              },
            ),
          ),
        );
      }

      // Act - Start with unchecked
      await tester.pumpWidget(buildTestWidget(value: checkboxValue));

      // Initial state - unchecked
      expect(find.byType(EvoCheckboxWidget), findsOneWidget);

      // Tap to check
      await tester.tap(find.byType(EvoCheckboxWidget));
      expect(checkboxValue, isTrue); // Value should have changed

      // Rebuild with new value
      await tester.pumpWidget(buildTestWidget(value: checkboxValue));
      await tester.pumpAndSettle();

      // Now checkbox should be checked
      // The actual visual verification isn't directly possible in widget tests
    });
  });
}
