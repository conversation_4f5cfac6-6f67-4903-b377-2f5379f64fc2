// Copyright (c) 2025 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/widget/evo_radio_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_common_package/init_common_package.dart';

import '../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterColorV2();
    getItRegisterTextStyleV2();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('EvoRadioWidget', () {
    testWidgets('should render enabled and unselected radio button', (WidgetTester tester) async {
      // Arrange
      String? selectedValue;
      const String radioValue = 'option2';
      const String radioTitle = 'Option 2';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoRadioWidget<String>(
              title: radioTitle,
              value: radioValue,
              onChange: (String value) {
                selectedValue = value;
              },
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(EvoRadioWidget<String>), findsOneWidget);
      expect(find.text(radioTitle), findsOneWidget);

      // Test interaction
      await tester.tap(find.byType(EvoRadioWidget<String>));
      await tester.pumpAndSettle();
      expect(selectedValue, equals(radioValue));
    });

    testWidgets('should render disabled and selected radio button', (WidgetTester tester) async {
      // Arrange
      String? selectedValue;
      const String radioValue = 'option3';
      const String radioTitle = 'Option 3';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoRadioWidget<String>(
              title: radioTitle,
              value: radioValue,
              isSelected: true,
              enable: false,
              onChange: (String value) {
                selectedValue = value;
              },
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(EvoRadioWidget<String>), findsOneWidget);
      expect(find.text(radioTitle), findsOneWidget);

      // Test that onChange is not triggered when disabled
      await tester.tap(find.byType(EvoRadioWidget<String>));
      await tester.pumpAndSettle();
      expect(selectedValue, isNull);
    });

    testWidgets('should work with int values', (WidgetTester tester) async {
      // Arrange
      int? selectedValue;
      const int radioValue = 42;
      const String radioTitle = 'Number Option';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoRadioWidget<int>(
              title: radioTitle,
              value: radioValue,
              onChange: (int value) {
                selectedValue = value;
              },
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(EvoRadioWidget<int>), findsOneWidget);
      expect(find.text(radioTitle), findsOneWidget);

      // Test interaction with int value
      await tester.tap(find.byType(EvoRadioWidget<int>));
      await tester.pumpAndSettle();
      expect(selectedValue, equals(radioValue));
    });

    testWidgets('should handle multiple radio buttons in a group', (WidgetTester tester) async {
      // Arrange
      String selectedValue = 'option1';
      const String option1 = 'option1';
      const String option2 = 'option2';

      // Create a stateful wrapper to control the radio group
      Widget buildTestWidget() {
        return MaterialApp(
          home: Scaffold(
            body: Column(
              children: <Widget>[
                EvoRadioWidget<String>(
                  title: 'Option 1',
                  value: option1,
                  isSelected: selectedValue == option1,
                  onChange: (String value) {
                    selectedValue = value;
                  },
                ),
                EvoRadioWidget<String>(
                  title: 'Option 2',
                  value: option2,
                  isSelected: selectedValue == option2,
                  onChange: (String value) {
                    selectedValue = value;
                  },
                ),
              ],
            ),
          ),
        );
      }

      // Act - Start with option1 selected
      await tester.pumpWidget(buildTestWidget());

      // Initial state assertion
      expect(find.byType(EvoRadioWidget<String>), findsNWidgets(2));

      // Find the second radio button and tap it
      await tester.tap(find.text('Option 2'));
      expect(selectedValue, equals(option2));
    });
  });
}
