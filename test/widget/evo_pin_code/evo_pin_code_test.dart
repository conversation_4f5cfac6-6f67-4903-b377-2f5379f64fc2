import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/default_obscure_widget_v2.dart';
import 'package:evoapp/widget/evo_pin_code/evo_pin_code.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_common_package/widget/pin_code/pin_code_widget.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

class MockCallback extends Mock {
  void call(String value);
}

class MockCallbackWithoutParam extends Mock {
  void call();
}

void main() {
  const Widget obscuringWidgetExpect = DefaultObscureWidgetV2();
  const String errorTextExpect = 'error message';
  const String resetPinTextExpect = 'reset pin';

  late CommonImageProvider commonImageProvider;
  late MockCallback mockOnChange;
  late MockCallback mockOnSubmit;
  late MockCallbackWithoutParam mockOnResetPin;
  late TextStyle errorStyleExpect;
  late TextStyle resetPinStyleExpect;

  setUpAll(() {
    getItRegisterColorV2();
    getItRegisterTextStyleV2();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getIt.registerLazySingleton<FeatureToggle>(() => MockFeatureToggle());

    commonImageProvider = getIt<CommonImageProvider>();
    mockOnChange = MockCallback();
    mockOnSubmit = MockCallback();
    mockOnResetPin = MockCallbackWithoutParam();
    errorStyleExpect = evoTextStyles.h300(color: evoColors.background);
    resetPinStyleExpect = evoTextStyles.h400(color: evoColors.primary);
    when(() => commonImageProvider.asset(any())).thenReturn(Container());
    when(() => getIt<FeatureToggle>().enableRevampUiFeature).thenReturn(true);
  });

  group('test [EvoPinCodeWidget]', () {
    testWidgets('verify with default value', (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoPinCodeWidget(
              textEditingController: TextEditingController(),
            ),
          ),
        ),
      );

      // verify CommonPinCode
      final Finder commonPinCodeFinder = find.byType(CommonPinCode);
      expect(commonPinCodeFinder, findsOneWidget);

      // verify text error message
      final Finder textErrorFinder = find.byWidgetPredicate((Widget widget) {
        if (widget is Text) {
          return widget.data?.isEmpty == true &&
              widget.style == evoTextStyles.bodySmall(color: evoColors.error);
        }
        return false;
      });
      expect(textErrorFinder, findsOneWidget);

      // verify text reset pin
      final Finder textResetPinFinder = find.byWidgetPredicate((Widget widget) {
        if (widget is Text) {
          return widget.data?.isEmpty == true &&
              widget.style == evoTextStyles.bodyLarge(evoColors.textPassive);
        }
        return false;
      });
      expect(textResetPinFinder, findsOneWidget);

      // verify inkwell change show obscure text
      final Finder inkWellFinder = find.byType(InkWell);
      expect(inkWellFinder, findsOneWidget);

      final Finder evoPinCodeWidgetFinder = find.byType(EvoPinCodeWidget);
      final EvoPinCodeWidgetState evoPinCodeWidgetState =
          widgetTester.state(evoPinCodeWidgetFinder);
      expect(evoPinCodeWidgetState.showObscureText, isTrue);

      verify(() => commonImageProvider.asset(EvoImages.icShowOffPin)).called(1);

      await widgetTester.tap(inkWellFinder);
      await widgetTester.pump(Duration(milliseconds: 100));
      verify(() => commonImageProvider.asset(EvoImages.icShowOnPin)).called(1);
      expect(evoPinCodeWidgetState.showObscureText, isFalse);
    });

    testWidgets('verify with custom value', (WidgetTester widgetTester) async {
      when(() => getIt<FeatureToggle>().enableRevampUiFeature).thenReturn(false);
      await widgetTester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoPinCodeWidget(
              textEditingController: TextEditingController(),
              onChange: mockOnChange.call,
              onResetPin: mockOnResetPin.call,
              onSubmit: mockOnSubmit.call,
              obscuringWidget: obscuringWidgetExpect,
              errorText: errorTextExpect,
              resetPinText: resetPinTextExpect,
              errorStyle: errorStyleExpect,
              resetPinStyle: resetPinStyleExpect,
            ),
          ),
        ),
      );

      // verify CommonPinCode
      final Finder commonPinCodeFinder = find.byType(CommonPinCode);
      expect(commonPinCodeFinder, findsOneWidget);

      final CommonPinCode commonPinCode = widgetTester.widget(commonPinCodeFinder);
      expect(commonPinCode.obscuringWidget, isA<DefaultObscureWidgetV2>());

      // verify onChange
      await widgetTester.enterText(commonPinCodeFinder, '1');
      await widgetTester.enterText(commonPinCodeFinder, '2');

      verify(() => mockOnChange.call('1')).called(1);
      verify(() => mockOnChange.call('2')).called(1);

      // verify onSubmit
      commonPinCode.onSubmit?.call('1');
      verify(() => mockOnSubmit.call('1')).called(1);

      // verify text error message
      final Finder textErrorFinder = find.text(errorTextExpect);
      expect(textErrorFinder, findsOneWidget);

      final Text textError = widgetTester.widget(textErrorFinder);
      expect(textError.style, errorStyleExpect);

      // verify text reset pin
      final Finder textResetPinFinder = find.text(resetPinTextExpect);
      expect(textResetPinFinder, findsOneWidget);

      final Text textResetPin = widgetTester.widget(textResetPinFinder);
      expect(textResetPin.style, resetPinStyleExpect);

      await widgetTester.tap(textResetPinFinder);
      await widgetTester.pump(Duration(milliseconds: 100));
      verify(() => mockOnResetPin.call()).called(1);

      // verify inkwell change show obscure text
      final Finder inkWellFinder = find.byType(InkWell);
      expect(inkWellFinder, findsOneWidget);

      verify(() => commonImageProvider.asset(EvoImages.icShowOffPin)).called(1);

      final Finder evoPinCodeWidgetFinder = find.byType(EvoPinCodeWidget);
      expect(evoPinCodeWidgetFinder, findsOneWidget);
      final EvoPinCodeWidgetState evoPinCodeWidgetState =
          widgetTester.state(evoPinCodeWidgetFinder);
      expect(evoPinCodeWidgetState.showObscureText, isTrue);

      // verify after tap inkwell
      await widgetTester.tap(inkWellFinder);
      await widgetTester.pump(Duration(milliseconds: 100));
      verify(() => commonImageProvider.asset(EvoImages.icShowOnPin)).called(1);
      final CommonPinCode commonPinCodeAfterTap = widgetTester.widget(commonPinCodeFinder);
      expect(commonPinCodeAfterTap.obscuringWidget, isNull);
      expect(evoPinCodeWidgetState.showObscureText, isFalse);
    });
  });
}
