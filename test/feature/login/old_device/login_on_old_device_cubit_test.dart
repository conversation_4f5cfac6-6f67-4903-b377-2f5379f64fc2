import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/login/old_device/login_on_old_device_cubit.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/util/share_preference_helper.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';
import '../../splash_screen/splash_cubit_test.dart';
import 'login_on_old_device_test_class_mock.dart';

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

class MockFeatureToggle extends Mock implements FeatureToggle {}

void main() {
  late AuthenticationRepoMock authenticationRepoMock;
  late EvoLocalStorageHelperMock localStorageHelperMock;
  late BiometricsAuthenticateMock biometricsAuthenticateMock;
  late JwtHelperMock jwtHelperMock;
  late BiometricsTokenModuleMock biometricsTokenModuleMock;
  late AppState appState;
  late CommonUtilFunction mockCommonUtilFunction;
  late MockOneSignal mockOneSignal;

  const String phoneNumber = '0355089123';

  setUpAll(() {
    authenticationRepoMock = AuthenticationRepoMock();
    localStorageHelperMock = EvoLocalStorageHelperMock();
    biometricsAuthenticateMock = BiometricsAuthenticateMock();
    jwtHelperMock = JwtHelperMock();

    biometricsTokenModuleMock = BiometricsTokenModuleMock();
    getIt.registerLazySingleton<BiometricsTokenModule>(() => biometricsTokenModuleMock);

    mockOneSignal = testOneSignalExecutable();

    appState = AppState();
    getIt.registerLazySingleton<AppState>(() => appState);

    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());

    getIt.registerLazySingleton<CommonUtilFunction>(() => MockCommonUtilFunction());
    mockCommonUtilFunction = getIt.get<CommonUtilFunction>();

    getIt.registerLazySingleton<EvoLocalStorageHelper>(() => localStorageHelperMock);

    when(() => localStorageHelperMock.getUserPhoneNumber()).thenAnswer((_) async {
      return phoneNumber;
    });
  });

  LoginOnOldDeviceCubit getLoginCubit() {
    return LoginOnOldDeviceCubit(
      authenticationRepoMock,
      localStorageHelperMock,
      biometricsAuthenticateMock,
      jwtHelperMock,
      biometricsTokenModuleMock,
    );
  }

  group('test loginWithAnotherAccount function', () {
    setUpAll(() {
      getIt.registerLazySingleton<CommonSharedPreferencesHelper>(() => MockCommonSharedPreferencesHelper());
      when(()=> getIt<CommonSharedPreferencesHelper>().removeKey(any())).thenAnswer((_) async => true);
      when(() => localStorageHelperMock.clearAllUserData()).thenAnswer((_) async {});

      when(() => mockCommonUtilFunction.clearNotifications()).thenAnswer((_) async {
        return Future<void>.value();
      });

      when(
        () => commonUtilFunction.clearDataOnTokenInvalid(
            clearAllNotifications: any(named: 'clearAllNotifications')),
      ).thenAnswer((_) async {});
    });

    test('make sure clear data when user login with another phone number', () async {
      await getLoginCubit().loginWithNewAccount(oneSignal: mockOneSignal);

      verify(() => localStorageHelperMock.clearAllUserData()).called(1);
      verify(() => commonUtilFunction.clearDataOnTokenInvalid(clearAllNotifications: true))
          .called(1);
    });
  });

  group('test getUserPhoneNumberFromLocal function', () {
    test('test getUserPhoneNumberFromLocal return correct value from local', () async {
      final String? lastPhoneNumber = await getLoginCubit().getUserPhoneNumberFromLocal();

      expect(lastPhoneNumber, phoneNumber);

      verify(() => localStorageHelperMock.getUserPhoneNumber()).called(1);
    });
  });

  group('test init function with isBiometricFailManyTime is false', () {
    setUpAll(() {
      when(() => biometricsTokenModuleMock.disableBiometricAuthenticatorFeature())
          .thenAnswer((_) async {});
    });

    blocTest<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
      'test cannot login with biometric when biometric is not enable',
      setUp: () {
        when(() => biometricsTokenModuleMock.isEnableBiometricAuthenticator())
            .thenAnswer((_) async {
          return false;
        });
      },
      build: () => getLoginCubit(),
      act: (LoginOnOldDeviceCubit cubit) => cubit.init(),
      expect: () => <dynamic>[
        isA<LoginOnOldDeviceInitial>()
            .having(
              (LoginOnOldDeviceInitial state) => state.isBiometricLoginAvailable,
              'verify isBiometricLoginAvailable value',
              false,
            )
            .having(
              (LoginOnOldDeviceInitial state) => state.phoneNumber,
              'verify phone number value',
              phoneNumber,
            )
      ],
    );

    blocTest<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
        'test cannot login with biometric when biometric is enable, biometric token can not use',
        setUp: () {
          when(() => biometricsTokenModuleMock.isEnableBiometricAuthenticator())
              .thenAnswer((_) async {
            return true;
          });

          when(() => biometricsTokenModuleMock.isBiometricTokenUsable()).thenAnswer((_) async {
            return false;
          });
        },
        build: () => getLoginCubit(),
        act: (LoginOnOldDeviceCubit cubit) => cubit.init(),
        expect: () => <dynamic>[
              isA<LoginOnOldDeviceInitial>()
                  .having(
                    (LoginOnOldDeviceInitial state) => state.isBiometricLoginAvailable,
                    'verify isBiometricLoginAvailable value',
                    false,
                  )
                  .having(
                    (LoginOnOldDeviceInitial state) => state.phoneNumber,
                    'verify phone number value',
                    phoneNumber,
                  )
            ],
        verify: (_) {
          verify(() => biometricsTokenModuleMock.disableBiometricAuthenticatorFeature()).called(1);
        });

    blocTest<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
        'test can login with biometric when biometric is enable, biometric token can  use',
        setUp: () {
          when(() => biometricsTokenModuleMock.isEnableBiometricAuthenticator())
              .thenAnswer((_) async {
            return true;
          });

          when(() => biometricsTokenModuleMock.isBiometricTokenUsable()).thenAnswer((_) async {
            return true;
          });
        },
        build: () => getLoginCubit(),
        act: (LoginOnOldDeviceCubit cubit) => cubit.init(),
        expect: () => <dynamic>[
              isA<LoginOnOldDeviceInitial>()
                  .having(
                    (LoginOnOldDeviceInitial state) => state.isBiometricLoginAvailable,
                    'verify isBiometricLoginAvailable value',
                    true,
                  )
                  .having(
                    (LoginOnOldDeviceInitial state) => state.phoneNumber,
                    'verify phone number value',
                    phoneNumber,
                  )
            ],
        verify: (_) {
          verifyNever(biometricsTokenModuleMock.disableBiometricAuthenticatorFeature);
        });
  });

  group(
      'test init function with isBiometricFailManyTime is true, make sure biometric login always not use',
      () {
    setUpAll(() {
      when(() => biometricsTokenModuleMock.disableBiometricAuthenticatorFeature())
          .thenAnswer((_) async {});
    });

    blocTest<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
      'test cannot login with biometric when biometric is not enable',
      setUp: () {
        when(() => biometricsTokenModuleMock.isEnableBiometricAuthenticator())
            .thenAnswer((_) async {
          return false;
        });
      },
      build: () => getLoginCubit(),
      act: (LoginOnOldDeviceCubit cubit) => cubit.init(isBiometricFailManyTime: true),
      expect: () => <dynamic>[
        isA<LoginOnOldDeviceInitial>()
            .having(
              (LoginOnOldDeviceInitial state) => state.isBiometricLoginAvailable,
              'verify isBiometricLoginAvailable value',
              false,
            )
            .having(
              (LoginOnOldDeviceInitial state) => state.phoneNumber,
              'verify phone number value',
              phoneNumber,
            )
      ],
    );

    blocTest<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
        'test cannot login with biometric when biometric is enable, biometric token can not use',
        setUp: () {
          when(() => biometricsTokenModuleMock.isEnableBiometricAuthenticator())
              .thenAnswer((_) async {
            return true;
          });

          when(() => biometricsTokenModuleMock.isBiometricTokenUsable()).thenAnswer((_) async {
            return false;
          });
        },
        build: () => getLoginCubit(),
        act: (LoginOnOldDeviceCubit cubit) => cubit.init(isBiometricFailManyTime: true),
        expect: () => <dynamic>[
              isA<LoginOnOldDeviceInitial>()
                  .having(
                    (LoginOnOldDeviceInitial state) => state.isBiometricLoginAvailable,
                    'verify isBiometricLoginAvailable value',
                    false,
                  )
                  .having(
                    (LoginOnOldDeviceInitial state) => state.phoneNumber,
                    'verify phone number value',
                    phoneNumber,
                  )
            ],
        verify: (_) {
          verify(() => biometricsTokenModuleMock.disableBiometricAuthenticatorFeature()).called(1);
        });

    blocTest<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
        'test cannot login with biometric even though biometric is enable, biometric token can  use',
        setUp: () {
          when(() => biometricsTokenModuleMock.isEnableBiometricAuthenticator())
              .thenAnswer((_) async {
            return true;
          });

          when(() => biometricsTokenModuleMock.isBiometricTokenUsable()).thenAnswer((_) async {
            return true;
          });
        },
        build: () => getLoginCubit(),
        act: (LoginOnOldDeviceCubit cubit) => cubit.init(isBiometricFailManyTime: true),
        expect: () => <dynamic>[
              isA<LoginOnOldDeviceInitial>()
                  .having(
                    (LoginOnOldDeviceInitial state) => state.isBiometricLoginAvailable,
                    'verify isBiometricLoginAvailable value',
                    false,
                  )
                  .having(
                    (LoginOnOldDeviceInitial state) => state.phoneNumber,
                    'verify phone number value',
                    phoneNumber,
                  )
            ],
        verify: (_) {
          verifyNever(biometricsTokenModuleMock.disableBiometricAuthenticatorFeature);
        });
  });
}
