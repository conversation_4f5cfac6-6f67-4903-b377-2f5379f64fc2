import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/login/old_device/login_by_pincode_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/default_obscure_widget.dart';
import 'package:evoapp/widget/default_obscure_widget_v2.dart';
import 'package:evoapp/widget/evo_pin_code/evo_pin_code_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_common_package/widget/pin_code/pin_code_utils.dart';
import 'package:flutter_common_package/widget/pin_code/pin_code_widget.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

class MockCallback extends Mock {
  void call(String value);
}

class MockCallbackWithoutParam extends Mock {
  void call();
}

void main() {
  const EdgeInsets paddingContainerIconExpect = EdgeInsets.only(
    left: 30,
    top: 10,
    bottom: 10,
  );
  const String errorMessageExpect = 'error message';

  late CommonImageProvider commonImageProvider;
  late MockCallback mockOnChange;
  late MockCallback mockOnSubmit;
  late MockCallbackWithoutParam mockOnResetPin;
  late CommonUtilFunction commonUtilFunction;

  setUpAll(() {
    registerFallbackValue(FocusNode());

    getItRegisterColorV2();
    getItRegisterTextStyleV2();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getIt.registerLazySingleton<FeatureToggle>(() => MockFeatureToggle());

    commonImageProvider = getIt<CommonImageProvider>();
    commonUtilFunction = getIt<CommonUtilFunction>();
    mockOnChange = MockCallback();
    mockOnSubmit = MockCallback();
    mockOnResetPin = MockCallbackWithoutParam();

    when(() => commonImageProvider.asset(any())).thenReturn(Container());
    when(() => getIt<FeatureToggle>().enableRevampUiFeature).thenReturn(true);
  });

  group('test [LoginByPinCodeWidget]', () {
    testWidgets('verify with default value', (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: LoginByPinCodeWidget(),
          ),
        ),
      );

      // verify openKeyboard() called
      final Finder loginByPinCodeWidgetFinder = find.byType(LoginByPinCodeWidget);
      expect(loginByPinCodeWidgetFinder, findsOneWidget);
      final LoginByPinCodeWidgetState loginByPinCodeWidgetState =
          widgetTester.state(loginByPinCodeWidgetFinder);
      loginByPinCodeWidgetState.openKeyboard();
      verify(() => commonUtilFunction.delayAndRequestFocus(any())).called(1);

      // verify CommonPinCode
      final Finder commonPinCodeFinder = find.byType(CommonPinCode);
      expect(commonPinCodeFinder, findsOneWidget);

      final CommonPinCode commonPinCode = widgetTester.widget(commonPinCodeFinder);
      if(getIt<FeatureToggle>().enableRevampUiFeature) {
        expect(commonPinCode.obscuringWidget, isA<DefaultObscureWidgetV2>());
      } else {
        expect(commonPinCode.obscuringWidget, isA<DefaultObscureWidget>());
      }
      expect(commonPinCode.pinLength, PinCodeUtils.defaultMaxPinLength);
      expect(commonPinCode.animationDuration, Duration.zero);
      if(getIt<FeatureToggle>().enableRevampUiFeature) {
        expect(commonPinCode.pinTheme?.fieldHeight, EvoPinCodeConfig.defaultPinCodeFieldHeightV2);
      } else {
        expect(commonPinCode.pinTheme?.fieldHeight, EvoPinCodeConfig.defaultPinCodeFieldHeight);
      }

      // verify text error message
      final Finder textErrorFinder = find.byWidgetPredicate((Widget widget) {
        if (widget is Text) {
          return widget.data?.isEmpty == true &&
              widget.style == evoTextStyles.bodyMedium(evoColors.error);
        }
        return false;
      });
      expect(textErrorFinder, findsOneWidget);

      // verify text reset pin
      final Finder textResetPinFinder = find.text(EvoStrings.loginScreenResetPin);
      expect(textResetPinFinder, findsOneWidget);

      final Text textResetPin = widgetTester.widget(textResetPinFinder);
      expect(textResetPin.style, evoTextStyles.bodyLarge(evoColors.textPassive));

      // verify icon show/hide pin code
      final Finder containerFinder = find.byWidgetPredicate((Widget widget) {
        if (widget is Container) {
          return widget.padding == paddingContainerIconExpect && widget.color == Colors.transparent;
        }
        return false;
      });

      expect(containerFinder, findsOneWidget);
      expect(loginByPinCodeWidgetState.isHidePinCode, isTrue);

      verify(() => commonImageProvider.asset(EvoImages.icShowOffPin)).called(1);

      // verify onTap icon show/hide pin code
      await widgetTester.tap(containerFinder);
      await widgetTester.pump(Duration(milliseconds: 100));
      verify(() => commonImageProvider.asset(EvoImages.icShowOnPin)).called(1);
      final CommonPinCode commonPinCodeAfterTap = widgetTester.widget(commonPinCodeFinder);
      expect(commonPinCodeAfterTap.obscuringWidget, isNull);
      expect(loginByPinCodeWidgetState.isHidePinCode, isFalse);
    });

    testWidgets('verify with custom value', (WidgetTester widgetTester) async {
      when(() => getIt<FeatureToggle>().enableRevampUiFeature).thenReturn(false);
      const int pinLenExpect = 2;

      await widgetTester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LoginByPinCodeWidget(
              onChange: mockOnChange.call,
              onSubmit: mockOnSubmit.call,
              onResetPin: mockOnResetPin.call,
              errorMessage: errorMessageExpect,
              pinLen: 2,
            ),
          ),
        ),
      );

      // verify openKeyboard() called
      final Finder loginByPinCodeWidgetFinder = find.byType(LoginByPinCodeWidget);
      expect(loginByPinCodeWidgetFinder, findsOneWidget);
      final LoginByPinCodeWidgetState loginByPinCodeWidgetState =
          widgetTester.state(loginByPinCodeWidgetFinder);
      loginByPinCodeWidgetState.openKeyboard();
      verify(() => commonUtilFunction.delayAndRequestFocus(any())).called(1);

      // verify CommonPinCode
      final Finder commonPinCodeFinder = find.byType(CommonPinCode);
      expect(commonPinCodeFinder, findsOneWidget);

      final CommonPinCode commonPinCode = widgetTester.widget(commonPinCodeFinder);
      if(getIt<FeatureToggle>().enableRevampUiFeature) {
        expect(commonPinCode.obscuringWidget, isA<DefaultObscureWidgetV2>());
      } else {
        expect(commonPinCode.obscuringWidget, isA<DefaultObscureWidget>());
      }
      expect(commonPinCode.pinLength, pinLenExpect);
      expect(commonPinCode.animationDuration, Duration.zero);
      if(getIt<FeatureToggle>().enableRevampUiFeature) {
        expect(commonPinCode.pinTheme?.fieldHeight, EvoPinCodeConfig.defaultPinCodeFieldHeightV2);
      } else {
        expect(commonPinCode.pinTheme?.fieldHeight, EvoPinCodeConfig.defaultPinCodeFieldHeight);
      }

      // verify onChange
      commonPinCode.onChange?.call('1');
      commonPinCode.onChange?.call('2');

      verify(() => mockOnChange.call('1')).called(1);
      verify(() => mockOnChange.call('2')).called(1);

      // verify onSubmit
      commonPinCode.onSubmit?.call('1');
      verify(() => mockOnSubmit.call('1')).called(1);

      // verify text error message
      final Finder textErrorFinder = find.text(errorMessageExpect);
      expect(textErrorFinder, findsOneWidget);

      final Text textError = widgetTester.widget(textErrorFinder);
      expect(textError.style, evoTextStyles.bodyMedium(evoColors.error));

      // verify text reset pin
      final Finder textResetPinFinder = find.text(EvoStrings.loginScreenResetPin);
      expect(textResetPinFinder, findsOneWidget);

      // verify onTap text reset pin
      await widgetTester.tap(textResetPinFinder);
      await widgetTester.pump(Duration(milliseconds: 100));
      verify(() => mockOnResetPin.call()).called(1);

      final Text textResetPin = widgetTester.widget(textResetPinFinder);
      expect(textResetPin.style, evoTextStyles.bodyLarge(evoColors.textPassive));

      // verify icon show/hide pin code
      final Finder containerFinder = find.byWidgetPredicate((Widget widget) {
        if (widget is Container) {
          return widget.padding == paddingContainerIconExpect && widget.color == Colors.transparent;
        }
        return false;
      });

      expect(containerFinder, findsOneWidget);
      expect(loginByPinCodeWidgetState.isHidePinCode, isTrue);

      verify(() => commonImageProvider.asset(EvoImages.icShowOffPin)).called(1);

      // verify onTap icon show/hide pin code
      await widgetTester.tap(containerFinder);
      await widgetTester.pump(Duration(milliseconds: 100));
      verify(() => commonImageProvider.asset(EvoImages.icShowOnPin)).called(1);
      final CommonPinCode commonPinCodeAfterTap = widgetTester.widget(commonPinCodeFinder);
      expect(commonPinCodeAfterTap.obscuringWidget, isNull);
      expect(loginByPinCodeWidgetState.isHidePinCode, isFalse);
    });
  });
}
