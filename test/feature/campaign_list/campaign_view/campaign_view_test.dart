import 'package:evoapp/data/repository/campaign_repo.dart';
import 'package:evoapp/data/response/campaign_list_entity.dart';
import 'package:evoapp/feature/campaign_list/campaign_view/campaign_view.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/model/evo_action_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_action_handler.dart';
import 'package:evoapp/util/mock_file_name_utils/mock_campaign_file_name.dart';
import 'package:evoapp/widget/empty_data_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/widget/refreshable_view.dart';
import 'package:flutter_common_package/widget/shimmer/shimmer_animation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';
import '../../home/<USER>/home_campaign_cubit_test.dart';

void main() async {
  TestWidgetsFlutterBinding.ensureInitialized();
  late CampaignRepo mockCampaignRepo;

  setUpAll(() {
    registerFallbackValue(BoxFit.cover);
    registerFallbackValue(EvoActionModel());
  });

  setUp(() {
    mockCampaignRepo = MockCampaignRepo();
    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterButtonStyle();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getIt.registerLazySingleton(() => mockCampaignRepo);
    setupMockImageProvider();

    //setup mock EvoActionHandler
    EvoActionHandler.setInstanceForTesting(MockEvoActionHandler());
    when(() => EvoActionHandler().handle(any(), arg: any(named: 'arg')))
        .thenAnswer((_) async => true);

    //setup link utils mock
    setUpOneLinkDeepLinkRegExForTest();

    //setup feature toggle mock
    getIt.registerLazySingleton<FeatureToggle>(() => MockFeatureToggle());
    when(() => getIt<FeatureToggle>().enableEventTrackingFeature).thenAnswer((_) => true);
  });

  tearDown(() {
    getIt.reset();
    EvoActionHandler.resetToOriginalInstance();
  });

  testWidgets('CampaignListPage on load success with campaign lists not empty',
      (WidgetTester tester) async {
    final BaseResponse mockResponse = await getMockBaseResponse(getOffersMockFileName());
    when(() => mockCampaignRepo.getOffers(
        flowType: FlowType.offers, mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
      await Future<void>.delayed(Duration(milliseconds: 100));
      return CampaignListEntity.fromBaseResponse(mockResponse);
    });

    await tester.pumpWidget(MaterialApp(
      home: Scaffold(
        body: ShimmerAnimation(child: CampaignView()),
      ),
    ));

    verify(() => mockCampaignRepo.getOffers(
        flowType: FlowType.offers, mockConfig: any(named: 'mockConfig'))).called(1);
    await tester.pump(Duration(milliseconds: 200));

    //test onRefresh method
    final Finder refreshableViewFinder = find.byType(RefreshableView);
    final RefreshableView refreshableView = tester.widget<RefreshableView>(refreshableViewFinder);
    refreshableView.onRefresh();
    refreshableView.controller.requestRefresh();
    verify(() => mockCampaignRepo.getOffers(
        flowType: FlowType.offers, mockConfig: any(named: 'mockConfig'))).called(1);
    await tester.pump(Duration(milliseconds: 200));

    //test on tap campaign widget
    final Finder campaignItemFinder = find.byKey(Key('campaign_item_0'));
    await tester.tap(campaignItemFinder);
    verify(() => EvoActionHandler().handle(any(), arg: any(named: 'arg'))).called(1);
  });

  testWidgets('CampaignListPage on load success with empty campaign lists',
      (WidgetTester tester) async {
    when(() => mockCampaignRepo.getOffers(
        flowType: FlowType.offers, mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
      await Future<void>.delayed(Duration(milliseconds: 100));
      return CampaignListEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{},
        },
      ));
    });

    await tester.pumpWidget(MaterialApp(
      home: Scaffold(
        body: ShimmerAnimation(child: CampaignView()),
      ),
    ));

    verify(() => mockCampaignRepo.getOffers(
        flowType: FlowType.offers, mockConfig: any(named: 'mockConfig'))).called(1);
    await tester.pump(Duration(milliseconds: 200));

    //should render empty view
    expect(find.byType(EmptyDataContainer), findsOneWidget);
  });

  testWidgets('CampaignListPage on load fail', (WidgetTester tester) async {
    bool isShowedError = false;
    when(() => mockCampaignRepo.getOffers(
        flowType: FlowType.offers, mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
      await Future<void>.delayed(Duration(milliseconds: 100));
      return CampaignListEntity();
    });

    await tester.pumpWidget(MaterialApp(
      home: Scaffold(
        body: ShimmerAnimation(child: CampaignView(
          onErrorUIModel: (_) {
            isShowedError = true;
          },
        )),
      ),
    ));

    await tester.pump(Duration(milliseconds: 200));

    //should render empty view
    expect(isShowedError, true);
  });
}
