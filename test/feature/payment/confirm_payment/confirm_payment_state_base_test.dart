import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/campaign_repo.dart';
import 'package:evoapp/data/repository/checkout_repo.dart';
import 'package:evoapp/data/response/emi_package_entity.dart';
import 'package:evoapp/data/response/emi_tenor_offer_entity.dart';
import 'package:evoapp/data/response/linked_card_status_checking_entity.dart';
import 'package:evoapp/data/response/order_session_entity.dart';
import 'package:evoapp/data/response/payment_method_entity.dart';
import 'package:evoapp/data/response/promotion_info_entity.dart';
import 'package:evoapp/data/response/store_info_entity.dart';
import 'package:evoapp/data/response/voucher_entity.dart';
import 'package:evoapp/feature/biometric_pin_confirm/biometric_and_pin_confirmation.dart';
import 'package:evoapp/feature/biometric_pin_confirm/biometric_pin_data.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/manual_link_card/manual_link_card_cubit.dart';
import 'package:evoapp/feature/manual_link_card/model/linked_card_status_model.dart';
import 'package:evoapp/feature/payment/base_page_payment/cubit/update_order_cubit.dart';
import 'package:evoapp/feature/payment/confirm_payment/bloc/confirm_button_cubit/confirm_button_cubit.dart';
import 'package:evoapp/feature/payment/confirm_payment/bloc/confirm_payment_cubit.dart';
import 'package:evoapp/feature/payment/confirm_payment/bloc/order_info_cubit.dart';
import 'package:evoapp/feature/payment/confirm_payment/confirm_payment_state_base.dart';
import 'package:evoapp/feature/payment/confirm_payment/model/order_info_ui_model.dart';
import 'package:evoapp/feature/payment/payment_shared_data.dart';
import 'package:evoapp/feature/payment/promotion/payment_promotion_list_screen.dart';
import 'package:evoapp/feature/payment/widget/payment_detail_info/emi_summary_info_widget.dart';
import 'package:evoapp/feature/payment/widget/payment_detail_info/revamp_payment_detail_info/revamp_emi_summary_info_widget.dart';
import 'package:evoapp/feature/payment/widget/payment_detail_info/revamp_payment_detail_info/revamp_outright_purchase_summary_info_widget.dart';
import 'package:evoapp/feature/payment/widget/payment_title_widget/emi_payment_title_widget.dart';
import 'package:evoapp/feature/payment/widget/payment_title_widget/outright_purchase_payment_title_widget.dart';
import 'package:evoapp/feature/payment/widget/promotion_selected/revamp_promotion_selected/revamp_payment_promotion_widget.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_authentication_helper.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:evoapp/widget/evo_appbar.dart';
import 'package:evoapp/widget/evo_appbar_leading_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/flutter_test_config.dart';

// Mock classes
class MockConfirmPaymentCubit extends MockCubit<ConfirmPaymentState>
    implements ConfirmPaymentCubit {}

class MockConfirmButtonCubit extends MockCubit<ConfirmButtonState> implements ConfirmButtonCubit {}

class MockBiometricAndPinConfirmation extends Mock implements BiometricAndPinConfirmation {}

class MockUpdateOrderCubit extends MockCubit<UpdateOrderState> implements UpdateOrderCubit {}

class MockOrderInfoCubit extends MockCubit<OrderInfoState> implements OrderInfoCubit {}

class MockManualLinkCardCubit extends MockCubit<ManualLinkCardState>
    implements ManualLinkCardCubit {}

class MockOrderSessionEntity extends Mock implements OrderSessionEntity {}

class MockEmiPackageEntity extends Mock implements EmiPackageEntity {}

class MockVoucherEntity extends Mock implements VoucherEntity {}

class MockPaymentMethodEntity extends Mock implements PaymentMethodEntity {}

class MockErrorUIModel extends Mock implements ErrorUIModel {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockEvoSnackBar extends Mock implements EvoSnackBar {}

class MockEmiTenorOfferEntity extends Mock implements EmiTenorOfferEntity {}

class MockStoreInfoEntity extends Mock implements StoreInfoEntity {}

class MockOrderInfoUIModel extends Mock implements OrderInfoUIModel {}

class MockLinkedCardStatusCheckingEntity extends Mock implements LinkedCardStatusCheckingEntity {}

class MockAppState extends Mock implements AppState {}

class MockCampaignRepo extends Mock implements CampaignRepo {}

class MockCheckOutRepo extends Mock implements CheckOutRepo {}

// Implementation of ConfirmBiometricAndPinCallback for testing
class FakeConfirmBiometricAndPinCallback implements ConfirmBiometricAndPinCallback {
  @override
  void onBiometricConfirm(String? biometricToken) {}

  @override
  void onConfirmPinPopupClosed() {}

  @override
  void onPinInputConfirm(String? pin) {}
}

// Test implementation of ConfirmPaymentScreenStateBase
class TestConfirmPaymentScreen extends PageBase {
  const TestConfirmPaymentScreen({super.key});

  @override
  State<TestConfirmPaymentScreen> createState() => TestConfirmPaymentScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId =>
      const EventTrackingScreenId('test_confirm_payment');

  @override
  RouteSettings get routeSettings => const RouteSettings(name: 'test_confirm_payment');
}

class TestConfirmPaymentScreenState
    extends ConfirmPaymentScreenStateBase<TestConfirmPaymentScreen> {
  @override
  List<String>? getTenorInfo() {
    return <String>['Tenor info 1', 'Tenor info 2'];
  }

  @override
  void getSnackBarBottomPadding(Offset? voucherUIOffset) {
    // Do nothing in tests to avoid context issues
    snackBarMarginBottomRatio = 0.1; // Set a default value for testing
  }
}

class TestConfirmPaymentScreenStateMockBuildBody
    extends ConfirmPaymentScreenStateBase<TestConfirmPaymentScreen> {
  bool isBuildBodyCalled = false;

  @override
  List<String>? getTenorInfo() {
    return <String>['Tenor info 1', 'Tenor info 2'];
  }

  @override
  Widget buildBody(
      {OrderSessionEntity? orderSession,
      EmiPackageEntity? emiPackage,
      VoucherEntity? selectedVoucher,
      VoucherSelectionState voucherSelectionState = VoucherSelectionState.noSelect}) {
    isBuildBodyCalled = true;
    return const Text('Build Body Called');
  }
}

class TestConfirmPaymentScreenStateTestGetSnackBarBottomPadding
    extends ConfirmPaymentScreenStateBase<TestConfirmPaymentScreen> {
  @override
  List<String>? getTenorInfo() {
    return <String>['Tenor info 1', 'Tenor info 2'];
  }
}

void main() {
  late MockConfirmPaymentCubit mockConfirmPaymentCubit;
  late MockConfirmButtonCubit mockConfirmButtonCubit;
  late MockBiometricAndPinConfirmation mockBiometricAndPinConfirmation;
  late MockUpdateOrderCubit mockUpdateOrderCubit;
  late MockOrderInfoCubit mockOrderInfoCubit;
  late MockManualLinkCardCubit mockManualLinkCardCubit;
  late MockFeatureToggle mockFeatureToggle;
  late MockEvoSnackBar mockEvoSnackBar;
  late Widget testConfirmPaymentScreen;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(SnackBarType.error);
    registerFallbackValue(AuthenticateType.biometricToken);
    registerFallbackValue(MockOrderSessionEntity());
    registerFallbackValue(MockEmiPackageEntity());
    registerFallbackValue(MockVoucherEntity());
    registerFallbackValue(MockErrorUIModel());
    registerFallbackValue(MockEmiTenorOfferEntity());
    registerFallbackValue(FakeConfirmBiometricAndPinCallback());
    registerFallbackValue(VoucherSelectionState.noSelect);
    registerFallbackValue(EnableState());
    registerFallbackValue(DisableState());
    registerFallbackValue(EvoDialogId.expiredOrderBottomSheet);
    registerFallbackValue(EvoDialogId.paymentInvalidPromotionBottomSheet);
    registerFallbackValue(EvoDialogId.manualLinkCardEncourageBottomSheet);
    registerFallbackValue(EvoDialogId.waitingAfterActivateCardBottomSheet);

    initConfigEvoPageStateBase();
  });

  setUp(() {
    setUtilsMockInstanceForTesting();
    setUpMockConfigEvoPageStateBase();
    setupMockImageProvider();
    setUpMockSnackBarForTest();

    // Initialize mocks
    mockConfirmPaymentCubit = MockConfirmPaymentCubit();
    mockConfirmButtonCubit = MockConfirmButtonCubit();
    mockBiometricAndPinConfirmation = MockBiometricAndPinConfirmation();
    mockUpdateOrderCubit = MockUpdateOrderCubit();
    mockOrderInfoCubit = MockOrderInfoCubit();
    mockManualLinkCardCubit = MockManualLinkCardCubit();
    mockFeatureToggle = MockFeatureToggle();
    mockEvoSnackBar = MockEvoSnackBar();

    // Register mocks in getIt
    getIt.registerFactory<ConfirmPaymentCubit>(() => mockConfirmPaymentCubit);
    getIt.registerFactory<ConfirmButtonCubit>(() => mockConfirmButtonCubit);
    getIt.registerFactory<BiometricAndPinConfirmation>(() => mockBiometricAndPinConfirmation);
    getIt.registerFactory<UpdateOrderCubit>(() => mockUpdateOrderCubit);
    getIt.registerFactory<OrderInfoCubit>(() => mockOrderInfoCubit);
    getIt.registerFactory<ManualLinkCardCubit>(() => mockManualLinkCardCubit);
    getIt.registerSingleton<FeatureToggle>(mockFeatureToggle);

    // Set up default mock behaviors
    when(() => mockFeatureToggle.enableInstantCashbackFeature).thenReturn(false);
    when(() => mockConfirmPaymentCubit.state).thenReturn(ConfirmPaymentInitial());
    when(() => mockConfirmButtonCubit.state).thenReturn(EnableState());
    when(() => mockOrderInfoCubit.state).thenReturn(OrderInfoUpdateInitial());
    when(() => mockManualLinkCardCubit.state).thenReturn(ManualLinkCardInitState());
    when(() => mockUpdateOrderCubit.state).thenReturn(UpdateOrderInitial());
    when(() => mockOrderInfoCubit.order).thenReturn(null);
    when(() => mockOrderInfoCubit.emiPackage).thenReturn(null);
    when(() => mockOrderInfoCubit.selectedVoucher).thenReturn(null);
    when(() => mockOrderInfoCubit.hasValidVoucher).thenReturn(false);
    when(() => mockOrderInfoCubit.needShowNotSelectVoucherWarning()).thenReturn(false);

    when(() => mockConfirmPaymentCubit.confirmAndPay(
          authenticateType: any(named: 'authenticateType'),
          biometricToken: any(named: 'biometricToken'),
          orderSession: any(named: 'orderSession'),
          emiPackage: any(named: 'emiPackage'),
          pin: any(named: 'pin'),
          selectedVoucher: any(named: 'selectedVoucher'),
        )).thenAnswer((_) => Future<void>.value());

    when(() => mockBiometricAndPinConfirmation.confirm(callback: any(named: 'callback')))
        .thenAnswer((_) => Future<void>.value());

    when(() => EvoUiUtils().showHudLoading(loadingText: EvoStrings.hubLoadingText))
        .thenAnswer((_) => Future<void>.value());

    when(() => mockEvoUtilFunction.evoFormatCurrency(
          any(),
          currencySymbol: vietNamCurrencySymbol,
        )).thenReturn('10.000 ₫');

    when(() => EvoDialogHelper().showDialogBottomSheet(
          content: any(named: 'content'),
          title: any(named: 'title'),
          dialogId: any(named: 'dialogId'),
          textPositive: any(named: 'textPositive'),
          buttonListOrientation: any(named: 'buttonListOrientation'),
          positiveButtonStyle: any(named: 'positiveButtonStyle'),
          contentTextStyle: any(named: 'contentTextStyle'),
          onClickPositive: any(named: 'onClickPositive'),
          isDismissible: any(named: 'isDismissible'),
          textNegative: any(named: 'textNegative'),
          onClickNegative: any(named: 'onClickNegative'),
          negativeButtonStyle: any(named: 'negativeButtonStyle'),
        )).thenAnswer((_) async {});

    // Setup test widget
    testConfirmPaymentScreen = MaterialApp(
      home: const TestConfirmPaymentScreen(),
    );
  });

  tearDown(() {
    getIt.unregister<ConfirmPaymentCubit>();
    getIt.unregister<ConfirmButtonCubit>();
    getIt.unregister<BiometricAndPinConfirmation>();
    getIt.unregister<UpdateOrderCubit>();
    getIt.unregister<OrderInfoCubit>();
    getIt.unregister<ManualLinkCardCubit>();
    getIt.unregister<FeatureToggle>();

    resetUtilMockToOriginalInstance();

    reset(mockConfirmPaymentCubit);
  });

  tearDownAll(() {
    getIt.reset();
  });

  testWidgets('should build widget correctly with initial state', (WidgetTester tester) async {
    await tester.pumpWidget(testConfirmPaymentScreen);
    await tester.pumpAndSettle();

    // Verify that the app bar exists
    expect(find.byType(EvoAppBar), findsOneWidget);

    // Verify the back button exists
    expect(find.byType(EvoAppBarLeadingButton), findsOneWidget);

    // Verify that the initial state shows an empty screen (no order data)
    expect(find.byType(SizedBox), findsWidgets);
  });

  group('handleConfirmPaymentListener tests', () {
    testWidgets('should show loading when ConfirmPaymentLoading state',
        (WidgetTester tester) async {
      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      state.handleConfirmPaymentListener(ConfirmPaymentLoading());

      verify(() => EvoUiUtils().showHudLoading(loadingText: EvoStrings.hubLoadingText)).called(1);
    });

    testWidgets('should navigate to ThreeDPollingScreen on ConfirmPaymentSuccess',
        (WidgetTester tester) async {
      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      final OrderSessionEntity mockOrderSession = MockOrderSessionEntity();
      state.handleConfirmPaymentListener(ConfirmPaymentSuccess(orderSession: mockOrderSession));

      verify(() => mockNavigatorContext.pushReplacementNamed(
            Screen.threeDPolling.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('should validate payment method on NoNeedShowWaitingPopup',
        (WidgetTester tester) async {
      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      final OrderSessionEntity mockOrderSession = MockOrderSessionEntity();
      state.handleConfirmPaymentListener(NoNeedShowWaitingPopup(order: mockOrderSession));

      verify(() => mockManualLinkCardCubit.validatePaymentMethod(order: mockOrderSession))
          .called(1);
    });

    testWidgets('should show bottom sheet waiting on NeedShowWaitingPopup',
        (WidgetTester tester) async {
      when(() => EvoDialogHelper().showDialogBottomSheet(
            header: any(named: 'header'),
            title: any(named: 'title'),
            content: any(named: 'content'),
            dialogId: any(named: 'dialogId'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            textPositive: any(named: 'textPositive'),
            onClickPositive: any(named: 'onClickPositive'),
          )).thenAnswer((_) async {});

      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      state.handleConfirmPaymentListener(NeedShowWaitingPopup(30));

      verify(() => EvoDialogHelper().showDialogBottomSheet(
            header: any(named: 'header'),
            title: EvoStrings.systemNeedTimeToProcess,
            content: EvoStrings.youWaitAndRetryLater,
            dialogId: EvoDialogId.waitingAfterActivateCardBottomSheet,
            isShowButtonClose: any(named: 'isShowButtonClose'),
            textPositive: any(named: 'textPositive'),
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
    });

    testWidgets('should handleConfirmPayFailedState on isConfirmPaymentStateFailed',
        (WidgetTester tester) async {
      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      state.handleConfirmPaymentListener(InvalidCredentialPin(ErrorUIModel()));

      verify(() => getIt.get<EvoSnackBar>().show(
            any(),
            typeSnackBar: any(named: 'typeSnackBar'),
            durationInSec: any(named: 'durationInSec'),
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          )).called(1);
    });
  });

  group('isConfirmPaymentStateFailed tests', () {
    test('should return true for failed states', () {
      final TestConfirmPaymentScreenState state = TestConfirmPaymentScreenState();

      expect(state.isConfirmPaymentStateFailed(RequestPinPopup()), isTrue);
      expect(state.isConfirmPaymentStateFailed(InvalidCredentialPin(MockErrorUIModel())), isTrue);
      expect(
          state.isConfirmPaymentStateFailed(ConfirmAndPayCommonFailed(MockErrorUIModel())), isTrue);
      expect(state.isConfirmPaymentStateFailed(ConfirmPaymentNetworkError(MockErrorUIModel())),
          isTrue);
      expect(state.isConfirmPaymentStateFailed(ConfirmPaymentPromotionError(MockErrorUIModel())),
          isTrue);
      expect(
          state.isConfirmPaymentStateFailed(ConfirmPaymentMissingPaymentMethod(MockErrorUIModel())),
          isTrue);
      expect(
          state.isConfirmPaymentStateFailed(
              ConfirmPaymentFailedWhenOrderExpired(MockErrorUIModel())),
          isTrue);
      expect(state.isConfirmPaymentStateFailed(ConfirmAndPayTransactionTooSoon(MockErrorUIModel())),
          isTrue);
      expect(state.isConfirmPaymentStateFailed(ConfirmAndPayInsufficientCreditLimit()), isTrue);

      expect(state.isConfirmPaymentStateFailed(ConfirmPaymentInitial()), isFalse);
      expect(state.isConfirmPaymentStateFailed(ConfirmPaymentLoading()), isFalse);
      expect(
          state.isConfirmPaymentStateFailed(
              ConfirmPaymentSuccess(orderSession: MockOrderSessionEntity())),
          isFalse);
    });
  });

  group('handleConfirmPayFailedState tests', () {
    testWidgets('should handle RequestPinPopup state', (WidgetTester tester) async {
      when(() => mockBiometricAndPinConfirmation.confirm(
            callback: any(named: 'callback'),
            isForcePin: true,
          )).thenAnswer((_) => Future<void>.value());

      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      state.handleConfirmPayFailedState(RequestPinPopup());

      verify(() => mockBiometricAndPinConfirmation.confirm(
          callback: any(named: 'callback'), isForcePin: true)).called(1);
    });

    testWidgets('should show snackbar error for InvalidCredentialPin', (WidgetTester tester) async {
      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      final ErrorUIModel errorModel = MockErrorUIModel();
      when(() => errorModel.userMessage).thenReturn('Invalid PIN');

      state.handleConfirmPayFailedState(InvalidCredentialPin(errorModel));

      // Verify snackbar is shown with error message
      verify(() => getIt.get<EvoSnackBar>().show(
            'Invalid PIN',
            typeSnackBar: SnackBarType.error,
            durationInSec: any(named: 'durationInSec'),
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          )).called(1);
    });

    testWidgets('should navigate to ConfirmPaymentFailScreen for ConfirmAndPayCommonFailed',
        (WidgetTester tester) async {
      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      final ErrorUIModel errorModel = MockErrorUIModel();
      when(() => errorModel.userMessage).thenReturn('payment fail');

      state.handleConfirmPayFailedState(ConfirmAndPayCommonFailed(errorModel));

      verify(() => mockNavigatorContext.pushReplacementNamed(
            Screen.confirmPaymentFail.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('should handle EVO api error for ConfirmPaymentNetworkError',
        (WidgetTester tester) async {
      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      final ErrorUIModel errorModel = MockErrorUIModel();
      when(() => errorModel.userMessage).thenReturn('network error');

      state.handleConfirmPayFailedState(ConfirmPaymentNetworkError(errorModel));

      verify(() => getIt.get<EvoSnackBar>().show(
            'network error',
            typeSnackBar: SnackBarType.error,
            durationInSec: any(named: 'durationInSec'),
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          )).called(1);
    });

    testWidgets('should remove voucher for ConfirmPaymentPromotionError',
        (WidgetTester tester) async {
      when(() =>
              mockUpdateOrderCubit.updateSelectedVoucher(orderSession: any(named: 'orderSession')))
          .thenAnswer((_) => Future<void>.value());

      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      state.handleConfirmPayFailedState(ConfirmPaymentPromotionError(MockErrorUIModel()));

      verify(() => mockUpdateOrderCubit.updateSelectedVoucher(
            orderSession: any(named: 'orderSession'),
          )).called(1);
    });

    testWidgets('should show snack bar for ConfirmPaymentMissingPaymentMethod',
        (WidgetTester tester) async {
      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      final ErrorUIModel errorModel = MockErrorUIModel();
      when(() => errorModel.userMessage).thenReturn('missing payment method');

      state.handleConfirmPayFailedState(ConfirmPaymentMissingPaymentMethod(errorModel));

      verify(() => getIt.get<EvoSnackBar>().show(
            'missing payment method',
            typeSnackBar: SnackBarType.error,
            durationInSec: any(named: 'durationInSec'),
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          )).called(1);
    });

    testWidgets('should show bottom sheet for ConfirmPaymentFailedWhenOrderExpired',
        (WidgetTester tester) async {
      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      state.handleConfirmPayFailedState(ConfirmPaymentFailedWhenOrderExpired(ErrorUIModel()));

      verify(() => EvoDialogHelper().showDialogBottomSheet(
            content: EvoStrings.orderExpiredDescriptionBottomSheet,
            title: EvoStrings.orderExpiredTitleBottomSheet,
            dialogId: EvoDialogId.expiredOrderBottomSheet,
            textPositive: EvoStrings.doAgain,
            buttonListOrientation: any(named: 'buttonListOrientation'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            contentTextStyle: any(named: 'contentTextStyle'),
            onClickPositive: any(named: 'onClickPositive'),
            isDismissible: any(named: 'isDismissible'),
            textNegative: EvoStrings.moveToHome,
            onClickNegative: any(named: 'onClickNegative'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
          )).called(1);
    });

    testWidgets('should show bottom sheet for ConfirmAndPayTransactionTooSoon',
        (WidgetTester tester) async {
      when(() => EvoDialogHelper().showDialogBottomSheet(
            header: any(named: 'header'),
            title: any(named: 'title'),
            content: any(named: 'content'),
            dialogId: any(named: 'dialogId'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            textPositive: any(named: 'textPositive'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            contentTextStyle: any(named: 'contentTextStyle'),
            onClickPositive: any(named: 'onClickPositive'),
          )).thenAnswer((_) async {});

      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      state.handleConfirmPayFailedState(ConfirmAndPayTransactionTooSoon(ErrorUIModel()));

      verify(() => EvoDialogHelper().showDialogBottomSheet(
            header: any(named: 'header'),
            title: EvoStrings.transactionTooSoonBottomSheetTitle,
            content: any(named: 'content'),
            dialogId: EvoDialogId.transactionTooSoonBottomSheet,
            isShowButtonClose: any(named: 'isShowButtonClose'),
            textPositive: EvoStrings.understand,
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            contentTextStyle: any(named: 'contentTextStyle'),
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
    });

    testWidgets('should show bottom sheet for ConfirmAndPayInsufficientCreditLimit',
        (WidgetTester tester) async {
      when(() => EvoDialogHelper().showDialogBottomSheet(
            header: any(named: 'header'),
            title: any(named: 'title'),
            content: any(named: 'content'),
            dialogId: any(named: 'dialogId'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            textPositive: any(named: 'textPositive'),
            onClickPositive: any(named: 'onClickPositive'),
          )).thenAnswer((_) async {});

      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      state.handleConfirmPayFailedState(ConfirmAndPayInsufficientCreditLimit());

      verify(() => EvoDialogHelper().showDialogBottomSheet(
            header: any(named: 'header'),
            title: EvoStrings.creditLimitInsufficientBottomSheetTitle,
            content: EvoStrings.creditLimitInsufficientBottomSheetDescription,
            dialogId: EvoDialogId.creditLimitInsufficientBottomSheet,
            isShowButtonClose: any(named: 'isShowButtonClose'),
            textPositive: EvoStrings.moveToHome,
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
    });
  });

  group('confirmPayment tests', () {
    testWidgets('should disable button and call biometric confirmation',
        (WidgetTester tester) async {
      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      state.confirmPayment();

      verify(() => mockConfirmButtonCubit.onDisableButton()).called(1);
      verify(() => mockBiometricAndPinConfirmation.confirm(callback: state)).called(1);
    });
  });

  group('onBiometricConfirm and onPinInputConfirm tests', () {
    testWidgets('should call confirmAndPay with biometric token', (WidgetTester tester) async {
      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      state.onBiometricConfirm('test_token');

      verify(() => mockConfirmPaymentCubit.confirmAndPay(
            authenticateType: AuthenticateType.biometricToken,
            biometricToken: 'test_token',
            orderSession: any(named: 'orderSession'),
            emiPackage: any(named: 'emiPackage'),
            selectedVoucher: any(named: 'selectedVoucher'),
          )).called(1);
    });

    testWidgets('should call confirmAndPay with PIN', (WidgetTester tester) async {
      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      state.onPinInputConfirm('1234');

      verify(() => mockConfirmPaymentCubit.confirmAndPay(
            authenticateType: AuthenticateType.pin,
            pin: '1234',
            orderSession: any(named: 'orderSession'),
            emiPackage: any(named: 'emiPackage'),
            selectedVoucher: any(named: 'selectedVoucher'),
          )).called(1);
    });
  });

  group('handleManualLinkCardCubit tests', () {
    testWidgets('should show loading for ManualLinkCardLoadingState', (WidgetTester tester) async {
      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      state.handleManualLinkCardCubit(ManualLinkCardLoadingState());

      verify(() => EvoUiUtils().showHudLoading()).called(1);
    });

    testWidgets(
        'should navigate to DOPCardStatusScreen for LinkedCardStatusCheckingErrorState with verdict failure',
        (WidgetTester tester) async {
      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      final ErrorUIModel errorModel = MockErrorUIModel();
      when(() => errorModel.statusCode).thenReturn(CommonHttpClient.INTERNAL_SERVER_ERROR);
      when(() => errorModel.verdict).thenReturn(LinkedCardStatusVerdict.verdictFailureAll.value);

      state.handleManualLinkCardCubit(LinkedCardStatusCheckingErrorState(errorModel));
      verify(() => mockNavigatorContext.pushReplacementNamed(
            Screen.dopCardStatusScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('should call confirmPayment for PaymentMethodLoadedState',
        (WidgetTester tester) async {
      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      final OrderSessionEntity mockOrderSession = MockOrderSessionEntity();
      final OrderInfoUIModel orderInfoUIModel = OrderInfoUIModel(
        orderSession: mockOrderSession,
      );

      when(() => mockOrderInfoCubit.state)
          .thenReturn(OrderInfoUpdateState(orderInfoUIModel: orderInfoUIModel));

      state.handleManualLinkCardCubit(PaymentMethodLoadedState(order: mockOrderSession));

      verify(() => mockConfirmButtonCubit.onDisableButton()).called(1);
      verify(() => mockBiometricAndPinConfirmation.confirm(callback: state)).called(1);
    });

    testWidgets(
        'should call confirmPayment for PaymentMethodLoadedState and orderInfoCubit.state is OrderInfoUpdateInvalidVoucherState',
        (WidgetTester tester) async {
      final OrderSessionEntity mockOrderSession = MockOrderSessionEntity();
      final OrderInfoUIModel orderInfoUIModel = OrderInfoUIModel(
        orderSession: mockOrderSession,
      );

      when(() => EvoDialogHelper().showDialogBottomSheet(
            content: any(named: 'content'),
            title: any(named: 'title'),
            dialogId: any(named: 'dialogId'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            header: any(named: 'header'),
            buttonListOrientation: any(named: 'buttonListOrientation'),
            textPositive: any(named: 'textPositive'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            onClickNegative: any(named: 'onClickNegative'),
            onClickPositive: any(named: 'onClickPositive'),
            textNegative: any(named: 'textNegative'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
          )).thenAnswer((_) async {});

      when(() => mockOrderInfoCubit.state)
          .thenReturn(OrderInfoUpdateInvalidVoucherState(orderInfoUIModel: orderInfoUIModel));
      when(() => mockOrderInfoCubit.updateOrderInfo(
            order: any(named: 'order'),
            emiPackage: any(named: 'emiPackage'),
            selectedVoucher: any(named: 'selectedVoucher'),
          )).thenAnswer((_) => Future<void>.value());

      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      state.handleManualLinkCardCubit(PaymentMethodLoadedState(order: mockOrderSession));

      verify(() => mockOrderInfoCubit.updateOrderInfo(
            order: any(named: 'order'),
            emiPackage: any(named: 'emiPackage'),
            selectedVoucher: any(named: 'selectedVoucher'),
          )).called(1);

      verify(() => EvoDialogHelper().showDialogBottomSheet(
            content: EvoStrings.invalidPromotionBottomSheetDefaultError,
            title: EvoStrings.paymentPromotionApplyErrorTitle,
            dialogId: EvoDialogId.paymentInvalidPromotionBottomSheet,
            isShowButtonClose: any(named: 'isShowButtonClose'),
            header: any(named: 'header'),
            buttonListOrientation: any(named: 'buttonListOrientation'),
            textPositive: any(named: 'textPositive'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            onClickNegative: any(named: 'onClickNegative'),
            onClickPositive: any(named: 'onClickPositive'),
            textNegative: any(named: 'textNegative'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
          )).called(1);
    });

    testWidgets('should showManualLinkCardEncourageOfCheckout for PaymentMethodIsEmptyState',
        (WidgetTester tester) async {
      when(() => EvoDialogHelper().showDialogBottomSheet(
            content: any(named: 'content'),
            title: any(named: 'title'),
            dialogId: any(named: 'dialogId'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            header: any(named: 'header'),
            buttonListOrientation: any(named: 'buttonListOrientation'),
            textPositive: any(named: 'textPositive'),
            onClickPositive: any(named: 'onClickPositive'),
          )).thenAnswer((_) async {});

      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      state.handleManualLinkCardCubit(PaymentMethodIsEmptyState());

      verify(() => EvoDialogHelper().showDialogBottomSheet(
            content: EvoStrings.descriptionManualLinkCardCheckOut,
            title: EvoStrings.titleManualLinkCardCheckOut,
            dialogId: EvoDialogId.manualLinkCardEncourageBottomSheet,
            isShowButtonClose: true,
            header: any(named: 'header'),
            buttonListOrientation: any(named: 'buttonListOrientation'),
            textPositive: EvoStrings.manualLinkCardButtonTitleCheckOut,
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
    });
  });

  group('handleUpdateOrderWithPromotionListener tests', () {
    testWidgets('should show loading for UpdateOrderLoading state', (WidgetTester tester) async {
      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      state.handleUpdateOrderWithPromotionListener(UpdateOrderLoading());

      verify(() => EvoUiUtils().showHudLoading(loadingText: EvoStrings.hubLoadingText)).called(1);
    });

    testWidgets('should update order for UpdateOrderSuccess state', (WidgetTester tester) async {
      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      final OrderSessionEntity mockOrderSession = MockOrderSessionEntity();
      final VoucherEntity mockVoucher = MockVoucherEntity();
      final EmiPackageEntity mockEmiPackage = MockEmiPackageEntity();

      state.handleUpdateOrderWithPromotionListener(UpdateOrderSuccess(
        orderSession: mockOrderSession,
        selectedVoucher: mockVoucher,
        emiPackage: mockEmiPackage,
      ));

      verify(() => mockOrderInfoCubit.updateOrderInfo(
            order: mockOrderSession,
            emiPackage: mockEmiPackage,
            selectedVoucher: mockVoucher,
          )).called(1);
    });
  });

  group('removeVoucher tests', () {
    testWidgets('should call updateSelectedVoucher on updateOrderCubit',
        (WidgetTester tester) async {
      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      final OrderSessionEntity mockOrderSession = MockOrderSessionEntity();
      when(() => mockOrderInfoCubit.order).thenReturn(mockOrderSession);
      when(() =>
              mockUpdateOrderCubit.updateSelectedVoucher(orderSession: any(named: 'orderSession')))
          .thenAnswer((_) => Future<void>.value());

      state.removeVoucher();

      verify(() => mockUpdateOrderCubit.updateSelectedVoucher(
            orderSession: mockOrderSession,
          )).called(1);
    });
  });

  group('onConfirmPayButtonClick tests', () {
    testWidgets('should not call checkShowWaitingPopUp if button is disabled',
        (WidgetTester tester) async {
      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      final OrderSessionEntity mockOrderSession = MockOrderSessionEntity();

      state.onConfirmPayButtonClick(mockOrderSession, DisableState());

      verifyNever(() => mockConfirmPaymentCubit.checkShowWaitingPopUp(order: any(named: 'order')));
    });

    testWidgets('should call checkShowWaitingPopUp if button is enabled',
        (WidgetTester tester) async {
      when(() => mockConfirmPaymentCubit.checkShowWaitingPopUp(order: any(named: 'order')))
          .thenAnswer((_) => Future<void>.value());

      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      final OrderSessionEntity mockOrderSession = MockOrderSessionEntity();

      state.onConfirmPayButtonClick(mockOrderSession, EnableState());

      verify(() => mockConfirmPaymentCubit.checkShowWaitingPopUp(order: mockOrderSession))
          .called(1);
    });
  });

  group('handleUserBack tests', () {
    testWidgets('should clear selected voucher when user goes back', (WidgetTester tester) async {
      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      state.handleUserBack();

      verify(() => mockOrderInfoCubit.clearSelectedVoucher()).called(1);
    });
  });

  group('getDiscountAmount tests', () {
    test('should return null when no promotion or cashback', () {
      final TestConfirmPaymentScreenState state = TestConfirmPaymentScreenState();

      final OrderSessionEntity mockOrderSession = MockOrderSessionEntity();
      when(() => mockOrderSession.promotionAmount).thenReturn(0);
      when(() => mockOrderSession.promotionInfo).thenReturn(null);

      final String? result = state.getDiscountAmount(orderSession: mockOrderSession);

      expect(result, isNull);
    });

    test('should return formatted promotion amount when promotionAmount > 0', () {
      final TestConfirmPaymentScreenState state = TestConfirmPaymentScreenState();

      final OrderSessionEntity mockOrderSession = MockOrderSessionEntity();
      when(() => mockOrderSession.promotionAmount).thenReturn(10000);

      when(() => mockEvoUtilFunction.evoFormatCurrency(
            10000,
            currencySymbol: vietNamCurrencySymbol,
          )).thenReturn('10.000 ₫');

      final String? result = state.getDiscountAmount(orderSession: mockOrderSession);

      expect(result, '-10.000 ₫');
    });

    test('should return formatted cashback amount when cashbackAmount > 0 and feature enabled', () {
      final TestConfirmPaymentScreenState state = TestConfirmPaymentScreenState();

      final OrderSessionEntity mockOrderSession = MockOrderSessionEntity();
      final PromotionInfoEntity promotionInfo = PromotionInfoEntity(cashbackAmount: 5000);

      when(() => mockOrderSession.promotionAmount).thenReturn(0);
      when(() => mockOrderSession.promotionInfo).thenReturn(promotionInfo);
      when(() => mockFeatureToggle.enableInstantCashbackFeature).thenReturn(true);

      when(() => mockEvoUtilFunction.evoFormatCurrency(
            5000,
            currencySymbol: vietNamCurrencySymbol,
          )).thenReturn('5.000 ₫');

      final String? result = state.getDiscountAmount(orderSession: mockOrderSession);

      expect(result, '+5.000 ₫');
    });
  });

  group('buildPaymentDetailInfo tests', () {
    late MockPaymentMethodEntity mockPaymentMethod;
    late EmiPackageEntity mockEmiPackage;
    late MockOrderSessionEntity mockOrderSession;

    setUp(() {
      mockPaymentMethod = MockPaymentMethodEntity();
      mockOrderSession = MockOrderSessionEntity();
      mockEmiPackage = MockEmiPackageEntity();

      // Set up default mock behaviors
      when(() => mockManualLinkCardCubit.getDefaultPaymentMethod(order: any(named: 'order')))
          .thenReturn(mockPaymentMethod);
      when(() => mockPaymentMethod.sourceName).thenReturn('Test Card');
    });

    testWidgets(
        'returns RevampOutrightPurchaseSummaryInfoWidget when emiPackage is null and revamp UI is enabled',
        (WidgetTester tester) async {
      await tester.pumpWidget(testConfirmPaymentScreen);

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      // Act
      final Widget result = state.buildPaymentDetailInfo(
        orderSession: mockOrderSession,
      );

      // Build the widget
      await tester.pumpWidget(MaterialApp(home: Scaffold(body: result)));

      // Assert
      expect(find.byType(RevampOutrightPurchaseSummaryInfoWidget), findsOneWidget);
      expect(find.byType(RevampEmiSummaryInfoWidget), findsNothing);
      expect(find.byType(EmiSummaryInfoWidget), findsNothing);

      // Verify correct parameters are passed
      verify(() => mockManualLinkCardCubit.getDefaultPaymentMethod(order: mockOrderSession))
          .called(1);
    });

    testWidgets(
        'returns RevampEmiSummaryInfoWidget when emiPackage is provided and revamp UI is enabled',
        (WidgetTester tester) async {
      when(() => mockEmiPackage.monthlyInstallmentAmount).thenReturn(1000000);
      when(() => mockEmiPackage.conversionFee).thenReturn(50000);
      when(() => mockEmiPackage.outrightPurchaseDiff).thenReturn(150000);
      when(() => mockEmiPackage.offer).thenReturn(EmiTenorOfferEntity());

      await tester.pumpWidget(testConfirmPaymentScreen);
      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      // Act
      final Widget result = state.buildPaymentDetailInfo(
        orderSession: mockOrderSession,
        emiPackage: mockEmiPackage,
      );

      await tester.pumpWidget(MaterialApp(home: Scaffold(body: result)));

      // Assert
      expect(find.byType(RevampEmiSummaryInfoWidget), findsOneWidget);
      expect(find.byType(EmiSummaryInfoWidget), findsNothing);

      // because in RevampEmiSummaryInfoWidget, it contains RevampOutrightPurchaseSummaryInfoWidget
      expect(find.byType(RevampOutrightPurchaseSummaryInfoWidget), findsOneWidget);

      // Verify correct parameters are passed
      verify(() => mockManualLinkCardCubit.getDefaultPaymentMethod(order: mockOrderSession))
          .called(1);
    });
  });

  group('buildConfirmPayButton UI tests', () {
    late MockOrderSessionEntity mockOrderSession;
    late MockEmiPackageEntity mockEmiPackage;

    setUp(() {
      mockOrderSession = MockOrderSessionEntity();
      mockEmiPackage = MockEmiPackageEntity();
    });

    testWidgets('confirm payment button has correct text based on payment type',
        (WidgetTester tester) async {
      // Arrange
      when(() => mockConfirmButtonCubit.state).thenReturn(EnableState());
      final TestConfirmPaymentScreenState state = TestConfirmPaymentScreenState();

      // Test regular purchase button
      final Widget regularButton = state.buildConfirmPayButton(
        orderSession: mockOrderSession,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: MultiBlocProvider(
            providers: <BlocProvider<dynamic>>[
              BlocProvider<ConfirmButtonCubit>.value(value: mockConfirmButtonCubit),
            ],
            child: Scaffold(body: regularButton),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Assert - verify button text for regular purchase
      expect(find.text(EvoStrings.paymentSummaryPayment), findsOneWidget);

      // Test EMI purchase button
      when(() => mockEmiPackage.offer).thenReturn(MockEmiTenorOfferEntity());
      final Widget emiButton = state.buildConfirmPayButton(
        orderSession: mockOrderSession,
        emiPackage: mockEmiPackage,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: MultiBlocProvider(
            providers: <BlocProvider<dynamic>>[
              BlocProvider<ConfirmButtonCubit>.value(value: mockConfirmButtonCubit),
            ],
            child: Scaffold(body: emiButton),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Assert - verify button text for EMI purchase
      expect(find.text(EvoStrings.paymentEmiSummaryPayment), findsOneWidget);
    });
  });

  group('buildConfirmPayButton callback tests', () {
    test('should call onConfirmPayButtonClick with correct parameters', () {
      // Arrange
      final TestConfirmPaymentScreenState state = TestConfirmPaymentScreenState();
      final OrderSessionEntity mockOrderSession = MockOrderSessionEntity();

      // Mock the state and behavior
      when(() => mockConfirmButtonCubit.state).thenReturn(EnableState());
      when(() => mockConfirmPaymentCubit.checkShowWaitingPopUp(order: any(named: 'order')))
          .thenAnswer((_) => Future<void>.value());

      // Act - directly call the method we want to test
      state.onConfirmPayButtonClick(mockOrderSession, EnableState());

      // Assert
      verify(() => mockConfirmPaymentCubit.checkShowWaitingPopUp(order: mockOrderSession))
          .called(1);
    });

    test('should not call checkShowWaitingPopUp when button is disabled', () {
      // Arrange
      final TestConfirmPaymentScreenState state = TestConfirmPaymentScreenState();
      final OrderSessionEntity mockOrderSession = MockOrderSessionEntity();

      // Act - directly call the method with a disabled state
      state.onConfirmPayButtonClick(mockOrderSession, DisableState());

      // Assert
      verifyNever(() => mockConfirmPaymentCubit.checkShowWaitingPopUp(order: any(named: 'order')));
    });
  });

  group('test handleAPIError', () {
    test('status code is invalid token', () async {
      final TestConfirmPaymentScreenState state = TestConfirmPaymentScreenState();
      state.handleEvoApiError(ErrorUIModel(statusCode: CommonHttpClient.INVALID_TOKEN));

      verify(() => EvoAuthenticationHelper().clearDataOnTokenInvalid()).called(1);
      verifyNever(() => mockEvoSnackBar.show(
            any(),
            typeSnackBar: any(named: 'typeSnackBar'),
            durationInSec: any(named: 'durationInSec'),
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          ));
    });

    test('status code is other', () async {
      final TestConfirmPaymentScreenState state = TestConfirmPaymentScreenState();
      state.handleEvoApiError(ErrorUIModel(statusCode: CommonHttpClient.UNKNOWN_ERRORS));

      verifyNever(() => EvoAuthenticationHelper().clearDataOnTokenInvalid());

      verify(() => getIt.get<EvoSnackBar>().show(
            any(),
            typeSnackBar: SnackBarType.error,
            durationInSec: SnackBarDuration.short.value,
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          )).called(1);
    });
  });

  group('buildConfirmPaymentTitle tests', () {
    late MockOrderSessionEntity mockOrderSession;
    late MockEmiPackageEntity mockEmiPackage;
    late MockEmiTenorOfferEntity mockEmiTenorOffer;
    late TestConfirmPaymentScreenState state;

    setUp(() {
      mockOrderSession = MockOrderSessionEntity();
      mockEmiPackage = MockEmiPackageEntity();
      mockEmiTenorOffer = MockEmiTenorOfferEntity();
      state = TestConfirmPaymentScreenState();

      // Mock store info
      final MockStoreInfoEntity mockStoreInfo = MockStoreInfoEntity();
      when(() => mockStoreInfo.merchantName).thenReturn('Test Merchant');
      when(() => mockOrderSession.storeInfo).thenReturn(mockStoreInfo);

      // Mock order amount
      when(() => mockOrderSession.orderAmount).thenReturn(1000000);

      // Mock EMI offer
      when(() => mockEmiTenorOffer.tenor).thenReturn(6);
      when(() => mockEmiPackage.offer).thenReturn(mockEmiTenorOffer);
    });

    testWidgets('returns OutrightPurchasePaymentTitle when emiPackage is null',
        (WidgetTester tester) async {
      // Act
      final Widget result = state.buildConfirmPaymentTitle(
        orderSession: mockOrderSession,
      );

      // Build the widget
      await tester.pumpWidget(MaterialApp(home: Scaffold(body: result)));

      // Assert
      expect(find.byType(OutrightPurchasePaymentTitle), findsOneWidget);
      expect(find.byType(EmiPaymentTitle), findsNothing);

      // Verify correct parameters are passed
      final OutrightPurchasePaymentTitle widget =
          tester.widget(find.byType(OutrightPurchasePaymentTitle));
      expect(widget.merchantStoreName, 'Test Merchant');
    });

    testWidgets('returns EmiPaymentTitle when emiPackage is provided', (WidgetTester tester) async {
      // Act
      final Widget result = state.buildConfirmPaymentTitle(
        orderSession: mockOrderSession,
        emiPackage: mockEmiPackage,
      );

      // Build the widget
      await tester.pumpWidget(MaterialApp(home: Scaffold(body: result)));

      // Assert
      expect(find.byType(EmiPaymentTitle), findsOneWidget);
      expect(find.byType(OutrightPurchasePaymentTitle), findsNothing);

      // Verify correct parameters are passed
      final EmiPaymentTitle widget = tester.widget(find.byType(EmiPaymentTitle));
      expect(widget.title, EvoStrings.emiCheckoutUWantEMIPaymentTitle);
      expect(widget.merchantStoreName, 'Test Merchant');
      expect(widget.amount, 1000000);
      expect(widget.tenor, 6);
    });
  });

  group('buildBody tests', () {
    late MockOrderSessionEntity mockOrderSession;
    late MockEmiPackageEntity mockEmiPackage;
    late MockVoucherEntity mockVoucher;
    late TestConfirmPaymentScreenState state;

    setUp(() {
      mockOrderSession = MockOrderSessionEntity();
      mockEmiPackage = MockEmiPackageEntity();
      mockVoucher = MockVoucherEntity();
      state = TestConfirmPaymentScreenState();

      // Set up default mock behaviors for orderSession
      final MockStoreInfoEntity mockStoreInfo = MockStoreInfoEntity();
      when(() => mockStoreInfo.merchantName).thenReturn('Test Merchant');
      when(() => mockOrderSession.storeInfo).thenReturn(mockStoreInfo);
      when(() => mockOrderSession.orderAmount).thenReturn(1000000);
      when(() => mockOrderSession.userChargeAmount).thenReturn(950000);
      when(() => mockOrderSession.promotionAmount).thenReturn(850000);

      // Set up default mock behaviors for emiPackage
      final MockEmiTenorOfferEntity mockEmiTenorOffer = MockEmiTenorOfferEntity();
      when(() => mockEmiPackage.offer).thenReturn(mockEmiTenorOffer);
      when(() => mockEmiTenorOffer.tenor).thenReturn(3);
    });

    tearDown(() {
      reset(mockFeatureToggle);
    });

    testWidgets('should build body correctly with no parameters', (WidgetTester tester) async {
      // Act
      final Widget result = state.buildBody();

      // Build the widget with BlocProvider for ConfirmButtonCubit
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<ConfirmButtonCubit>.value(
            value: mockConfirmButtonCubit,
            child: Scaffold(body: result),
          ),
        ),
      );

      // Assert - verify the structure
      expect(find.byType(Column), findsWidgets);
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      expect(find.byType(SizedBox), findsWidgets);
    });

    testWidgets('should build body correctly with orderSession', (WidgetTester tester) async {
      // Act
      final Widget result = state.buildBody(orderSession: mockOrderSession);

      // Build the widget with BlocProvider for ConfirmButtonCubit
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<ConfirmButtonCubit>.value(
            value: mockConfirmButtonCubit,
            child: Scaffold(body: result),
          ),
        ),
      );

      // Assert - verify the structure and components
      expect(find.byType(Column), findsWidgets);
      expect(find.byType(SingleChildScrollView), findsOneWidget);

      // Verify that buildConfirmPaymentTitle was called with correct parameters
      verify(() => mockOrderSession.storeInfo?.merchantName).called(greaterThanOrEqualTo(1));

      // Verify that buildTotalOrderAmountWidget was called with correct parameters
      verify(() => mockOrderSession.userChargeAmount).called(greaterThanOrEqualTo(1));
    });

    testWidgets('should build body correctly with orderSession and emiPackage',
        (WidgetTester tester) async {
      // Act
      final Widget result = state.buildBody(
        orderSession: mockOrderSession,
        emiPackage: mockEmiPackage,
      );

      // Build the widget with BlocProvider for ConfirmButtonCubit
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<ConfirmButtonCubit>.value(
            value: mockConfirmButtonCubit,
            child: Scaffold(body: result),
          ),
        ),
      );

      // Assert - verify the structure and components
      expect(find.byType(Column), findsWidgets);
      expect(find.byType(SingleChildScrollView), findsOneWidget);

      // Verify that buildConfirmPaymentTitle was called with correct parameters for EMI
      verify(() => mockOrderSession.storeInfo?.merchantName).called(greaterThanOrEqualTo(1));
      verify(() => mockEmiPackage.offer?.tenor).called(greaterThanOrEqualTo(1));

      // Verify that buildTotalOrderAmountWidget was called with correct parameters
      verify(() => mockOrderSession.userChargeAmount).called(greaterThanOrEqualTo(1));
    });

    testWidgets('should build body correctly with all parameters including voucher',
        (WidgetTester tester) async {
      // Act
      final Widget result = state.buildBody(
        orderSession: mockOrderSession,
        emiPackage: mockEmiPackage,
        selectedVoucher: mockVoucher,
        voucherSelectionState: VoucherSelectionState.validVoucher,
      );

      // Build the widget with BlocProvider for ConfirmButtonCubit
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<ConfirmButtonCubit>.value(
            value: mockConfirmButtonCubit,
            child: Scaffold(body: result),
          ),
        ),
      );

      // Assert - verify the structure and components
      expect(find.byType(Column), findsWidgets);
      expect(find.byType(SingleChildScrollView), findsOneWidget);

      // Verify that all the necessary components are included
      verify(() => mockOrderSession.storeInfo?.merchantName).called(greaterThanOrEqualTo(1));
      verify(() => mockEmiPackage.offer?.tenor).called(greaterThanOrEqualTo(1));
      verify(() => mockOrderSession.userChargeAmount).called(greaterThanOrEqualTo(1));
    });
  });

  group('test handlePaymentPromotionSelectionError', () {
    testWidgets('handlePaymentPromotionSelectionError should handle UpdateOrderError correctly',
        (WidgetTester tester) async {
      // Arrange
      final TestConfirmPaymentScreenState state = TestConfirmPaymentScreenState();

      // Act
      state.handlePaymentPromotionSelectionError(UpdateOrderError(MockErrorUIModel()));

      // Assert - because mockError is not CommonHttpClient.INVALID_TOKEN
      verifyNever(() => EvoAuthenticationHelper().clearDataOnTokenInvalid());
      verify(() => getIt.get<EvoSnackBar>().show(
            any(),
            typeSnackBar: SnackBarType.error,
            durationInSec: SnackBarDuration.short.value,
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          )).called(1);
    });

    testWidgets('handlePaymentPromotionSelectionError should handle UpdateOrderExpired correctly',
        (WidgetTester tester) async {
      final TestConfirmPaymentScreenState state = TestConfirmPaymentScreenState();

      // Act
      state.handlePaymentPromotionSelectionError(UpdateOrderExpired(error: MockErrorUIModel()));

      // Assert
      verify(() => EvoDialogHelper().showDialogBottomSheet(
            content: any(named: 'content'),
            title: any(named: 'title'),
            dialogId: EvoDialogId.expiredOrderBottomSheet,
            textPositive: any(named: 'textPositive'),
            buttonListOrientation: any(named: 'buttonListOrientation'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            contentTextStyle: any(named: 'contentTextStyle'),
            onClickPositive: any(named: 'onClickPositive'),
            isDismissible: any(named: 'isDismissible'),
            textNegative: any(named: 'textNegative'),
            onClickNegative: any(named: 'onClickNegative'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
          )).called(1);
    });
  });

  group('onConfirmPinPopupClosed', () {
    test('should enable button', () {
      when(() => mockConfirmButtonCubit.onEnableButton()).thenAnswer((_) => Future<void>.value());

      final TestConfirmPaymentScreenState state = TestConfirmPaymentScreenState();

      // Act
      state.onConfirmPinPopupClosed();

      // Assert
      verify(() => mockConfirmButtonCubit.onEnableButton()).called(1);
    });
  });

  group('onApplyVoucherChange', () {
    test('should call updateOrder', () {
      when(() => mockOrderInfoCubit.updateOrderInfo(
            order: any(named: 'order'),
            emiPackage: any(named: 'emiPackage'),
            selectedVoucher: any(named: 'selectedVoucher'),
          )).thenAnswer((_) => Future<void>.value());

      final TestConfirmPaymentScreenState state = TestConfirmPaymentScreenState();

      state.onApplyVoucherChange(PaymentPromotionResultArg(
        orderSession: MockOrderSessionEntity(),
        currentVoucherSelected: MockVoucherEntity(),
        emiPackage: MockEmiPackageEntity(),
      ));

      verify(() => mockOrderInfoCubit.updateOrderInfo(
            order: any(named: 'order'),
            emiPackage: any(named: 'emiPackage'),
            selectedVoucher: any(named: 'selectedVoucher'),
          )).called(1);
    });
  });

  group('hasValidVoucherChange', () {
    test('should update hasValidVoucher in orderInfoCubit', () {
      // Arrange
      final TestConfirmPaymentScreenState state = TestConfirmPaymentScreenState();

      // Act
      state.hasValidVoucherChange(true);

      // Assert
      verify(() => mockOrderInfoCubit.hasValidVoucher = true).called(1);

      // Act again with different value
      state.hasValidVoucherChange(false);

      // Assert again
      verify(() => mockOrderInfoCubit.hasValidVoucher = false).called(1);
    });
  });

  group('displayInvalidPromotionPopup', () {
    testWidgets('should show popup with error message when provided', (WidgetTester tester) async {
      // Arrange
      when(() => EvoDialogHelper().showDialogBottomSheet(
            content: any(named: 'content'),
            title: any(named: 'title'),
            dialogId: any(named: 'dialogId'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            header: any(named: 'header'),
            buttonListOrientation: any(named: 'buttonListOrientation'),
            textPositive: any(named: 'textPositive'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            onClickNegative: any(named: 'onClickNegative'),
            onClickPositive: any(named: 'onClickPositive'),
            textNegative: any(named: 'textNegative'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
          )).thenAnswer((_) async {});

      when(() => mockNavigatorContext.screenWidth).thenReturn(1000);

      await tester.pumpWidget(MaterialApp(
        home: Builder(builder: (BuildContext context) {
          return const TestConfirmPaymentScreen();
        }),
      ));

      final TestConfirmPaymentScreenState state =
          tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

      // Act
      state.displayInvalidPromotionPopup(error: MockErrorUIModel());

      // Assert
      verify(() => EvoDialogHelper().showDialogBottomSheet(
            content: any(named: 'content'),
            title: EvoStrings.paymentPromotionApplyErrorTitle,
            dialogId: EvoDialogId.paymentInvalidPromotionBottomSheet,
            isShowButtonClose: any(named: 'isShowButtonClose'),
            header: any(named: 'header'),
            buttonListOrientation: any(named: 'buttonListOrientation'),
            textPositive: any(named: 'textPositive'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            onClickNegative: any(named: 'onClickNegative'),
            onClickPositive: any(named: 'onClickPositive'),
            textNegative: EvoStrings.paymentPromotionApplyErrorSelectOtherPromotion,
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
          )).called(1);
    });
  });

  group('ConfirmPaymentScreenStateBase class variables', () {
    late TestConfirmPaymentScreenState state;

    setUp(() {
      state = TestConfirmPaymentScreenState();
    });

    test('cubit should be initialized correctly', () {
      expect(state.cubit, isA<ConfirmPaymentCubit>());
      expect(state.cubit, equals(getIt<ConfirmPaymentCubit>()));
    });

    test('confirmButtonCubit should be initialized correctly', () {
      expect(state.confirmButtonCubit, isA<ConfirmButtonCubit>());
      expect(state.confirmButtonCubit, equals(getIt<ConfirmButtonCubit>()));
    });

    test('biometricAndPinConfirmation should be initialized correctly', () {
      expect(state.biometricAndPinConfirmation, isA<BiometricAndPinConfirmation>());
      expect(state.biometricAndPinConfirmation, equals(getIt<BiometricAndPinConfirmation>()));
    });

    test('updateOrderCubit should be initialized correctly', () {
      expect(state.updateOrderCubit, isA<UpdateOrderCubit>());
      expect(state.updateOrderCubit, equals(getIt<UpdateOrderCubit>()));
    });

    test('orderInfoCubit should be initialized correctly', () {
      expect(state.orderInfoCubit, isA<OrderInfoCubit>());
      expect(state.orderInfoCubit, equals(getIt<OrderInfoCubit>()));
    });

    test('manualLinkCardCubit should be initialized correctly', () {
      expect(state.manualLinkCardCubit, isA<ManualLinkCardCubit>());
      expect(state.manualLinkCardCubit, equals(getIt<ManualLinkCardCubit>()));
    });

    test('snackBarMarginBottomRatio should be null initially', () {
      expect(state.snackBarMarginBottomRatio, isNull);
    });

    test('discountAmount should be initialized as ValueNotifier with null value', () {
      expect(state.discountAmount, isA<ValueNotifier<String?>>());
      expect(state.discountAmount.value, isNull);
    });

    test('discountAmount can be updated and notifies listeners', () {
      bool listenerCalled = false;
      state.discountAmount.addListener(() {
        listenerCalled = true;
      });

      state.discountAmount.value = '-10.000 ₫';

      expect(state.discountAmount.value, equals('-10.000 ₫'));
      expect(listenerCalled, isTrue);
    });
  });

  group('getContentWidget', () {
    late TestConfirmPaymentScreenStateMockBuildBody state;
    late MockOrderInfoUIModel mockOrderInfoUIModel;
    late MockOrderSessionEntity mockOrderSessionEntity;

    setUp(() {
      mockOrderInfoUIModel = MockOrderInfoUIModel();
      mockOrderSessionEntity = MockOrderSessionEntity();
      state = TestConfirmPaymentScreenStateMockBuildBody();

      when(() => mockOrderInfoUIModel.orderSession).thenReturn(mockOrderSessionEntity);
      when(() => mockOrderInfoUIModel.emiPackage).thenReturn(null);
      when(() => mockOrderInfoUIModel.selectedVoucher).thenReturn(null);
      when(() => mockOrderInfoUIModel.voucherSelectionState)
          .thenReturn(VoucherSelectionState.noSelect);
    });

    testWidgets('should render MultiBlocProvider with correct providers',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (BuildContext context) => state.getContentWidget(context),
          ),
        ),
      );

      expect(find.byType(MultiBlocProvider), findsOneWidget);
      expect(find.byType(MultiBlocListener), findsOneWidget);
    });

    testWidgets('should render SizedBox.shrink when order is null', (WidgetTester tester) async {
      when(() => mockOrderInfoCubit.state).thenReturn(OrderInfoUpdateInitial());

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (BuildContext context) => state.getContentWidget(context),
          ),
        ),
      );

      await tester.pump();

      expect(find.byType(Column), findsNothing);
    });

    testWidgets('should call buildBody when order is available in OrderInfoUpdateState',
        (WidgetTester tester) async {
      // Mock buildBody to return a simple widget
      when(() => mockOrderInfoCubit.state)
          .thenReturn(OrderInfoUpdateState(orderInfoUIModel: mockOrderInfoUIModel));

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (BuildContext context) => state.getContentWidget(context),
          ),
        ),
      );

      // Wait for bloc builder to complete
      await tester.pump();

      // Verify that buildBody was called and its returned widget is rendered
      expect(find.text('Build Body Called'), findsOneWidget);
      expect(state.isBuildBodyCalled, isTrue);
    });

    testWidgets(
        'should call buildBody when order is available in OrderInfoUpdateInvalidVoucherState',
        (WidgetTester tester) async {
      // Mock OrderInfoUpdateInvalidVoucherState
      when(() => mockOrderInfoCubit.state)
          .thenReturn(OrderInfoUpdateInvalidVoucherState(orderInfoUIModel: mockOrderInfoUIModel));

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (BuildContext context) => state.getContentWidget(context),
          ),
        ),
      );

      // Wait for bloc builder to complete
      await tester.pump();

      expect(find.text('Build Body Called'), findsOneWidget);
      expect(state.isBuildBodyCalled, isTrue);
    });
  });

  group('getSnackBarBottomPadding', () {
    test(
        'getSnackBarBottomPadding should not update snackBarMarginBottomRatio if it is already set',
        () {
      // Given
      final TestConfirmPaymentScreenStateTestGetSnackBarBottomPadding state =
          TestConfirmPaymentScreenStateTestGetSnackBarBottomPadding();
      final Offset voucherOffset = Offset(10, 20);
      state.snackBarMarginBottomRatio = 0.3;

      // When
      state.getSnackBarBottomPadding(voucherOffset);

      // Then
      verifyNever(
          () => EvoUiUtils().calculateVerticalOffsetRatio(mockNavigatorContext, voucherOffset));
      expect(state.snackBarMarginBottomRatio, 0.3); // Value should remain unchanged
    });
  });

  testWidgets('onPromotionSelectionError', (WidgetTester tester) async {
    // Arrange

    when(() => EvoDialogHelper().showDialogBottomSheet(
          content: any(named: 'content'),
          title: any(named: 'title'),
          dialogId: any(named: 'dialogId'),
          isShowButtonClose: any(named: 'isShowButtonClose'),
          header: any(named: 'header'),
          buttonListOrientation: any(named: 'buttonListOrientation'),
          textPositive: any(named: 'textPositive'),
          positiveButtonStyle: any(named: 'positiveButtonStyle'),
          onClickNegative: any(named: 'onClickNegative'),
          onClickPositive: any(named: 'onClickPositive'),
          textNegative: any(named: 'textNegative'),
          negativeButtonStyle: any(named: 'negativeButtonStyle'),
        )).thenAnswer((_) async {});

    when(() => mockNavigatorContext.screenWidth).thenReturn(1000);

    await tester.pumpWidget(MaterialApp(
      home: TestConfirmPaymentScreen(),
    ));

    final TestConfirmPaymentScreenState state =
        tester.state(find.byType(TestConfirmPaymentScreen)) as TestConfirmPaymentScreenState;

    // Act
    state.onPromotionSelectionError(MockErrorUIModel());

    verify(() => EvoDialogHelper().showDialogBottomSheet(
          content: any(named: 'content'),
          title: EvoStrings.paymentPromotionApplyErrorTitle,
          dialogId: EvoDialogId.paymentInvalidPromotionBottomSheet,
          isShowButtonClose: any(named: 'isShowButtonClose'),
          header: any(named: 'header'),
          buttonListOrientation: any(named: 'buttonListOrientation'),
          textPositive: any(named: 'textPositive'),
          positiveButtonStyle: any(named: 'positiveButtonStyle'),
          onClickNegative: any(named: 'onClickNegative'),
          onClickPositive: any(named: 'onClickPositive'),
          textNegative: EvoStrings.paymentPromotionApplyErrorSelectOtherPromotion,
          negativeButtonStyle: any(named: 'negativeButtonStyle'),
        )).called(1);
  });

  group('redirectToOrderCreationScreenIfNeed', () {
    final MockAppState mockAppState = MockAppState();

    setUp(() {
      if (getIt.isRegistered<AppState>()) {
        getIt.unregister<AppState>();
        getIt.registerSingleton<AppState>(mockAppState);
      }
    });

    tearDown(() {
      reset(mockAppState);
    });

    tearDownAll(() {
      if (getIt.isRegistered<AppState>()) {
        getIt.unregister<AppState>();
        getIt.registerSingleton<AppState>(AppState());
      }
    });

    test('redirectToOrderCreationScreenIfNeed should call pop if orderCreationScreen is null', () {
      final MockAppState mockAppState = MockAppState();
      if (getIt.isRegistered<AppState>()) {
        getIt.unregister<AppState>();
        getIt.registerSingleton<AppState>(mockAppState);
      }

      when(() => mockAppState.paymentSharedData)
          .thenReturn(PaymentSharedData()..orderCreationScreen = null);
      final TestConfirmPaymentScreenState state = TestConfirmPaymentScreenState();
      state.redirectToOrderCreationScreenIfNeed(100);
      verify(() => mockNavigatorContext.pop()).called(1);
    });

    test('redirectToOrderCreationScreenIfNeed should call pop if orderCreationScreen is null', () {
      final Screen testScreenName = Screen.cloneConfirmPayment;

      when(() => mockAppState.paymentSharedData)
          .thenReturn(PaymentSharedData()..orderCreationScreen = testScreenName);

      final TestConfirmPaymentScreenState state = TestConfirmPaymentScreenState();
      state.redirectToOrderCreationScreenIfNeed(100);

      verify(() => mockNavigatorContext.popUntilNamed(testScreenName.name)).called(1);
    });
  });

  group('buildPromotionWidget', () {
    late TestConfirmPaymentScreenState state;
    late MockVoucherEntity mockVoucher;

    setUp(() {
      state = TestConfirmPaymentScreenState();
      mockVoucher = MockVoucherEntity();
      state.discountAmount.value = '-10.000 ₫';
    });

    testWidgets('should return RevampPaymentPromotionWidget when revamp UI is enabled',
        (WidgetTester tester) async {
      // Act
      final Widget widget = state.buildPromotionWidget(
        selectedVoucher: mockVoucher,
        voucherSelectionState: VoucherSelectionState.validVoucher,
      );

      // Assert
      expect(widget, isA<ValueListenableBuilder<String?>>());

      // Render widget to verify ValueListenableBuilder behavior
      await tester.pumpWidget(MaterialApp(home: widget));
      expect(find.byType(RevampPaymentPromotionWidget), findsOneWidget);

      final RevampPaymentPromotionWidget revampWidget =
          tester.widget(find.byType(RevampPaymentPromotionWidget));
      expect(revampWidget.selectedPromotion, equals(mockVoucher));
      expect(revampWidget.voucherSelectionState, equals(VoucherSelectionState.validVoucher));
      expect(revampWidget.discount, equals('-10.000 ₫'));
    });

    testWidgets('should pass null discount when discount amount is empty',
        (WidgetTester tester) async {
      // Arrange
      state.discountAmount.value = '';

      // Act
      final Widget widget = state.buildPromotionWidget(
        selectedVoucher: mockVoucher,
      );

      // Assert
      await tester.pumpWidget(MaterialApp(home: widget));
      final RevampPaymentPromotionWidget revampWidget =
          tester.widget(find.byType(RevampPaymentPromotionWidget));
      expect(revampWidget.discount, isNull);
    });
  });

  test('updateOrderInfoWithInvalidVoucher', () {
    when(() => mockOrderInfoCubit.updateOrderInfoWithInvalidVoucher(
          order: any(named: 'order'),
          emiPackage: any(named: 'emiPackage'),
          selectedVoucher: any(named: 'selectedVoucher'),
          errorUIModel: any(named: 'errorUIModel'),
        )).thenAnswer((_) => Future<void>.value());

    final TestConfirmPaymentScreenState state = TestConfirmPaymentScreenState();

    state.updateOrderInfoWithInvalidVoucher(
      orderSession: MockOrderSessionEntity(),
      emiPackageEntity: MockEmiPackageEntity(),
      selectedVoucher: MockVoucherEntity(),
      errorUIModel: MockErrorUIModel(),
    );

    verify(() => mockOrderInfoCubit.updateOrderInfoWithInvalidVoucher(
          order: any(named: 'order'),
          emiPackage: any(named: 'emiPackage'),
          selectedVoucher: any(named: 'selectedVoucher'),
          errorUIModel: any(named: 'errorUIModel'),
        )).called(1);
  });

  test('showSnackBarWarning', () {
    final TestConfirmPaymentScreenState state = TestConfirmPaymentScreenState();
    state.showSnackBarWarning('test');

    verify(() => getIt.get<EvoSnackBar>().show(
          'test',
          typeSnackBar: SnackBarType.warning,
          durationInSec: SnackBarDuration.short.value,
          description: any(named: 'description'),
          marginBottomRatio: any(named: 'marginBottomRatio'),
        )).called(1);
  });
}
