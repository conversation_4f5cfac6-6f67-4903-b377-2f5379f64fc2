import 'package:evoapp/feature/payment/widget/markdown_bullet_text/revamp_markdown_bullet_text_item.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_markdown_plus/flutter_markdown_plus.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String testText = 'This is a test bullet item';

  setUpAll(() {
    getIt.registerLazySingleton(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
  });

  testWidgets('RevampMarkDownBulletTextItem renders correctly without dash line',
      (WidgetTester tester) async {
    // Build the widget
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: RevampMarkDownBulletTextItem(
            text: testText,
          ),
        ),
      ),
    );

    // Verify the Markdown widget
    final Finder markdownFinder = find.byType(Markdown);
    expect(markdownFinder, findsOneWidget);

    final Markdown markdown = tester.widget(markdownFinder);
    expect(markdown.data, testText);
    expect(markdown.styleSheet?.p,
        evoTextStyles.bodyMedium(evoColors.textPassive).copyWith(height: 1.42));
    expect(markdown.styleSheet?.strong,
        evoTextStyles.h200(color: evoColors.textActive).copyWith(height: 1.42));
    expect(markdown.physics, const NeverScrollableScrollPhysics());
    expect(markdown.padding, EdgeInsets.zero);
    expect(markdown.shrinkWrap, true);

    // Verify the dash line is not shown
    expect(find.byType(SizedBox), findsOneWidget);
    expect(
        find.byType(Container).evaluate().any((Element element) {
          final Container container = element.widget as Container;
          return container.color == EvoColors().divider;
        }),
        isFalse);
  });

  testWidgets('RevampMarkDownBulletTextItem renders correctly with dash line',
      (WidgetTester tester) async {
    // Build the widget
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: RevampMarkDownBulletTextItem(
            text: testText,
            isShowDashLine: true,
          ),
        ),
      ),
    );

    // Verify the dash line is shown
    final Finder dashLineFinder = find.byWidgetPredicate((Widget widget) {
      if (widget is Container) {
        return widget.color == EvoColors().divider;
      }
      return false;
    });
    expect(dashLineFinder, findsOneWidget);

    // Verify the Markdown widget
    final Finder markdownFinder = find.byType(Markdown);
    expect(markdownFinder, findsOneWidget);

    final Markdown markdown = tester.widget(markdownFinder);
    expect(markdown.data, testText);
  });
}
