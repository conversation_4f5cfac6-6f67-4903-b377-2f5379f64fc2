import 'package:evoapp/data/repository/checkout_repo.dart';
import 'package:evoapp/data/response/create_order_entity.dart';
import 'package:evoapp/data/response/emi_package_entity.dart';
import 'package:evoapp/data/response/emi_tenor_offer_entity.dart';
import 'package:evoapp/data/response/update_order_entity.dart';
import 'package:evoapp/data/response/voucher_entity.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/payment/emi_option_screen/tenor_detail_info_bottom_sheet.dart';
import 'package:evoapp/feature/payment/models/payment_entry_point.dart';
import 'package:evoapp/feature/payment/payment_shared_data.dart';
import 'package:evoapp/feature/payment/utils/payment_with_emi_utils.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:evoapp/widget/evo_dialog/evo_dialog_remind_enable_pos_limit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_utils.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

class MockPayWithEMIUtils extends PaymentWithEMIUtils {
  String productCodeParam = '';
  int amountParam = 0;
  String merchantIdParam = '';
  int checkIfProductCodeValidCount = 0;

  @override
  bool checkIfOrderValidForEMI(
      {required String productCode, required int amount, required String merchantId}) {
    productCodeParam = productCode;
    amountParam = amount;
    merchantIdParam = merchantId;
    return true;
  }

  @override
  bool checkIfProductCodeValid(String? productCode) {
    checkIfProductCodeValidCount++;
    return super.checkIfProductCodeValid(productCode);
  }

  void resetMethodCallCount() {
    checkIfProductCodeValidCount = 0;
  }
}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockBuildContext extends Mock implements BuildContext {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockFeatureToggle extends Mock implements FeatureToggle {}

class MockEventTrackingUtils extends Mock implements EventTrackingUtils {}

void main() {
  late EvoUtilFunction mockEvoUtilFunction;
  late PaymentWithEMIUtils payWithEMIUtils;
  late AppState appState;
  late CommonImageProvider commonImageProvider;
  late EvoLocalStorageHelper localStorageHelper;
  late FeatureToggle mockFeatureToggle;

  const double expectSize = 10.0;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(MockBuildContext());

    getIt.registerLazySingleton(() => AppState());
    appState = getIt.get<AppState>();

    getIt.registerSingleton<GlobalKeyProvider>(GlobalKeyProvider());

    getItRegisterColor();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterTextStyle();
    getItRegisterButtonStyle();
    setUtilsMockInstanceForTesting();
    mockEvoUtilFunction = getIt.get<EvoUtilFunction>();

    getIt.registerSingleton<EvoLocalStorageHelper>(MockEvoLocalStorageHelper());
    localStorageHelper = getIt.get<EvoLocalStorageHelper>();

    getIt.registerSingleton<FeatureToggle>(MockFeatureToggle());
    mockFeatureToggle = getIt.get<FeatureToggle>();

    when(() => mockFeatureToggle.enableEventTrackingFeature).thenReturn(true);

    commonImageProvider = getIt.get<CommonImageProvider>();
    when(() => commonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());

    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenReturn(expectSize);
    when(() => EvoUiUtils().calculateHorizontalSpace(
          context: any(named: 'context'),
          widthPercentage: any(named: 'widthPercentage'),
        )).thenReturn(expectSize);
  });

  setUp(() {
    payWithEMIUtils = PaymentWithEMIUtils();

    when(() => localStorageHelper.getEnablePosLimitWarning())
        .thenAnswer((_) => Future<bool>.value(true));

    when(() => localStorageHelper.setEnablePosLimitWarning(any()))
        .thenAnswer((_) => Future<void>.value());
  });

  tearDown(() {
    reset(localStorageHelper);
  });

  tearDownAll(() {
    getIt.reset();
    resetUtilMockToOriginalInstance();
  });

  group('verify canPayWithEMI()', () {
    late MockPayWithEMIUtils mockPayWithEMIUtils;

    setUp(() {
      mockPayWithEMIUtils = MockPayWithEMIUtils();
    });

    test('has call check rule EMI', () {
      const String expectProductCode = 'productCode';
      const int expectAmount = 1000;
      const String expectMerchantId = 'merchantId';

      final bool result =
          mockPayWithEMIUtils.canPayWithEMI(expectProductCode, expectAmount, expectMerchantId);
      expect(result, true);
      expect(mockPayWithEMIUtils.productCodeParam, expectProductCode);
      expect(mockPayWithEMIUtils.amountParam, expectAmount);
      expect(mockPayWithEMIUtils.merchantIdParam, expectMerchantId);
    });
  });

  group('verify getAmountFromStrWithCurrency()', () {
    const String amount = '1000';
    const int expectedAmount = 1000;
    const String expectedCurrency = 'đ';

    setUp(() {
      when(() => mockEvoUtilFunction.getAmountFromStr(
            any(),
            currencySuffix: any(named: 'currencySuffix'),
          )).thenReturn(expectedAmount);
    });

    test('Give amount without currency, should right amount', () {
      final int? result = payWithEMIUtils.getAmountFromStrWithCurrency(amount);
      expect(result, expectedAmount);
      verify(() => mockEvoUtilFunction.getAmountFromStr(
            amount,
            currencySuffix: vietNamCurrencySymbol,
          )).called(1);
    });

    test('Give amount with currency, should right amount', () {
      const String fakeAmount = '$amount$expectedCurrency';
      final int? result = payWithEMIUtils.getAmountFromStrWithCurrency(fakeAmount);
      expect(result, expectedAmount);
      verify(() => mockEvoUtilFunction.getAmountFromStr(
            fakeAmount,
            currencySuffix: vietNamCurrencySymbol,
          )).called(1);
    });
  });

  group('verify isUserWantPayWithEmi()', () {
    tearDownAll(() {
      appState.paymentSharedData.clearAll();
    });

    test('verify isUserWantPayWithEmi() return true', () {
      appState.paymentSharedData.paymentEntryPoint = PaymentEntryPoint.paymentWithEMI;
      final bool isResult = payWithEMIUtils.isUserWantPayWithEmi();

      expect(isResult, true);
    });

    test('verify isUserWantPayWithEmi() return false', () {
      appState.paymentSharedData.paymentEntryPoint = PaymentEntryPoint.voucherDetailScreen;
      final bool isResult = payWithEMIUtils.isUserWantPayWithEmi();

      expect(isResult, false);
    });
  });

  group('verify getPaymentService()', () {
    tearDownAll(() {
      appState.paymentSharedData.clearAll();
    });

    test('verify getPaymentService() return null', () {
      final PaymentService? paymentService = payWithEMIUtils.getPaymentService();

      expect(paymentService, isNull);
    });

    test('verify getPaymentService() return value correctly', () {
      expect(payWithEMIUtils.getPaymentService(), isNull);

      appState.paymentSharedData.selectedPaymentService = PaymentService.outrightPurchase;
      expect(payWithEMIUtils.getPaymentService(), PaymentService.outrightPurchase);

      appState.paymentSharedData.selectedPaymentService = PaymentService.emi;
      expect(payWithEMIUtils.getPaymentService(), PaymentService.emi);
    });

    test('verify getPaymentEntryPoint() return value correctly', () {
      expect(payWithEMIUtils.getPaymentEntryPoint(), isNull);

      appState.paymentSharedData.paymentEntryPoint = PaymentEntryPoint.paymentWithEMI;
      expect(payWithEMIUtils.getPaymentEntryPoint(), PaymentEntryPoint.paymentWithEMI);

      appState.paymentSharedData.paymentEntryPoint = PaymentEntryPoint.bottomNavigationBar;
      expect(payWithEMIUtils.getPaymentEntryPoint(), PaymentEntryPoint.bottomNavigationBar);

      appState.paymentSharedData.paymentEntryPoint = PaymentEntryPoint.voucherDetailScreen;
      expect(payWithEMIUtils.getPaymentEntryPoint(), PaymentEntryPoint.voucherDetailScreen);
    });
  });

  group('verify set selectedPaymentService()', () {
    tearDownAll(() {
      appState.paymentSharedData.clearAll();
    });

    test('this method work correctly', () {
      final PaymentSharedData paymentSharedData = getIt.get<AppState>().paymentSharedData;

      payWithEMIUtils.selectedPaymentService = null;
      expect(paymentSharedData.selectedPaymentService, null);

      payWithEMIUtils.selectedPaymentService = PaymentService.outrightPurchase;
      expect(paymentSharedData.selectedPaymentService, PaymentService.outrightPurchase);

      payWithEMIUtils.selectedPaymentService = PaymentService.emi;
      expect(paymentSharedData.selectedPaymentService, PaymentService.emi);
    });
  });

  group('Test selectedEmiPackage', () {
    const String fakeTenorId = 'fakeTenorId';
    final EmiPackageEntity fakeEmiPackage = EmiPackageEntity(
      offer: EmiTenorOfferEntity(id: fakeTenorId),
    );

    tearDownAll(() {
      appState.paymentSharedData.clearAll();
    });

    test('selectedEmiPackage updates right data', () {
      final PaymentSharedData paymentSharedData = getIt.get<AppState>().paymentSharedData;

      expect(paymentSharedData.selectedEmiPackage, null);

      payWithEMIUtils.selectedEmiPackage = fakeEmiPackage;
      expect(
        paymentSharedData.selectedEmiPackage,
        isA<EmiPackageEntity>().having(
          (EmiPackageEntity p0) => p0.offer?.id,
          'test tenor offer id',
          fakeTenorId,
        ),
      );

      payWithEMIUtils.selectedEmiPackage = null;
      expect(paymentSharedData.selectedEmiPackage, null);
    });
  });

  group('Test orderCreationScreen', () {
    const Screen expectOrderCreationScreen = Screen.paymentInputAmount;

    tearDownAll(() {
      appState.paymentSharedData.clearAll();
    });

    test('orderCreationScreen updates right data', () {
      final PaymentSharedData paymentSharedData = getIt.get<AppState>().paymentSharedData;

      expect(paymentSharedData.orderCreationScreen, null);

      payWithEMIUtils.setOrderCreationScreen = expectOrderCreationScreen;
      expect(
        paymentSharedData.orderCreationScreen,
        expectOrderCreationScreen,
      );
      expect(payWithEMIUtils.getOrderCreationScreen(), expectOrderCreationScreen);

      payWithEMIUtils.setOrderCreationScreen = null;
      expect(paymentSharedData.orderCreationScreen, null);
      expect(payWithEMIUtils.getOrderCreationScreen(), null);
    });
  });

  group('Test function prepareVoucherIdsParam', () {
    const int fakeVoucherId = 1234;
    final VoucherEntity fakeVoucherEntity = VoucherEntity(id: fakeVoucherId);

    test('Give voucherEntity null, should return empty voucherId list', () {
      final List<int> result = payWithEMIUtils.prepareVoucherIdsParam(null);
      expect(result, isEmpty);
    });

    test('Give voucherEntity.id null, should return empty voucherId list', () {
      final List<int> result = payWithEMIUtils.prepareVoucherIdsParam(VoucherEntity());
      expect(result, isEmpty);
    });

    test('Give voucherEntity.id NOT null, should return voucherId list contains this id', () {
      final List<int> result = payWithEMIUtils.prepareVoucherIdsParam(fakeVoucherEntity);
      expect(result, <int>[fakeVoucherId]);
    });
  });

  group('verify checkIfOrderExpiredByVerdict', () {
    test('return TRUE if verdict is matched as expectation', () {
      expect(
          payWithEMIUtils.checkIfOrderExpiredByVerdict(UpdateOrderEntity.verdictSessionNotOpened),
          true);
      expect(payWithEMIUtils.checkIfOrderExpiredByVerdict(UpdateOrderEntity.verdictSessionExpired),
          true);
      expect(payWithEMIUtils.checkIfOrderExpiredByVerdict(CreateOrderEntity.verdictOrderExpired),
          true);
    });

    test('return FALSE if verdict is NOT matched as expectation', () {
      expect(payWithEMIUtils.checkIfOrderExpiredByVerdict(null), false);
      expect(payWithEMIUtils.checkIfOrderExpiredByVerdict('other_verdict'), false);
    });
  });

  group('verify getRecommendedEmiPackageFromList()', () {
    test('getRecommendedEmiPackageFromList returns null if packages is empty', () {
      final List<EmiPackageEntity> fakeEmiPackages = <EmiPackageEntity>[];

      final EmiPackageEntity? result =
          payWithEMIUtils.getRecommendedEmiPackageFromList(fakeEmiPackages);

      expect(result, isNull);
    });

    test('getRecommendedEmiPackageFromList returns the first recommended package', () {
      final List<EmiPackageEntity> fakeEmiPackages = <EmiPackageEntity>[
        EmiPackageEntity(offer: EmiTenorOfferEntity(id: '1', isRecommended: false)),
        EmiPackageEntity(offer: EmiTenorOfferEntity(id: '2', isRecommended: true)),
        EmiPackageEntity(offer: EmiTenorOfferEntity(id: '3', isRecommended: false)),
      ];

      final EmiPackageEntity? result =
          payWithEMIUtils.getRecommendedEmiPackageFromList(fakeEmiPackages);

      expect(result, fakeEmiPackages[1]);
    });

    test(
        'getRecommendedEmiPackageFromList returns the first package if there is no a recommended package',
        () {
      final List<EmiPackageEntity> fakeEmiPackages = <EmiPackageEntity>[
        EmiPackageEntity(offer: EmiTenorOfferEntity(id: '1', isRecommended: false)),
        EmiPackageEntity(offer: EmiTenorOfferEntity(id: '2', isRecommended: false)),
        EmiPackageEntity(offer: EmiTenorOfferEntity(id: '3', isRecommended: false)),
      ];

      final EmiPackageEntity? result =
          payWithEMIUtils.getRecommendedEmiPackageFromList(fakeEmiPackages);

      expect(result, fakeEmiPackages[0]);
    });
  });

  group('verify updateFlagPostLimitWarning()', () {
    test('verify update value', () async {
      await payWithEMIUtils.updateFlagPostLimitWarning();

      verify(() => localStorageHelper.setEnablePosLimitWarning(true)).called(1);
    });
  });

  void verifyShowPosLimitBottomSheetCalled() {
    final Finder finder = find.byType(EvoDialogRemindEnablePosLimit);
    expect(finder, findsOneWidget);
  }

  group('verify showPosLimitBottomSheetIfNeed()', () {
    late EventTrackingUtils mockEventTrackingUtils;

    setUpAll(() {
      getIt.registerLazySingleton<EventTrackingUtils>(() => MockEventTrackingUtils());
      mockEventTrackingUtils = getIt.get<EventTrackingUtils>();

      when(() => mockEventTrackingUtils.sendUserActionEvent(
            eventId: any(named: 'eventId'),
            metaData: any(named: 'metaData'),
          )).thenAnswer((_) => Future<void>.value());
    });

    tearDownAll(() {
      getIt.unregister<EventTrackingUtils>();
    });

    testWidgets('verify NOT call showPosLimitBottomSheet()', (WidgetTester tester) async {
      when(() => mockFeatureToggle.enableActivatePOSLimitFeature).thenReturn(false);

      await tester.pumpWidget(MaterialApp(
        navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
        scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return GestureDetector(
                onTap: () {
                  payWithEMIUtils.showPosLimitBottomSheetIfNeed();
                },
                child: const Text('showPosLimitBottomSheetIfNeed'),
              );
            },
          ),
        ),
      ));

      await tester.tap(find.text('showPosLimitBottomSheetIfNeed'));
      await tester.pumpAndSettle(const Duration(seconds: 1));

      final Finder finder = find.byType(EvoDialogRemindEnablePosLimit);
      expect(finder, findsNothing);
    });

    testWidgets('verify call showPosLimitBottomSheet() with getEnablePosLimitWarning() = false',
        (WidgetTester tester) async {
      when(() => mockFeatureToggle.enableActivatePOSLimitFeature).thenReturn(false);
      when(() => localStorageHelper.getEnablePosLimitWarning())
          .thenAnswer((_) => Future<bool>.value(false));

      await tester.pumpWidget(MaterialApp(
        navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
        scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return GestureDetector(
                onTap: () {
                  payWithEMIUtils.showPosLimitBottomSheetIfNeed();
                },
                child: const Text('showPosLimitBottomSheetIfNeed'),
              );
            },
          ),
        ),
      ));

      await tester.tap(find.text('showPosLimitBottomSheetIfNeed'));
      await tester.pumpAndSettle(const Duration(seconds: 1));

      verifyShowPosLimitBottomSheetCalled();
    });

    testWidgets('verify call showPosLimitBottomSheet() with getEnablePosLimitWarning() is null',
        (WidgetTester tester) async {
      when(() => mockFeatureToggle.enableActivatePOSLimitFeature).thenReturn(false);
      when(() => localStorageHelper.getEnablePosLimitWarning())
          .thenAnswer((_) => Future<bool?>.value());

      await tester.pumpWidget(MaterialApp(
        navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
        scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return GestureDetector(
                onTap: () {
                  payWithEMIUtils.showPosLimitBottomSheetIfNeed();
                },
                child: const Text('showPosLimitBottomSheetIfNeed'),
              );
            },
          ),
        ),
      ));

      await tester.tap(find.text('showPosLimitBottomSheetIfNeed'));
      await tester.pumpAndSettle(const Duration(seconds: 1));

      verifyShowPosLimitBottomSheetCalled();
    });

    testWidgets('should NOT show dialog when Activate POS Limit feature is enabled',
        (WidgetTester tester) async {
      when(() => mockFeatureToggle.enableActivatePOSLimitFeature).thenReturn(true);

      await tester.pumpWidget(MaterialApp(
        navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
        scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return GestureDetector(
                onTap: () {
                  payWithEMIUtils.showPosLimitBottomSheetIfNeed();
                },
                child: const Text('showPosLimitBottomSheetIfNeed'),
              );
            },
          ),
        ),
      ));

      await tester.tap(find.text('showPosLimitBottomSheetIfNeed'));
      await tester.pumpAndSettle(const Duration(seconds: 1));

      verifyNever(() => localStorageHelper.getEnablePosLimitWarning());

      final Finder finder = find.byType(EvoDialogRemindEnablePosLimit);
      expect(finder, findsNothing);
    });
  });

  group('verify showPosLimitBottomSheet()', () {
    late EventTrackingUtils mockEventTrackingUtils;

    setUpAll(() {
      getIt.registerLazySingleton<EventTrackingUtils>(() => MockEventTrackingUtils());
      mockEventTrackingUtils = getIt.get<EventTrackingUtils>();

      when(() => mockEventTrackingUtils.sendUserActionEvent(
            eventId: any(named: 'eventId'),
            metaData: any(named: 'metaData'),
          )).thenAnswer((_) => Future<void>.value());
    });

    tearDownAll(() {
      getIt.unregister<EventTrackingUtils>();
    });

    testWidgets('verify show() with type = error', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
        scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return GestureDetector(
                onTap: () {
                  payWithEMIUtils.showPosLimitBottomSheet();
                },
                child: const Text('showPosLimitBottomSheet'),
              );
            },
          ),
        ),
      ));

      await tester.tap(find.text('showPosLimitBottomSheet'));
      await tester.pumpAndSettle(const Duration(seconds: 1));

      verifyShowPosLimitBottomSheetCalled();
    });
  });

  group('verify isEmiFeatureEnabled', () {
    test('return true if feature is enabled', () {
      when(() => mockFeatureToggle.enableEmiFeatureVersion).thenReturn(EmiFeatureVersion.version_1);

      final bool result = payWithEMIUtils.isEmiFeatureEnabled();
      expect(result, true);
    });

    test('return false if feature is disabled', () {
      when(() => mockFeatureToggle.enableEmiFeatureVersion)
          .thenReturn(EmiFeatureVersion.notSupported);

      final bool result = payWithEMIUtils.isEmiFeatureEnabled();
      expect(result, false);
    });
  });

  group('verify onShowTenorInfo()', () {
    late EventTrackingUtils mockEventTrackingUtils;

    setUpAll(() {
      getIt.registerLazySingleton<EventTrackingUtils>(() => MockEventTrackingUtils());
      mockEventTrackingUtils = getIt.get<EventTrackingUtils>();

      when(() => mockEventTrackingUtils.sendUserActionEvent(
            eventId: any(named: 'eventId'),
            metaData: any(named: 'metaData'),
          )).thenAnswer((_) => Future<void>.value());
    });

    tearDownAll(() {
      getIt.unregister<EventTrackingUtils>();
    });

    testWidgets('should not show bottom sheet when tenorInfo is null', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
          home: Scaffold(
            body: Builder(
              builder: (BuildContext context) {
                return ElevatedButton(
                  onPressed: () => payWithEMIUtils.onShowTenorInfo(null),
                  child: const Text('Show tenor info'),
                );
              },
            ),
          ),
        ),
      );

      await tester.tap(find.text('Show tenor info'));
      await tester.pumpAndSettle();

      final Finder finder = find.byType(TenorInfoBottomSheet);
      expect(finder, findsNothing);
    });

    testWidgets('should show bottom sheet with correct items when tenorInfo is not null',
        (WidgetTester tester) async {
      final List<String> testItems = <String>['Item 1', 'Item 2', 'Item 3'];

      await tester.pumpWidget(
        MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
          home: Scaffold(
            body: Builder(
              builder: (BuildContext context) {
                return ElevatedButton(
                  onPressed: () => payWithEMIUtils.onShowTenorInfo(testItems),
                  child: const Text('Show tenor info'),
                );
              },
            ),
          ),
        ),
      );

      await tester.tap(find.text('Show tenor info'));
      await tester.pumpAndSettle();

      final Finder finder = find.byType(TenorInfoBottomSheet);
      expect(finder, findsOneWidget);
    });
  });
}
