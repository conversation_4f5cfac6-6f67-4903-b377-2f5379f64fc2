import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/response/sign_in_otp_entity.dart';
import 'package:evoapp/feature/biometric/model/biometric_ui_model.dart';
import 'package:evoapp/feature/biometric/utils/biometric_status_helper.dart';
import 'package:evoapp/feature/biometric/utils/biometric_type_helper.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/logging/evo_event_tracking_handler.dart';
import 'package:evoapp/feature/maintenance/maintenance_handler.dart';
import 'package:evoapp/feature/remote_config/remote_config_helper.dart';
import 'package:evoapp/feature/splash_screen/splash_screen.dart';
import 'package:evoapp/feature/splash_screen/utils/exit_app_feature/exit_app_feature.dart';
import 'package:evoapp/feature/splash_screen/utils/secure_detection_utils/secure_detection.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_authentication_helper.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/feature/data_collection/data_collector.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/util/share_preference_helper.dart';
import 'package:flutter_common_package/util/uuid/uuid_generator.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../base/evo_page_state_base_test_config.dart';
import '../../util/flutter_test_config.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockJwtHelper extends Mock implements JwtHelper {}

class MockBiometricTypeHelper extends Mock implements BiometricTypeHelper {}

class MockBiometricStatusHelper extends Mock implements BiometricStatusHelper {}

class MockSecureDetection extends Mock implements SecureDetection {}

class MockRemoteConfigHelper extends Mock implements RemoteConfigHelper {}

class MockDataCollector extends Mock implements DataCollector {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockEvoDialogHelper extends Mock implements EvoDialogHelper {}

class MockExitAppFeature extends Mock implements ExitAppFeature {}

class MockFeatureToggle extends Mock implements FeatureToggle {}

class MockCommonSharedPreferencesHelper extends Mock implements CommonSharedPreferencesHelper {}

class MockNavigatorObserver extends Mock implements NavigatorObserver {}

class MockEvoEventTrackingHandler extends Mock implements EvoEventTrackingHandler {}

class MockUUIDGenerator extends Mock implements UUIDGenerator {}

class MockMaintenanceHandler extends Mock implements MaintenanceHandler {}

void main() {
  late MockAuthenticationRepo mockAuthenticationRepo;
  late MockJwtHelper mockJwtHelper;
  late MockBiometricTypeHelper mockBiometricTypeHelper;
  late MockBiometricStatusHelper mockBiometricStatusHelper;
  late MockSecureDetection mockSecureDetection;
  late MockRemoteConfigHelper mockRemoteConfigHelper;
  late MockDataCollector mockDataCollector;
  late MockEvoLocalStorageHelper mockLocalStorageHelper;
  late MockEvoDialogHelper mockDialogHelper;
  late MockExitAppFeature mockExitAppFeature;
  late MockFeatureToggle mockFeatureToggle;
  late MockCommonSharedPreferencesHelper mockSharedPreferencesHelper;
  late MockNavigatorObserver mockNavigatorObserver;
  late CommonImageProvider mockImageProvider;
  late MockEvoEventTrackingHandler mockEvoEventTrackingHandler;
  late MockUUIDGenerator mockUUIDGenerator;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    initConfigEvoPageStateBase();
    setUtilsMockInstanceForTesting();
    registerFallbackValue(MockBuildContext());
    registerFallbackValue(EvoDialogId.blockInsecureDeviceDialog);
    registerFallbackValue(SnackBarType.success);
    registerFallbackValue(TsBiometricType.face);
    registerFallbackValue(BiometricTypeUIModel.face());

    mockAuthenticationRepo = MockAuthenticationRepo();
    mockJwtHelper = MockJwtHelper();
    mockBiometricTypeHelper = MockBiometricTypeHelper();
    mockBiometricStatusHelper = MockBiometricStatusHelper();
    mockSecureDetection = MockSecureDetection();
    mockRemoteConfigHelper = MockRemoteConfigHelper();
    mockDataCollector = MockDataCollector();
    mockLocalStorageHelper = MockEvoLocalStorageHelper();
    mockDialogHelper = MockEvoDialogHelper();
    mockExitAppFeature = MockExitAppFeature();
    mockFeatureToggle = MockFeatureToggle();
    mockSharedPreferencesHelper = MockCommonSharedPreferencesHelper();
    mockNavigatorObserver = MockNavigatorObserver();
    mockEvoEventTrackingHandler = MockEvoEventTrackingHandler();
    mockUUIDGenerator = MockUUIDGenerator();

    getIt.registerSingleton<AuthenticationRepo>(mockAuthenticationRepo);
    getIt.registerSingleton<JwtHelper>(mockJwtHelper);
    getIt.registerSingleton<BiometricTypeHelper>(mockBiometricTypeHelper);
    getIt.registerSingleton<BiometricStatusHelper>(mockBiometricStatusHelper);
    getIt.registerSingleton<SecureDetection>(mockSecureDetection);
    getIt.registerSingleton<RemoteConfigHelper>(mockRemoteConfigHelper);
    getIt.registerSingleton<DataCollector>(mockDataCollector);
    getIt.registerSingleton<EvoLocalStorageHelper>(mockLocalStorageHelper);
    getIt.registerSingleton<EvoDialogHelper>(mockDialogHelper);
    getIt.registerSingleton<ExitAppFeature>(mockExitAppFeature);
    getIt.registerSingleton<FeatureToggle>(mockFeatureToggle);
    getIt.registerSingleton<EvoEventTrackingHandler>(mockEvoEventTrackingHandler);
    getIt.registerSingleton<UUIDGenerator>(mockUUIDGenerator);
    getIt.registerSingleton<CommonSharedPreferencesHelper>(mockSharedPreferencesHelper);

    mockImageProvider = getIt.get<CommonImageProvider>();

    // Mock image provider
    when(() => mockImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());

    EvoDialogHelper.setInstanceForTesting(mockDialogHelper);

    when(() => mockLocalStorageHelper.deleteAllData())
        .thenAnswer((_) async => Future<void>.value());
    when(() => mockDataCollector.logMobileDataCollection()).thenAnswer((_) => Future<void>.value());
    when(() => mockLocalStorageHelper.getAccessToken())
        .thenAnswer((_) async => Future<String>.value(''));
    when(() => EvoUiUtils().calculateHorizontalSpace(
          context: any(named: 'context'),
          widthPercentage: any(named: 'widthPercentage'),
        )).thenReturn(10);
    when(() => mockBiometricTypeHelper.getTsBiometricType())
        .thenAnswer((_) => Future<TsBiometricType>.value(TsBiometricType.face));
    when(() => EvoAuthenticationHelper().getBiometricUIModel(any()))
        .thenReturn(BiometricTypeUIModel.face());
    when(() => mockAuthenticationRepo.refreshToken(any()))
        .thenAnswer((_) => Future<SignInOtpEntity>.value(SignInOtpEntity()));
    when(() => EvoAuthenticationHelper().clearDataOnTokenInvalid())
        .thenAnswer((_) async => Future<void>.value());
    when(() => mockRemoteConfigHelper.init()).thenAnswer((_) async => Future<void>.value());
    when(() => mockEvoUtilFunction.deleteAllData()).thenAnswer((_) async => Future<void>.value());
    when(() => mockEvoUtilFunction.setNewDeviceId()).thenAnswer((_) async => Future<void>.value());
    when(() => mockUUIDGenerator.genV4()).thenReturn('new-batch-id');
    when(() => mockLocalStorageHelper.setChatwootIdForNonEvoUser(any())).thenAnswer((_) async {});
    when(() => mockLocalStorageHelper.loadAllDataIntoMemory())
        .thenAnswer((_) async => Future<void>.value());
    when(() => mockBiometricStatusHelper.updateBiometricStatus())
        .thenAnswer((_) async => Future<void>.value());
    when(() => mockEvoEventTrackingHandler.prepareInitialSession())
        .thenAnswer((_) => Future<void>.value());
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();

    when(() => mockJwtHelper.isCanUse(any())).thenReturn(false);

    when(() => mockSharedPreferencesHelper.setPassedTutorial(true))
        .thenAnswer((_) async => Future<void>.value());

    when(() => mockSecureDetection.isSecureDevice())
        .thenAnswer((_) async => Future<bool>.value(true));

    when(() => mockFeatureToggle.enableRemoteConfigFeature).thenReturn(true);
    when(() => mockSharedPreferencesHelper.setPassedTutorial(true))
        .thenAnswer((_) async => Future<void>.value());
    when(() => mockSharedPreferencesHelper.isPassedTutorial())
        .thenAnswer((_) async => Future<bool>.value(false));

    when(() => mockEvoUtilFunction.detectReinstallAppOnIOSDevice())
        .thenAnswer((_) async => Future<bool>.value(false));

    when(() => mockNetworkManager.hasInternet).thenReturn(true);
  });

  tearDown(() {
    reset(mockNavigatorContext);
    reset(mockFeatureToggle);
    reset(mockSharedPreferencesHelper);
    reset(mockDialogHelper);
    reset(mockSecureDetection);
  });

  tearDownAll(() {
    getIt.reset();
    resetUtilMockToOriginalInstance();
  });

  testWidgets('SplashScreen should render with logo and loading indicator',
      (WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pumpWidget(
        MaterialApp(
          home: SplashScreen(),
          navigatorObservers: <NavigatorObserver>[mockNavigatorObserver],
        ),
      );
    });

    expect(find.byType(Scaffold), findsOneWidget);
    expect(find.byType(CircularProgressIndicator), findsOneWidget);
    expect(find.byType(Stack), findsWidgets);
  });

  test('SplashScreen should not listen to maintenance mode', () {
    // Create a SplashScreen instance and get its state
    final SplashScreen splashScreen = SplashScreen();
    final dynamic state = splashScreen.createState();

    // Verify that hasListenMaintenanceMode returns false
    expect(state.hasListenMaintenanceMode(), false);
  });

  testWidgets('SplashScreen should show insecure device dialog when state is insecure device',
      (WidgetTester tester) async {
    // Arrange
    when(() => mockSecureDetection.isSecureDevice())
        .thenAnswer((_) async => Future<bool>.value(false));
    when(() => mockDialogHelper.showDialogConfirm(
          dialogId: any(named: 'dialogId'),
          title: any(named: 'title'),
          content: any(named: 'content'),
          isDismissible: any(named: 'isDismissible'),
          textPositive: any(named: 'textPositive'),
          onClickPositive: any(named: 'onClickPositive'),
        )).thenAnswer((_) => Future<void>.value());

    // Act
    await tester.pumpWidget(
      MaterialApp(
        home: SplashScreen(),
        navigatorObservers: <NavigatorObserver>[mockNavigatorObserver],
      ),
    );

    // Use multiple pump calls instead of pumpAndSettle to avoid timeouts
    await tester.pumpAndSettle();

    // Assert
    verify(() => mockDialogHelper.showDialogConfirm(
          dialogId: EvoDialogId.blockInsecureDeviceDialog,
          title: EvoStrings.titleBlockInsecureDeviceDialog,
          content: EvoStrings.descriptionBlockInsecureDeviceDialog,
          isDismissible: false,
          textPositive: EvoStrings.close,
          onClickPositive: any(named: 'onClickPositive'),
        )).called(1);
  });

  testWidgets(
      'SplashScreen should navigate to home screen when state is tutorial and feature toggle is on',
      (WidgetTester tester) async {
    // Arrange

    when(() => mockSharedPreferencesHelper.isPassedTutorial())
        .thenAnswer((_) => Future<bool>.value(false));

    when(() => mockSharedPreferencesHelper.setPassedTutorial(true))
        .thenAnswer((_) async => Future<void>.value());

    when(() => mockNavigatorContext.goNamed(
          any(),
          extra: any(named: 'extra'),
        )).thenAnswer((_) {});

    // Act
    await tester.pumpWidget(
      MaterialApp(
        home: SplashScreen(),
        routes: <String, WidgetBuilder>{
          '/main': (BuildContext context) => Scaffold(body: Text('Main Screen')),
        },
        navigatorObservers: <NavigatorObserver>[mockNavigatorObserver],
      ),
    );

    await tester.pump(Duration(seconds: 5));

    verify(() => mockSharedPreferencesHelper.isPassedTutorial()).called(greaterThanOrEqualTo(1));

    verify(() => mockSharedPreferencesHelper.setPassedTutorial(true))
        .called(greaterThanOrEqualTo(1));

    verify(() => mockNavigatorContext.goNamed(
          any(),
          extra: any(named: 'extra'),
        )).called(1);
  });

  testWidgets('SplashScreen should navigate to home screen when state is nonUser',
      (WidgetTester tester) async {
    // Arrange
    reset(mockNavigatorContext);

    when(() => mockNavigatorContext.goNamed(
          any(),
          extra: any(named: 'extra'),
        )).thenAnswer((_) {});

    // Act
    await tester.pumpWidget(
      MaterialApp(
        home: SplashScreen(),
        routes: <String, WidgetBuilder>{
          '/main': (BuildContext context) => Scaffold(body: Text('Main Screen')),
        },
        navigatorObservers: <NavigatorObserver>[mockNavigatorObserver],
      ),
    );

    await tester.pump(Duration(seconds: 5));

    // Assert
    verify(() => mockNavigatorContext.goNamed(
          any(),
          extra: any(named: 'extra'),
        )).called(1);
  });

  testWidgets('SplashScreen should navigate to home screen when state is loggedIn',
      (WidgetTester tester) async {
    // Arrange

    when(() => mockJwtHelper.isCanUse(any())).thenReturn(true);

    when(() => mockNavigatorContext.goNamed(
          any(),
          extra: any(named: 'extra'),
        )).thenAnswer((_) {});

    // Act
    await tester.pumpWidget(
      MaterialApp(
        home: SplashScreen(),
        routes: <String, WidgetBuilder>{
          '/main': (BuildContext context) => Scaffold(body: Text('Main Screen')),
        },
        navigatorObservers: <NavigatorObserver>[mockNavigatorObserver],
      ),
    );

    await tester.pump(Duration(seconds: 5));

    // Assert
    verify(() => mockNavigatorContext.goNamed(
          any(),
          extra: any(named: 'extra'),
        )).called(1);
  });

  test('SplashScreen.goNamed should call navigatorContext.goNamed', () {
    // Arrange
    when(() => mockNavigatorContext.goNamed(any())).thenAnswer((_) {});

    // Act
    SplashScreen.goNamed();

    // Assert
    verify(() => mockNavigatorContext.goNamed(Screen.splashScreen.name)).called(1);
  });
}
