import 'package:evoapp/feature/ekyc_v2/face_auth/instruction/face_auth_instruction_state.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('FaceAuthInstructionState', () {
    test('FaceAuthInstructionInitialState can be instantiated', () {
      final FaceAuthInstructionInitialState state = FaceAuthInstructionInitialState();
      expect(state, isA<FaceAuthInstructionInitialState>());
    });

    test('FaceAuthInstructionLoadingState can be instantiated', () {
      final FaceAuthInstructionLoadingState state = FaceAuthInstructionLoadingState();
      expect(state, isA<FaceAuthInstructionLoadingState>());
    });

    test('FaceAuthInstructionStartNow can be instantiated', () {
      final FaceAuthInstructionStartNow state = FaceAuthInstructionStartNow();
      expect(state, isA<FaceAuthInstructionStartNow>());
    });

    test('FaceAuthInstructionFailState stores errorUIModel', () {
      final ErrorUIModel errorUIModel = ErrorUIModel();
      final FaceAuthInstructionFailState state = FaceAuthInstructionFailState(errorUIModel);
      expect(state, isA<FaceAuthInstructionFailState>());
      expect(state.errorUIModel, equals(errorUIModel));
    });
  });
}
