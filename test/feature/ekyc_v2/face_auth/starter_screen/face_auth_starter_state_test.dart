import 'package:evoapp/feature/ekyc_v2/face_auth/starter_screen/face_auth_starter_cubit.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_error_reason.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('FaceAuthStarterState Tests', () {
    test('FaceAuthStarterInitial is created', () {
      final FaceAuthStarterInitial state = FaceAuthStarterInitial();
      expect(state, isA<FaceAuthStarterInitial>());
    });

    test('FaceAuthStarterLoadingState is created', () {
      final FaceAuthStarterLoadingState state = FaceAuthStarterLoadingState();
      expect(state, isA<FaceAuthStarterLoadingState>());
    });

    test('FaceAuthStarterInitializeSuccessState is created', () {
      final FaceAuthStarterInitializeSuccessState state = FaceAuthStarterInitializeSuccessState();
      expect(state, isA<FaceAuthStarterInitializeSuccessState>());
    });

    test('FaceAuthStarterInitializeExceedLimitationState is created', () {
      final FaceAuthStarterInitializeExceedLimitationState state =
          FaceAuthStarterInitializeExceedLimitationState();
      expect(state, isA<FaceAuthStarterInitializeExceedLimitationState>());
    });

    test('FaceAuthStarterInitializeEkycBridgeErrorState is created with correct errorReason', () {
      final EkycBridgeErrorReason errorReason =
          EkycBridgeErrorReason.exceedLimit; // Replace with actual reason
      final FaceAuthStarterInitializeEkycBridgeErrorState state =
          FaceAuthStarterInitializeEkycBridgeErrorState(errorReason: errorReason);
      expect(state, isA<FaceAuthStarterInitializeEkycBridgeErrorState>());
      expect(state.errorReason, equals(errorReason));
    });

    test('FaceAuthStarterInitializeApiErrorState is created with correct properties', () {
      final ErrorUIModel errorUIModel =
          ErrorUIModel(userMessage: 'Error'); // Replace with actual model
      final bool backToEntryPoint = true;
      final FaceAuthStarterInitializeApiErrorState state = FaceAuthStarterInitializeApiErrorState(
        errorUIModel: errorUIModel,
        backToEntryPoint: backToEntryPoint,
      );
      expect(state, isA<FaceAuthStarterInitializeApiErrorState>());
      expect(state.errorUIModel, equals(errorUIModel));
      expect(state.backToEntryPoint, equals(backToEntryPoint));
    });
  });
}
