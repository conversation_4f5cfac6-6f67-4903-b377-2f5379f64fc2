import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/fpt/fpt_sdk_bridge_impl.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/fpt/nfc_reader/nfc_reader.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/fpt/nfc_reader/result/nfc_availability_type.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/fpt/nfc_reader/result/nfc_detection_result.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/fpt/nfc_reader/result/nfc_error.dart';
import 'package:evoapp/feature/dop_native/features/logging/metadata_define/dop_native_event_metadata.dart';
import 'package:evoapp/feature/dop_native/features/logging/screen_action_define/dop_native_special_action_event.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/feature/logging/evo_event_tracking_utils/evo_event_tracking_utils.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockEvoEventTrackingUtils extends Mock implements EvoEventTrackingUtils {}

class MockMethodChannel extends Mock implements MethodChannel {}

class MockEvoFlutterWrapper extends Mock implements EvoFlutterWrapper {}

class MockDOPUtilFunctions extends Mock implements DOPUtilFunctions {}

class MockNFCReader extends Mock implements NFCReader {}

void main() {
  late EvoEventTrackingUtils mockEvoEventTrackingUtils;
  late FptSdkBridgeImpl fptSdkBridgeImpl;
  late EvoFlutterWrapper mockEvoFlutterWrapper;
  final NFCReader nfcReader = MockNFCReader();

  test('test static values', () {
    expect(FptSdkBridgeImpl.stepReadNFC, 'read_nfc');
    expect(FptSdkBridgeImpl.stepCheckNFCSupport, 'check_nfc_support');
    expect(FptSdkBridgeImpl.platformException, 'platform_exception');
    expect(FptSdkBridgeImpl.otherException, 'other_exception');
  });

  setUpAll(() {
    getIt.registerSingleton<EvoFlutterWrapper>(MockEvoFlutterWrapper());
    mockEvoFlutterWrapper = getIt<EvoFlutterWrapper>();

    getIt.registerSingleton<EvoEventTrackingUtils>(MockEvoEventTrackingUtils());
    mockEvoEventTrackingUtils = getIt<EvoEventTrackingUtils>();
    when(
      () => mockEvoEventTrackingUtils.sendEvoSpecialEvent(
        eventActionId: any(named: 'eventActionId'),
        metaData: any(named: 'metaData'),
      ),
    ).thenAnswer((_) async {});
  });

  setUp(() {
    registerFallbackValue(const EventType('fake_type'));

    fptSdkBridgeImpl = FptSdkBridgeImpl(nfcReader);
  });

  group('Test readNfc()', () {
    const String fakeIdCardNumber = 'fakeIdCardNumber';
    const String fakeDateOfBirth = '01/01/2000';
    const String fakeDateOfExpiry = '01/01/2023';
    late DOPUtilFunctions mockDOPUtilFunctions;
    const Map<dynamic, dynamic> fakeResultMap = <dynamic, dynamic>{
      'rawData': <dynamic, dynamic>{'key': 'value'},
      'personalData': <dynamic, dynamic>{'key': 'value'},
    };

    setUpAll(() {
      getIt.registerSingleton<DOPUtilFunctions>(MockDOPUtilFunctions());
      mockDOPUtilFunctions = getIt<DOPUtilFunctions>();
    });

    setUp(() {
      when(
        () => nfcReader.readNfc(
          idCardNumber: any(named: 'idCardNumber'),
          dateOfBirth: any(named: 'dateOfBirth'),
          dateOfExpiry: any(named: 'dateOfExpiry'),
          dateOfIssued: any(named: 'dateOfIssued'),
        ),
      ).thenAnswer((_) async => NFCDetectionResult.fromMap(fakeResultMap));

      when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(false);
    });

    tearDown(() {
      reset(mockEvoFlutterWrapper);
      reset(mockDOPUtilFunctions);
    });

    test('readNfc called with correct parameters and succeeds', () async {
      final NFCDetectionResult result = await fptSdkBridgeImpl.readNfc(
        idCardNumber: fakeIdCardNumber,
        dateOfBirth: fakeDateOfBirth,
        dateOfExpiry: fakeDateOfExpiry,
      );

      expect(result.error, isNull);

      verify(() => nfcReader.readNfc(
            idCardNumber: fakeIdCardNumber,
            dateOfBirth: fakeDateOfBirth,
            dateOfExpiry: fakeDateOfExpiry,
          )).called(1);
      verify(
        () => mockEvoEventTrackingUtils.sendEvoSpecialEvent(
          eventActionId: DOPNativeSpecialActionEvent.nfcSdkResult,
          metaData: <String, dynamic>{
            DOPNativeEventMetadataKey.stepStatus: DOPNativeEventMetadataValue.succeed,
          },
        ),
      );
    });

    test('readNfc called with correct parameters and failed', () async {
      when(
        () => nfcReader.readNfc(
          idCardNumber: any(named: 'idCardNumber'),
          dateOfBirth: any(named: 'dateOfBirth'),
          dateOfExpiry: any(named: 'dateOfExpiry'),
        ),
      ).thenAnswer(
        (_) async => NFCDetectionResult(
          errorMessage: 'error_message',
          error: NFCError.unknownError,
        ),
      );
      await fptSdkBridgeImpl.readNfc(
        idCardNumber: fakeIdCardNumber,
        dateOfBirth: fakeDateOfBirth,
        dateOfExpiry: fakeDateOfExpiry,
      );

      verify(() => nfcReader.readNfc(
            idCardNumber: fakeIdCardNumber,
            dateOfBirth: fakeDateOfBirth,
            dateOfExpiry: fakeDateOfExpiry,
          )).called(1);
      verify(
        () => mockEvoEventTrackingUtils.sendEvoSpecialEvent(
          eventActionId: DOPNativeSpecialActionEvent.nfcSdkResult,
          metaData: <String, dynamic>{
            DOPNativeEventMetadataKey.stepStatus: DOPNativeEventMetadataValue.failed,
            DOPNativeEventMetadataKey.errorCode: NFCError.unknownError.errorCode,
            DOPNativeEventMetadataKey.verdict: 'error_message',
          },
        ),
      );
    });

    test('readNfc throws PlatformException, should return sdkException', () async {
      final PlatformException exception = PlatformException(code: 'error');
      when(
        () => nfcReader.readNfc(
          idCardNumber: any(named: 'idCardNumber'),
          dateOfBirth: any(named: 'dateOfBirth'),
          dateOfExpiry: any(named: 'dateOfExpiry'),
        ),
      ).thenThrow(exception);

      final NFCDetectionResult result = await fptSdkBridgeImpl.readNfc(
        idCardNumber: fakeIdCardNumber,
        dateOfBirth: fakeDateOfBirth,
        dateOfExpiry: fakeDateOfExpiry,
      );

      expect(result.error, NFCError.sdkException);

      verify(() => nfcReader.readNfc(
            idCardNumber: fakeIdCardNumber,
            dateOfBirth: fakeDateOfBirth,
            dateOfExpiry: fakeDateOfExpiry,
          )).called(1);
      verify(
        () => mockEvoEventTrackingUtils.sendEvoSpecialEvent(
          eventActionId: DOPNativeSpecialActionEvent.nfcSdkResult,
          metaData: <String, dynamic>{
            DOPNativeEventMetadataKey.stepStatus: DOPNativeEventMetadataValue.failed,
            DOPNativeEventMetadataKey.errorCode: 'platform_exception',
            DOPNativeEventMetadataKey.verdict: exception.toString(),
          },
        ),
      );
    });

    test('readNfc throws Exception, should return sdkException', () async {
      final Exception exception = Exception('error');
      when(
        () => nfcReader.readNfc(
          idCardNumber: any(named: 'idCardNumber'),
          dateOfBirth: any(named: 'dateOfBirth'),
          dateOfExpiry: any(named: 'dateOfExpiry'),
        ),
      ).thenThrow(exception);

      final NFCDetectionResult result = await fptSdkBridgeImpl.readNfc(
        idCardNumber: fakeIdCardNumber,
        dateOfBirth: fakeDateOfBirth,
        dateOfExpiry: fakeDateOfExpiry,
      );

      expect(result.error, NFCError.sdkException);

      verify(() => nfcReader.readNfc(
            idCardNumber: fakeIdCardNumber,
            dateOfBirth: fakeDateOfBirth,
            dateOfExpiry: fakeDateOfExpiry,
          )).called(1);
      verify(
        () => mockEvoEventTrackingUtils.sendEvoSpecialEvent(
          eventActionId: DOPNativeSpecialActionEvent.nfcSdkResult,
          metaData: <String, dynamic>{
            DOPNativeEventMetadataKey.stepStatus: DOPNativeEventMetadataValue.failed,
            DOPNativeEventMetadataKey.errorCode: 'other_exception',
            DOPNativeEventMetadataKey.verdict: exception.toString(),
          },
        ),
      );
    });
  });

  group('verify checkNfcSupport', () {
    test('return available when Device support NFC', () async {
      when(() => nfcReader.checkNFCSupport()).thenAnswer(
        (_) async => NfcAvailabilityType.available,
      );

      final NfcAvailabilityType result = await fptSdkBridgeImpl.checkNfcSupport();
      expect(result, NfcAvailabilityType.available);
      verify(
        () => mockEvoEventTrackingUtils.sendEvoSpecialEvent(
          eventActionId: DOPNativeSpecialActionEvent.checkNfcSupport,
          metaData: <String, dynamic>{
            DOPNativeEventMetadataKey.checkResult: NfcAvailabilityType.available.name,
          },
        ),
      );
    });

    test('return disabled when Device disable NFC', () async {
      when(() => nfcReader.checkNFCSupport()).thenAnswer(
        (_) async => NfcAvailabilityType.disabled,
      );

      final NfcAvailabilityType result = await fptSdkBridgeImpl.checkNfcSupport();
      expect(result, NfcAvailabilityType.disabled);
      verify(
        () => mockEvoEventTrackingUtils.sendEvoSpecialEvent(
          eventActionId: DOPNativeSpecialActionEvent.checkNfcSupport,
          metaData: <String, dynamic>{
            DOPNativeEventMetadataKey.checkResult: NfcAvailabilityType.disabled.name,
          },
        ),
      );
    });

    test('return unsupported when Device NOT support NFC', () async {
      when(() => nfcReader.checkNFCSupport()).thenAnswer(
        (_) async => NfcAvailabilityType.unsupported,
      );

      final NfcAvailabilityType result = await fptSdkBridgeImpl.checkNfcSupport();
      expect(result, NfcAvailabilityType.unsupported);
      verify(
        () => mockEvoEventTrackingUtils.sendEvoSpecialEvent(
          eventActionId: DOPNativeSpecialActionEvent.checkNfcSupport,
          metaData: <String, dynamic>{
            DOPNativeEventMetadataKey.checkResult: NfcAvailabilityType.unsupported.name,
          },
        ),
      );
    });

    test('return unsupported when throw PlatformException', () async {
      final PlatformException exception = PlatformException(code: 'any-code');
      when(() => nfcReader.checkNFCSupport()).thenAnswer(
        (_) async => throw exception,
      );

      final NfcAvailabilityType result = await fptSdkBridgeImpl.checkNfcSupport();
      expect(result, NfcAvailabilityType.unsupported);
      verify(
        () => mockEvoEventTrackingUtils.sendEvoSpecialEvent(
          eventActionId: DOPNativeSpecialActionEvent.checkNfcSupport,
          metaData: <String, dynamic>{
            DOPNativeEventMetadataKey.checkResult: DOPNativeEventMetadataValue.failed,
            DOPNativeEventMetadataKey.errorCode: 'platform_exception',
            DOPNativeEventMetadataKey.verdict: exception.toString(),
          },
        ),
      );
    });

    test('return false when throw Unknown Exception', () async {
      final Exception exception = Exception('message');
      when(() => nfcReader.checkNFCSupport()).thenAnswer(
        (_) async => throw exception,
      );

      final NfcAvailabilityType result = await fptSdkBridgeImpl.checkNfcSupport();
      expect(result, NfcAvailabilityType.unsupported);
      verify(
        () => mockEvoEventTrackingUtils.sendEvoSpecialEvent(
          eventActionId: DOPNativeSpecialActionEvent.checkNfcSupport,
          metaData: <String, dynamic>{
            DOPNativeEventMetadataKey.checkResult: DOPNativeEventMetadataValue.failed,
            DOPNativeEventMetadataKey.errorCode: 'other_exception',
            DOPNativeEventMetadataKey.verdict: exception.toString(),
          },
        ),
      );
    });
  });
}
