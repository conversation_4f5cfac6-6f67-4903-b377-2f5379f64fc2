import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/data/response/dop_native/dop_native_application_state_entity.dart';
import 'package:evoapp/feature/dop_native/features/inform_success/base/cubit/dop_native_inform_success_cubit.dart';
import 'package:evoapp/feature/dop_native/features/inform_success/base/cubit/dop_native_inform_success_state.dart';
import 'package:evoapp/feature/dop_native/features/inform_success/dop_native_inform_success_sophia_screen.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_button_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_ui_strings.dart';
import 'package:evoapp/feature/dop_native/widgets/appbar/dop_native_appbar_widget.dart';
import 'package:evoapp/feature/dop_native/widgets/dop_native_status_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_markdown_plus/flutter_markdown_plus.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../base/evo_page_state_base_test_config.dart';
import '../../../../util/flutter_test_config.dart';

// Mock classes
class MockDOPNativeRepo extends Mock implements DOPNativeRepo {}

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockDOPNativeInformSuccessCubit extends MockCubit<DOPNativeInformSuccessState>
    implements DOPNativeInformSuccessCubit {}

class MockNavigatorObserver extends Mock implements NavigatorObserver {}

class MockDopNativeButtonStyles extends Mock implements DopNativeButtonStyles {}

// Test state extension
class TestableSophiaScreenState extends DOPNativeInformSuccessSophiaScreenState {
  bool isShowDOPLoadingCalled = false;
  bool isHideDOPLoadingCalled = false;
  bool isHandleEvoApiErrorCalled = false;
  ErrorUIModel? receivedError;

  @override
  void showDOPLoading() {
    isShowDOPLoadingCalled = true;
  }

  @override
  void hideDOPLoading() {
    isHideDOPLoadingCalled = true;
  }

  @override
  Future<void> handleEvoApiError(
    ErrorUIModel? errorUIModel, {
    bool isReplaceCurrentScreen = false,
  }) async {
    isHandleEvoApiErrorCalled = true;
    receivedError = errorUIModel;
  }
}

void main() {
  late DOPNativeRepo mockDOPNativeRepo;
  late DOPNativeInformSuccessCubit mockInformSuccessCubit;
  late NavigatorObserver mockNavigatorObserver;
  late DopNativeButtonStyles mockDopNativeButtonStyles;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    registerFallbackValue(BoxFit.contain);
    registerFallbackValue(ButtonSize.medium);

    initConfigEvoPageStateBase();
    setUtilsMockInstanceForTesting();

    // Register mocks in the service locator
    mockDOPNativeRepo = MockDOPNativeRepo();
    mockInformSuccessCubit = MockDOPNativeInformSuccessCubit();
    mockDopNativeButtonStyles = MockDopNativeButtonStyles();

    getIt.registerLazySingleton<DOPNativeRepo>(() => mockDOPNativeRepo);
    getIt.registerLazySingleton<DopNativeButtonStyles>(() => mockDopNativeButtonStyles);
    getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());
    getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());
    getIt.registerLazySingleton<AuthenticationRepo>(() => MockAuthenticationRepo());

    setupMockImageProvider();

    // Setup button style mocks
    final ButtonStyle mockButtonStyle = ButtonStyle();
    when(() => mockDopNativeButtonStyles.primary(any())).thenReturn(mockButtonStyle);
    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenReturn(10);
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();
    mockNavigatorObserver = MockNavigatorObserver();

    // Default cubit behavior
    when(() => mockInformSuccessCubit.state).thenReturn(DOPNativeInformSuccessInitial());
  });

  tearDown(() {
    reset(mockDOPNativeRepo);
    reset(mockNavigatorContext);
    reset(mockNavigatorObserver);
  });

  tearDownAll(() {
    getIt.reset();
    resetUtilMockToOriginalInstance();
  });

  Widget buildTestWidget({
    DOPNativeInformSuccessCubit? cubit,
  }) {
    return MaterialApp(
      navigatorObservers: <NavigatorObserver>[mockNavigatorObserver],
      home: MediaQuery(
        data: const MediaQueryData(padding: EdgeInsets.only(top: 24)),
        child: Builder(builder: (BuildContext context) {
          when(() => globalKeyProvider.navigatorContext).thenReturn(context);

          return DOPNativeInformSuccessSophiaScreen(
            informSuccessCubit: cubit ?? mockInformSuccessCubit,
          );
        }),
      ),
    );
  }

  group('DOPNativeInformSuccessSophiaScreen', () {
    testWidgets('should have correct route settings', (WidgetTester tester) async {
      // Act
      final DOPNativeInformSuccessSophiaScreen screen = DOPNativeInformSuccessSophiaScreen();

      // Assert
      expect(screen.routeSettings.name, Screen.dopNativeInformSuccessSophiaScreen.name);
      expect(screen.eventTrackingScreenId, EventTrackingScreenId.undefined);
    });

    testWidgets('should call pushReplacementNamed correctly', (WidgetTester tester) async {
      DOPNativeInformSuccessSophiaScreen.pushReplacementNamed();

      verify(() => mockNavigatorContext
          .pushReplacementNamed(Screen.dopNativeInformSuccessSophiaScreen.name)).called(1);
    });

    testWidgets('should render initial state correctly', (WidgetTester tester) async {
      await tester.pumpWidget(buildTestWidget());
      await tester.pumpAndSettle();

      expect(find.byType(DOPNativeAppBar), findsOneWidget);
      expect(find.byType(DOPNativeStatusWidget), findsOneWidget);
      expect(find.text(DOPNativeStrings.dopNativeAdditionalInfoCall), findsOneWidget);
      expect(find.byType(MarkdownBody), findsOneWidget);

      final MarkdownBody markdownBody = tester.widget<MarkdownBody>(find.byType(MarkdownBody));
      expect(markdownBody.data, DOPNativeStrings.dopNativeWeWillCallYouAfterMinutes);

      expect(find.text(DOPNativeStrings.dopNativeAdditionalInfoCallContent), findsOneWidget);
      expect(find.text(DOPNativeStrings.dopNativeNext), findsOneWidget);
      expect(find.text(DOPNativeStrings.dopNativeAdditionalInfoCallButtonNote), findsOneWidget);
      expect(find.byType(CommonButton), findsOneWidget);
    });

    testWidgets('should display notice widget with correct content', (WidgetTester tester) async {
      await tester.pumpWidget(buildTestWidget());
      await tester.pumpAndSettle();

      final Container noticeWidget = tester.widget<Container>(
        find
            .ancestor(
              of: find.text(DOPNativeStrings.dopNativeAdditionalInfoCallContent),
              matching: find.byType(Container),
            )
            .first,
      );

      // Verify notice widget styling
      final BoxDecoration decoration = noticeWidget.decoration as BoxDecoration;
      expect(decoration.borderRadius, equals(BorderRadius.circular(12)));

      // Verify icon is present
      expect(
        find.ancestor(
          of: find.text(DOPNativeStrings.dopNativeAdditionalInfoCallContent),
          matching: find.byType(Row),
        ),
        findsOneWidget,
      );
    });

    testWidgets('should call getApplicationNextState when Next button is pressed',
        (WidgetTester tester) async {
      when(() => mockInformSuccessCubit.getApplicationNextState()).thenAnswer((_) async {});

      await tester.pumpWidget(buildTestWidget());
      await tester.pumpAndSettle();

      await tester.tap(find.text(DOPNativeStrings.dopNativeNext));
      await tester.pumpAndSettle();

      verify(() => mockInformSuccessCubit.getApplicationNextState()).called(1);
    });
  });

  group('DOPNativeInformSuccessSophiaScreenState buildNoticeWidget', () {
    testWidgets('buildNoticeWidget should create widget with correct structure',
        (WidgetTester tester) async {
      // Arrange
      final DOPNativeInformSuccessSophiaScreenState state = DOPNativeInformSuccessSophiaScreen()
          .createState() as DOPNativeInformSuccessSophiaScreenState;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: state.buildNoticeWidget(),
          ),
        ),
      );

      // Assert
      expect(find.byType(Container), findsWidgets);
      expect(find.byType(Row), findsOneWidget);
      expect(find.text(DOPNativeStrings.dopNativeAdditionalInfoCallContent), findsOneWidget);
    });

    testWidgets('buildCTA should create button with correct text and nested elements',
        (WidgetTester tester) async {
      // Arrange
      final TestableSophiaScreenState state = TestableSophiaScreenState();
      state.informSuccessCubit = mockInformSuccessCubit;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (BuildContext context) {
                return state.buildCTA();
              },
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(CommonButton), findsOneWidget);
      expect(find.text(DOPNativeStrings.dopNativeNext), findsOneWidget);
      expect(find.text(DOPNativeStrings.dopNativeAdditionalInfoCallButtonNote), findsOneWidget);
    });
  });

  group('handleDOPNativeInformSuccessStateChanged', () {
    late TestableSophiaScreenState state;

    setUp(() {
      state = TestableSophiaScreenState();
      state.informSuccessCubit = mockInformSuccessCubit;

      when(() => mockDOPNativeRepo.getApplicationState(
            token: any(named: 'token'),
            flowSelectedAt: any(named: 'flowSelectedAt'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer(
        (_) async => DOPNativeApplicationStateEntity(),
      );
    });

    test('should show loading when state is DOPNativeApplicationNextStateLoading', () {
      // Act
      state.handleDOPNativeInformSuccessStateChanged(DOPNativeApplicationNextStateLoading());

      // Assert
      expect(state.isShowDOPLoadingCalled, isTrue);
      expect(state.isHideDOPLoadingCalled, isFalse);
    });

    test(
        'should hide loading and get application state when state is DOPNativeApplicationNextStateLoaded',
        () {
      // Act
      state.handleDOPNativeInformSuccessStateChanged(DOPNativeApplicationNextStateLoaded());

      // Assert
      expect(state.isHideDOPLoadingCalled, isTrue);
      verify(() => mockDOPNativeRepo.getApplicationState(
            token: any(named: 'token'),
            flowSelectedAt: any(named: 'flowSelectedAt'),
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('should hide loading and handle error when state is DOPNativeApplicationNextStateError',
        () {
      // Arrange
      final ErrorUIModel error = ErrorUIModel(userMessage: 'Test error');

      // Act
      state.handleDOPNativeInformSuccessStateChanged(DOPNativeApplicationNextStateError(error));

      // Assert
      expect(state.isHideDOPLoadingCalled, isTrue);
      expect(state.isHandleEvoApiErrorCalled, isTrue);
      expect(state.receivedError, equals(error));
    });
  });
}
