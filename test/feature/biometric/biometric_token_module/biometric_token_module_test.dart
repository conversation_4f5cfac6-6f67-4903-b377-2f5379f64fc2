import 'dart:async';

import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/data/response/biometric_token_entity.dart';
import 'package:evoapp/feature/biometric/activate_biometric/enter_pin_popup/enter_pin_popup.dart';
import 'package:evoapp/feature/biometric/base/ext_biometric_token_entity.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/feature/biometric/utils/bio_auth_result.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/logging/evo_logging_event.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/colors_v2.dart';
import 'package:evoapp/resources/text_styles_v2.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper_impl.dart';
import 'package:evoapp/util/token_utils/evo_jwt_helper_impl.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:local_auth/local_auth.dart';
import 'package:mocktail/mocktail.dart';
import 'package:ts_bio_detect_changed/ts_bio_detect_changed.dart';

import '../../../util/flutter_test_config.dart';
import '../../../util/functions/evo_action_handler_test.dart';

class MockBiometricsAuthenticate extends Mock implements BiometricsAuthenticate {}

class MockTsBioDetectChanged extends Mock implements TsBioDetectChanged {}

class MockUserRepo extends Mock implements UserRepo {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockBiometricTokenModuleCallback extends Mock implements BiometricTokenModuleCallback {}

void main() {
  late BiometricsAuthenticate bioAuth;
  late EvoLocalStorageHelper storageHelper;
  late JwtHelper jwtHelper;
  late UserRepo userRepo;
  late TsBioDetectChanged bioDetectChanged;
  late MockLoggingRepo loggingRepo;
  late BiometricsTokenModule module;
  late EvoUtilFunction mockEvoUtilFunction;

  setUpAll(() {
    bioAuth = MockBiometricsAuthenticate();
    userRepo = MockUserRepo();
    bioDetectChanged = MockTsBioDetectChanged();

    storageHelper = MockEvoLocalStorageHelper();
    final EvoUiUtils mockEvoUtil = MockEvoUiUtils();
    EvoUiUtils.setInstanceForTesting(mockEvoUtil);

    jwtHelper = MockJwtHelper();
    loggingRepo = MockLoggingRepo();
    getIt.registerLazySingleton<LoggingRepo>(() => loggingRepo);

    getIt.registerLazySingleton<EvoUtilFunction>(() => MockEvoUtilFunction());
    getIt.registerLazySingleton<AppState>(() => AppState());
    getIt.registerLazySingleton<FeatureToggle>(() => FeatureToggle());
    getIt.registerLazySingleton<EvoTextStylesV2>(() => EvoTextStylesV2());
    getIt.registerLazySingleton<EvoColorsV2>(() => EvoColorsV2());
    mockEvoUtilFunction = getIt.get<EvoUtilFunction>();
    when(() => mockEvoUtil.showHudLoading()).thenAnswer((_) => Future<void>.value());
    when(() => mockEvoUtil.hideHudLoading()).thenAnswer((_) => Future<void>.value());
    module = BiometricsTokenModule(
      biometricsAuthenticate: bioAuth,
      userRepo: userRepo,
      secureStorageHelper: storageHelper,
      bioDetectChanged: bioDetectChanged,
      jwtHelper: jwtHelper,
    );
  });

  tearDownAll(() {
    EvoUiUtils.resetToOriginalInstance();
  });

  group('test getBiometricChanged()', () {
    tearDown(() {
      reset(bioDetectChanged);
      reset(loggingRepo);
    });

    test('work correctly if TSBioDetectChanged.biometricChange() return TRUE', () async {
      when(() => bioDetectChanged.isBiometricChanged()).thenAnswer((_) async {
        return true;
      });

      final bool isResult = await module.getBiometricChanged();

      expect(isResult, true);
    });

    test('work correctly if TSBioDetectChanged.biometricChange() return NULL', () async {
      when(() => bioDetectChanged.isBiometricChanged()).thenAnswer((_) async {
        return null;
      });

      final bool isResult = await module.getBiometricChanged();

      expect(isResult, false);
    });

    test('work correctly if TSBioDetectChanged.biometricChange() throw EXCEPTION', () async {
      when(() => bioDetectChanged.isBiometricChanged()).thenThrow(Exception());
      when(() => loggingRepo.logErrorEvent(
            errorType: any(named: 'errorType'),
            args: any(named: 'args'),
          )).thenAnswer((_) => Future<void>.value());

      final bool isResult = await module.getBiometricChanged();

      expect(isResult, false);

      verify(() => loggingRepo.logErrorEvent(
          errorType: EvoEventType.biometrics.name, args: any(named: 'args'))).called(1);
    });

    test('work correctly if TSBioDetectChanged.biometricChange() throw ERROR', () async {
      when(() => bioDetectChanged.isBiometricChanged()).thenThrow(Error());
      when(() => loggingRepo.logErrorEvent(
          errorType: any(named: 'errorType'),
          args: any(named: 'args'))).thenAnswer((_) => Future<void>.value());

      final bool isResult = await module.getBiometricChanged();

      expect(isResult, false);

      verify(() => loggingRepo.logErrorEvent(
          errorType: EvoEventType.biometrics.name, args: any(named: 'args'))).called(1);
    });
  });

  group('verify disableBiometricAuthenticatorFeature() method', () {
    late BiometricsTokenModule biometricsTokenModule;
    late EvoLocalStorageHelper mockStorageHelper;
    const String timeNow = '2023-08-17 10:00:00.000';

    setUpAll(() async {
      mockStorageHelper = MockEvoLocalStorageHelper();

      biometricsTokenModule = BiometricsTokenModule(
        biometricsAuthenticate: bioAuth,
        userRepo: userRepo,
        secureStorageHelper: mockStorageHelper,
        bioDetectChanged: bioDetectChanged,
        jwtHelper: EvoJwtHelperImpl(),
      );

      when(() => mockStorageHelper.setBiometricAuthenticator(any())).thenAnswer((_) async {
        return Future<void>.value();
      });
      when(() => mockStorageHelper.delete(key: any(named: 'key'))).thenAnswer((_) async {
        return Future<void>.value();
      });

      when(() => mockStorageHelper.saveTimeShowBiometric(any())).thenAnswer((_) async {
        return Future<void>.value();
      });

      when(() => mockEvoUtilFunction.getCurrentTimeString()).thenReturn(timeNow);
    });

    test('verify call local storage', () async {
      await biometricsTokenModule.disableBiometricAuthenticatorFeature();

      verify(() => mockStorageHelper.setBiometricAuthenticator(false)).called(1);
      verify(() => mockStorageHelper.delete(key: EvoSecureStorageHelperImpl.biometricTokenKey))
          .called(1);
      verify(() => mockStorageHelper.saveTimeShowBiometric(timeNow)).called(1);
    });
  });

  group('verify enableBiometricAuthenticatorFeature() method', () {
    late BiometricsTokenModule biometricsTokenModule;
    late EvoLocalStorageHelper mockStorageHelper;
    const String biometricToken = '123456789';

    setUpAll(() async {
      mockStorageHelper = MockEvoLocalStorageHelper();

      biometricsTokenModule = BiometricsTokenModule(
        biometricsAuthenticate: bioAuth,
        userRepo: userRepo,
        secureStorageHelper: mockStorageHelper,
        bioDetectChanged: bioDetectChanged,
        jwtHelper: EvoJwtHelperImpl(),
      );

      when(() => mockStorageHelper.setBiometricAuthenticator(any())).thenAnswer((_) async {
        return Future<void>.value();
      });
      when(() => mockStorageHelper.setBiometricToken(any())).thenAnswer((_) async {
        return Future<void>.value();
      });
      when(() => bioDetectChanged.initialize()).thenAnswer((_) async {
        return Future<void>.value();
      });
    });

    test('verify call local storage if token null', () async {
      await biometricsTokenModule.enableBiometricAuthenticatorFeature(null);

      verifyNever(() => mockStorageHelper.setBiometricAuthenticator(any()));
      verifyNever(() => mockStorageHelper.setBiometricToken(any()));
      verifyNever(() => bioDetectChanged.initialize());
    });

    test('verify call local storage if token is not empty', () async {
      await biometricsTokenModule.enableBiometricAuthenticatorFeature(biometricToken);

      verify(() => mockStorageHelper.setBiometricAuthenticator(true)).called(1);
      verify(() => mockStorageHelper.setBiometricToken(biometricToken)).called(1);
      verify(() => bioDetectChanged.initialize()).called(1);
    });
  });

  group('verify hasEnrolledBiometrics() method', () {
    setUpAll(() async {
      when(() => bioAuth.getAvailableBiometricType()).thenAnswer((_) async {
        return <BiometricType>[];
      });
    });

    tearDownAll(() {
      reset(bioAuth);
    });

    test('verify biometricsAuthenticate.getAvailableBiometricType()', () async {
      await module.hasEnrolledBiometrics();

      verify(() => bioAuth.getAvailableBiometricType()).called(1);
    });

    test('verify if getAvailableBiometricType return empty', () async {
      final bool isResult = await module.hasEnrolledBiometrics();

      expect(isResult, false);
    });

    test('verify if getAvailableBiometricType return not empty', () async {
      when(() => bioAuth.getAvailableBiometricType()).thenAnswer((_) async {
        return <BiometricType>[BiometricType.face];
      });

      final bool isResult = await module.hasEnrolledBiometrics();

      expect(isResult, true);
    });
  });

  group('verify isEnableBiometricAuthenticator() method', () {
    setUpAll(() async {
      when(() => storageHelper.isEnableBiometricAuthenticator()).thenAnswer((_) async {
        return true;
      });
    });

    tearDownAll(() {
      reset(storageHelper);
    });

    test('verify secureStorageHelper.isEnableBiometricAuthenticator()', () async {
      await module.isEnableBiometricAuthenticator();

      verify(() => storageHelper.isEnableBiometricAuthenticator()).called(1);
    });
  });

  group('verify isBiometricTokenUsable() method', () {
    const String biometricToken = 'biometricToken';

    setUpAll(() async {
      when(() => storageHelper.getBiometricToken()).thenAnswer((_) async {
        return biometricToken;
      });
      when(() => jwtHelper.isCanUse(biometricToken)).thenReturn(true);
    });

    tearDownAll(() {
      reset(jwtHelper);
      reset(storageHelper);
    });

    test('verify secureStorageHelper.getBiometricToken()', () async {
      final bool isUsable = await module.isBiometricTokenUsable();

      verify(() => storageHelper.getBiometricToken()).called(1);
      expect(isUsable, true);
    });
  });

  group('test enable() method', () {
    tearDownAll(() {
      module.callback = null;
    });

    tearDown(() {
      reset(bioAuth);
      reset(userRepo);
    });

    test('should return early if already processing', () async {
      module.isProcessing = true;

      await module.enable();

      verifyNever(() => bioAuth.authenticate(localizedReason: any(named: 'localizedReason')));
    });

    test('should handle biometric authentication failure', () async {
      module.isProcessing = false;
      bool onErrorCalled = false;
      when(() => bioAuth.authenticate(localizedReason: any(named: 'localizedReason')))
          .thenAnswer((_) async => BioAuthResult(isAuthSuccess: false));
      final BiometricTokenModuleCallback mockCallBack = MockBiometricTokenModuleCallback();
      when(() => mockCallBack.onError(type: BiometricTokenModuleErrorType.biometrics))
          .thenAnswer((_) {
        onErrorCalled = true;
      });
      await module.enable(callback: mockCallBack);

      verify(() => bioAuth.authenticate(localizedReason: any(named: 'localizedReason'))).called(1);
      expect(module.isProcessing, false);
      expect(onErrorCalled, true);
    });

    test('should handle biometric authentication success and got new token fail', () async {
      module.isProcessing = false;
      bool onErrorCalled = false;
      final BiometricTokenModuleCallback mockCallBack = MockBiometricTokenModuleCallback();
      when(() => mockCallBack.onError(
            type: BiometricTokenModuleErrorType.apiError,
            userMessage: any(named: 'userMessage'),
            error: any(named: 'error'),
            bioError: any(named: 'bioError'),
          )).thenAnswer((_) {
        onErrorCalled = true;
      });

      when(() => bioAuth.authenticate(localizedReason: any(named: 'localizedReason')))
          .thenAnswer((_) async => BioAuthResult(isAuthSuccess: true));
      when(() => userRepo.getBiometricTokenByPin(mockConfig: any(named: 'mockConfig'))).thenAnswer(
          (_) async => BiometricTokenEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.BAD_REQUEST, response: <String, dynamic>{})));

      await module.enable(callback: mockCallBack);

      expect(onErrorCalled, true);
    });

    test('should handle biometric authentication success and got invalid challenge when request new token', () async {
      module.isProcessing = false;
      final BiometricTokenModuleCallback mockCallBack = MockBiometricTokenModuleCallback();
      bool onErrorCalled = false;
      final Completer<void> onSuccessCompleter = Completer<void>();
      when(() => mockCallBack.onError(
        type: BiometricTokenModuleErrorType.noSupportExtraChallenge,
        userMessage: any(named: 'userMessage'),
        error: any(named: 'error'),
        bioError: any(named: 'bioError'),
      )).thenAnswer((_) {
        onErrorCalled = true;
        onSuccessCompleter.complete();
      });

      when(() => bioAuth.authenticate(localizedReason: any(named: 'localizedReason')))
          .thenAnswer((_) async => BioAuthResult(isAuthSuccess: true));
      when(() => userRepo.getBiometricTokenByPin(mockConfig: any(named: 'mockConfig'))).thenAnswer(
              (_) async => BiometricTokenEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.SUCCESS, response: <String, dynamic>{
                'data': <String, dynamic>{
                  'challenge_type': 'invalid'
                }
              })));

      await module.enable(callback: mockCallBack);
      await onSuccessCompleter.future;

      expect(onErrorCalled, true);
    });

    test('should handle biometric authentication success and process token success', () async {
      module.isProcessing = false;
      bool onSuccessCalled = false;
      final Completer<void> onSuccessCompleter = Completer<void>();
      final BiometricTokenModuleCallback mockCallBack = MockBiometricTokenModuleCallback();
      when(() => mockCallBack.onSuccess()).thenAnswer((_) {
        onSuccessCalled = true;
        onSuccessCompleter.complete();
      });

      when(() => bioAuth.authenticate(localizedReason: any(named: 'localizedReason')))
          .thenAnswer((_) async => BioAuthResult(isAuthSuccess: true));
      when(() => userRepo.getBiometricTokenByPin(mockConfig: any(named: 'mockConfig'))).thenAnswer(
          (_) async => BiometricTokenEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: <String, dynamic>{'biometric_token': 'biometric_token'})));

      await module.enable(callback: mockCallBack);

      await onSuccessCompleter.future;
      expect(onSuccessCalled, true);
    });
  });

  group('test show challenge bottom sheet', () {
    late CommonImageProvider commonImageProvider;

    setUpAll(() {
      getIt.registerLazySingleton(() => GlobalKeyProvider());
      getIt.registerLazySingleton<UserRepo>(() => MockUserRepo());
      getItRegisterTextStyle();
      getItRegisterColor();
      getIt.registerLazySingleton<CommonImageProvider>(() => MockEvoImageProvider());

      commonImageProvider = getIt.get<CommonImageProvider>();

      when(() => commonImageProvider.asset(
        any(),
        width: any(named: 'width'),
        height: any(named: 'height'),
        color: any(named: 'color'),
        fit: any(named: 'fit'),
        cornerRadius: any(named: 'cornerRadius'),
        cacheWidth: any(named: 'cacheWidth'),
        cacheHeight: any(named: 'cacheHeight'),
        package: any(named: 'package'),
      )).thenAnswer((_) => Container());
    });

    tearDownAll(() {
      getIt.unregister<GlobalKeyProvider>();
      getIt.unregister<UserRepo>();
      getItUnRegisterTextStyle();
      getItUnregisterColor();
      getIt.unregister<CommonImageProvider>();
    });

    testWidgets('test show challenge bottom sheet', (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(MaterialApp(
        navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
        home: Scaffold(
          body: Builder(builder: (_) {
            return ElevatedButton(onPressed: () {
              module.showChallenge(ChallengeType.pin, module.extraChallengeBuilder(ChallengeType.pin));
            }, child: Text(''));
          }),
        ),
      ));
      await widgetTester.tap(find.byType(ElevatedButton));
      await widgetTester.pump(Duration(milliseconds: 1000));
      expect(find.byType(EnterPinPopup), findsOneWidget);
    });
  });

  group('test BiometricChallengeCallback', () {
    test('should handle API error correctly', () async {
      final ErrorUIModel error = ErrorUIModel(userMessage: 'API Error');
      module.isProcessing = true;
      module.onBioChallengeError(error);
      expect(module.isProcessing, false);
    });

    test('should handle challenge cancellation correctly', () async {
      module.isProcessing = true;
      module.onBioChallengeCancel();
      expect(module.isProcessing, false);
    });

    test('should handle API success correctly', () async {
      module.isProcessing = true;
      module.onBioChallengeSuccess('');
      expect(module.isProcessing, false);
    });
  });
}
