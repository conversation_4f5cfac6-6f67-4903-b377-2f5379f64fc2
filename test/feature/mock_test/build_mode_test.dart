import 'package:evoapp/feature/mock_test/build_mode.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EvoBuildModeImpl', () {
    late BuildMode buildMode;

    setUp(() {
      buildMode = EvoBuildModeImpl();
    });

    test('isDebugMode returns kDebugMode', () {
      expect(buildMode.isDebugMode(), kDebugMode);
    });

    test('isProfileMode returns kProfileMode', () {
      expect(buildMode.isProfileMode(), kProfileMode);
    });

    test('isReleaseMode returns kReleaseMode', () {
      expect(buildMode.isReleaseMode(), kReleaseMode);
    });
  });
}
