import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/response/reset_pin_entity.dart';
import 'package:evoapp/data/response/sign_in_entity.dart';
import 'package:evoapp/data/response/sign_in_otp_entity.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/verify_otp/verify_otp_cubit.dart';
import 'package:evoapp/feature/verify_otp/verify_otp_page.dart';
import 'package:evoapp/resources/colors_v2.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/evo_authentication_helper.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/util/otp_auto_fill/otp_auto_fill.dart';
import 'package:flutter_common_package/widget/otp/otp_widget.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../base/evo_page_state_base_test_config.dart';
import '../../util/flutter_test_config.dart';

// Create mock classes
class MockVerifyOtpCubit extends Mock implements VerifyOtpCubit {}

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockEvoUiUtils extends Mock implements EvoUiUtils {}

class MockEvoDialogHelper extends Mock implements EvoDialogHelper {}

class MockEvoAuthenticationHelper extends Mock implements EvoAuthenticationHelper {}

class MockOtpAutoFill extends Mock implements OtpAutoFill {}

class MockDevicePlatform extends Mock implements DevicePlatform {}

void main() {
  late MockAuthenticationRepo mockAuthenticationRepo;
  late MockDevicePlatform mockDevicePlatform;
  late MockOtpAutoFill mockOtpAutoFill;

  // Test data
  const String testPhoneNumber = '+84123456789';
  const String testSessionToken = 'test_session_token';
  const int testOtpResendSecs = 60;
  const String testOtp = '123456';

  final SignInEntity signInEntity = SignInEntity.fromBaseResponse(BaseResponse(
    statusCode: CommonHttpClient.SUCCESS,
    response: <String, dynamic>{
      'verdict': SignInEntity.verdictSuccess,
      'data': <String, dynamic>{
        'otp_resend_secs': testOtpResendSecs,
        'session_token': testSessionToken,
      },
    },
  ));

  final SignInOtpEntity signInOtpEntity = SignInOtpEntity.fromBaseResponse(BaseResponse(
    statusCode: CommonHttpClient.SUCCESS,
    response: <String, dynamic>{
      'verdict': SignInOtpEntity.verdictSuccess,
      'data': <String, dynamic>{
        'access_token': 'test_access_token',
      },
    },
  ));

  final ResetPinEntity resetPinEntity = ResetPinEntity.fromBaseResponse(BaseResponse(
    statusCode: CommonHttpClient.SUCCESS,
    response: <String, dynamic>{
      'verdict': ResetPinEntity.verdictSuccess,
      'data': <String, dynamic>{
        'otp_resend_secs': testOtpResendSecs,
        'session_token': testSessionToken,
      },
    },
  ));

  void setupMocks() {
    // Mock AuthenticationRepo
    when(() => mockAuthenticationRepo.login(
          any(),
          otp: any(named: 'otp'),
          sessionToken: any(named: 'sessionToken'),
          facialVerificationVersion: any(named: 'facialVerificationVersion'),
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer((_) async => signInOtpEntity);

    when(() => mockAuthenticationRepo.verifyPhone(
          phoneNumber: any(named: 'phoneNumber'),
          type: any(named: 'type'),
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer((_) async => signInEntity);

    when(() => mockAuthenticationRepo.requestResetPin(
          any(),
          otp: any(named: 'otp'),
          sessionToken: any(named: 'sessionToken'),
          phoneNumber: any(named: 'phoneNumber'),
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer((_) async => resetPinEntity);

    // Mock EvoUiUtils
    when(() => EvoUiUtils().showHudLoading()).thenAnswer((_) async {});
    when(() => EvoUiUtils().hideHudLoading()).thenAnswer((_) async {});

    // Mock DevicePlatform
    when(() => mockDevicePlatform.isAndroid()).thenReturn(true);
    when(() => mockDevicePlatform.isIOS()).thenReturn(false);

    // Mock OtpAutoFill
    when(() => mockOtpAutoFill.startUserConsentListening(
          onExtractCode: any(named: 'onExtractCode'),
          onCode: any(named: 'onCode'),
          onError: any(named: 'onError'),
        )).thenAnswer((_) async {});
    when(() => mockOtpAutoFill.stopListening()).thenAnswer((_) async {});

    setupMockDialogHelper();

    // Setup EvoSnackBar mock
    setUpMockSnackBarForTest();

    // Mock EvoUtilFunction.getFacialVerificationVersion
    when(() => getIt<EvoUtilFunction>().getFacialVerificationVersion())
        .thenReturn(FacialVerificationVersion.version_3);
  }

  setUpAll(() {
    registerFallbackValue(VerifyOtpType.signIn);
    registerFallbackValue(ErrorUIModel());
    registerFallbackValue(FocusNode());
    registerFallbackValue(TypeLogin.otp);
    registerFallbackValue(FacialVerificationVersion.version_3);
    registerFallbackValue(ResetPinType.verifyOtp);
  });

  setUp(() {
    EvoAuthenticationHelper.setInstanceForTesting(MockEvoAuthenticationHelper());

    mockAuthenticationRepo = MockAuthenticationRepo();
    mockDevicePlatform = MockDevicePlatform();
    mockOtpAutoFill = MockOtpAutoFill();

    // Setup utils for page state
    initConfigEvoPageStateBase();
    getIt.registerLazySingleton<EvoColorsV2>(() => EvoColorsV2());
    getIt.registerLazySingleton<EvoTextStylesV2>(() => EvoTextStylesV2());
    setUpMockConfigEvoPageStateBase();
    setupMockImageProvider();
    setUpOneLinkDeepLinkRegExForTest();

    // Override getIt registrations
    getIt.registerFactory<AuthenticationRepo>(() => mockAuthenticationRepo);
    getIt.registerLazySingleton<FeatureToggle>(() => FeatureToggle());
    getIt.registerFactory<OtpAutoFill>(() => mockOtpAutoFill);
    getIt.registerFactory<DevicePlatform>(() => mockDevicePlatform);

    when(() => getIt<CommonUtilFunction>().clearDataOnTokenInvalid(
          clearAllNotifications: any(named: 'clearAllNotifications'),
        )).thenAnswer((_) async => Future<void>.value());

    when(() => getIt<CommonUtilFunction>().delayAndRequestFocus(any())).thenAnswer((_) async {});

    setupMocks();
  });

  tearDown(() {
    // Reset GetIt registrations after each test
    getIt.reset();
  });

  Future<void> pumpVerifyOtpPage(
    WidgetTester tester, {
    VerifyOtpType verifyOtpType = VerifyOtpType.signIn,
    String? phoneNumber = testPhoneNumber,
    int? otpResendSecs = testOtpResendSecs,
    String? sessionToken = testSessionToken,
    void Function(VerifyOtpState)? onPopSuccess,
  }) async {
    await tester.pumpWidget(
      MaterialApp(
        home: VerifyOtpPage(
          phoneNumber: phoneNumber,
          otpResendSecs: otpResendSecs,
          sessionToken: sessionToken,
          verifyOtpType: verifyOtpType,
          onPopSuccess: onPopSuccess,
        ),
      ),
    );
    // Use pump instead of pumpAndSettle to avoid timeout issues
    await tester.pump();
    // Give some time for the widget to initialize
    await tester.pump(const Duration(milliseconds: 100));
  }

  SignInOtpPageState getPageState(WidgetTester tester) {
    return tester.state(find.byType(VerifyOtpPage)) as SignInOtpPageState;
  }

  group('VerifyOtpPage test pushNamed method', () {
    test('pushNamed', () {
      VerifyOtpPage.pushNamed(
        phoneNumber: testPhoneNumber,
        otpResendSecs: testOtpResendSecs,
        sessionToken: testSessionToken,
      );
      verify(() => getIt<CommonNavigator>().pushNamed(
            any(),
            Screen.verifyOtpScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });
  });

  group('VerifyOtpPage initialization', () {
    testWidgets('should initialize with correct phone number and resend seconds',
        (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester);
      final SignInOtpPageState state = getPageState(tester);

      expect(state.widget.phoneNumber, testPhoneNumber);
      expect(state.widget.otpResendSecs, testOtpResendSecs);
      expect(state.widget.sessionToken, testSessionToken);
      expect(state.otpResendSecs, testOtpResendSecs);
      expect(state.sessionToken, testSessionToken);
    });
  });

  group('VerifyOtpPage OTP interactions', () {
    testWidgets('should call verifyOtp when OTP is submitted', (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester);
      // Simulate OTP submission
      final OtpWidget otpWidget = tester.widget(find.byType(OtpWidget));
      otpWidget.onSubmit!(testOtp);

      // Verify the correct method was called
      verify(() => mockAuthenticationRepo.login(
        any(),
        otp: any(named: 'otp'),
        sessionToken: any(named: 'sessionToken'),
        facialVerificationVersion: any(named: 'facialVerificationVersion'),
        mockConfig: any(named: 'mockConfig'),
      )).called(1);
      await tester.pump(Duration(milliseconds: 20));
    });

    testWidgets('should call resendOtp when resend is triggered', (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester);
      // Simulate resend OTP
      final OtpWidget otpWidget = tester.widget(find.byType(OtpWidget));
      otpWidget.onResendOtp!();

      // Verify the correct method was called
      verify(() => mockAuthenticationRepo.verifyPhone(
        phoneNumber: any(named: 'phoneNumber'),
        type: any(named: 'type'),
        mockConfig: any(named: 'mockConfig'),
      )).called(1);
      await tester.pump(Duration(milliseconds: 20));
    });

    testWidgets('should call verifyOtp with resetPin type', (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester, verifyOtpType: VerifyOtpType.resetPin);
      // Simulate OTP submission
      final OtpWidget otpWidget = tester.widget(find.byType(OtpWidget));
      otpWidget.onSubmit!(testOtp);

      // Verify the correct method was called
      verify(() => mockAuthenticationRepo.requestResetPin(
        any(),
        otp: any(named: 'otp'),
        sessionToken: any(named: 'sessionToken'),
        phoneNumber: any(named: 'phoneNumber'),
        mockConfig: any(named: 'mockConfig'),
      )).called(1);
      await tester.pump(Duration(milliseconds: 20));
    });

    testWidgets('should call resendOtp with resetPin type', (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester, verifyOtpType: VerifyOtpType.resetPin);
      // Simulate resend OTP
      final OtpWidget otpWidget = tester.widget(find.byType(OtpWidget));
      otpWidget.onResendOtp!();

      // Verify the correct method was called
      verify(() => mockAuthenticationRepo.requestResetPin(
        any(),
        otp: any(named: 'otp'),
        sessionToken: any(named: 'sessionToken'),
        phoneNumber: any(named: 'phoneNumber'),
        mockConfig: any(named: 'mockConfig'),
      )).called(1);
      await tester.pump(Duration(milliseconds: 20));
    });
  });

  group('VerifyOtpPage state handling', () {
    testWidgets('should handle VerifyOtpLoading state correctly', (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester);
      final SignInOtpPageState state = getPageState(tester);

      // Simulate VerifyOtpLoading state
      state.handleShowLoading(VerifyOtpLoading());

      // Verify loading indicator is shown
      verify(() => EvoUiUtils().showHudLoading()).called(1);
    });

    testWidgets('should hide loading when state is not VerifyOtpLoading',
        (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester);
      final SignInOtpPageState state = getPageState(tester);

      // Simulate non-loading state
      state.handleShowLoading(VerifyOtpInitial());

      // Verify loading indicator is hidden
      verify(() => EvoUiUtils().hideHudLoading()).called(2);
    });

    testWidgets('should handle VerifyOtpFailed state correctly', (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester);
      final SignInOtpPageState state = getPageState(tester);

      final ErrorUIModel errorUIModel = ErrorUIModel(userMessage: 'Test error');

      // Simulate VerifyOtpFailed state
      state.handleListener(VerifyOtpFailed(errorUIModel));

      // Verify error handling is called
      verify(() => EvoUiUtils().hideHudLoading()).called(2);
    });

    testWidgets('should handle LimitResendOtp state correctly', (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester);
      final SignInOtpPageState state = getPageState(tester);

      const String errorText = 'Limit exceeded';

      // Simulate LimitResendOtp state
      state.handleListener(LimitResendOtp(errorText));

      // Verify dialog is shown
      verify(() => EvoDialogHelper().showDialogConfirm(
            title: any(named: 'title'),
            content: errorText,
            dialogId: EvoDialogId.otpBlockedErrorDialog,
            isDismissible: false,
            textPositive: EvoStrings.moveToHome,
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
    });
  });

  group('VerifyOtpPage SignIn ResendOtp handling', () {
    testWidgets('should handle successful signIn resend OTP', (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester);
      final SignInOtpPageState state = getPageState(tester);

      final SignInEntity successEntity = SignInEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'verdict': SignInEntity.verdictSuccess,
          'data': <String, dynamic>{
            'otp_resend_secs': 90,
            'session_token': 'new_session_token',
          },
        },
      ));

      // Simulate successful resend OTP
      state.handleSignInResendOtp(ResendOtpCompleted(successEntity));

      expect(state.otpResendSecs, 90);
      expect(state.sessionToken, 'new_session_token');
    });

    testWidgets('should handle user not existed verdict in signIn resend OTP',
        (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester);
      final SignInOtpPageState state = getPageState(tester);

      final SignInEntity userNotExistedEntity = SignInEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'verdict': SignInEntity.verdictUserNotExisted,
          'data': <String, dynamic>{},
        },
      ));

      // Simulate user not existed resend OTP
      state.handleSignInResendOtp(ResendOtpCompleted(userNotExistedEntity));

      // Verify navigation to create evo card screen
      verify(() => getIt<CommonNavigator>().pushNamed(
            any(),
            Screen.createEvoCardScreen.name,
          )).called(1);
    });

    testWidgets('should handle limit exceeded verdict in signIn resend OTP',
        (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester);
      final SignInOtpPageState state = getPageState(tester);

      final SignInEntity limitExceededEntity = SignInEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'verdict': SignInEntity.verdictLimitExceeded,
          'user_message': 'Limit exceeded message',
          'data': <String, dynamic>{},
        },
      ));

      // Simulate limit exceeded resend OTP
      state.handleSignInResendOtp(ResendOtpCompleted(limitExceededEntity));

      // Verify dialog is shown
      verify(() => EvoDialogHelper().showDialogConfirm(
            title: any(named: 'title'),
            content: any(named: 'content'),
            dialogId: EvoDialogId.otpBlockedErrorDialog,
            isDismissible: false,
            textPositive: EvoStrings.moveToHome,
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
    });

    testWidgets('should handle invalid token in signIn resend OTP', (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester);
      final SignInOtpPageState state = getPageState(tester);

      final SignInEntity invalidTokenEntity = SignInEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.INVALID_TOKEN,
        response: <String, dynamic>{
          'verdict': SignInEntity.verdictSuccess,
          'data': <String, dynamic>{},
        },
      ));

      // Mock EvoAuthenticationHelper
      when(() => EvoAuthenticationHelper().showDialogSessionTokenExpired(type: any(named: 'type')))
          .thenAnswer((_) async {});

      // Simulate invalid token resend OTP
      state.handleSignInResendOtp(ResendOtpCompleted(invalidTokenEntity));

      // Verify session token expired dialog is shown
      verify(() => EvoAuthenticationHelper()
          .showDialogSessionTokenExpired()).called(1);
    });
  });

  group('VerifyOtpPage SignIn VerifyOtp handling', () {
    testWidgets('should handle successful signIn verify OTP', (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester);
      final SignInOtpPageState state = getPageState(tester);

      state.widget.onPopSuccess?.call(VerifyOtpCompleted(signInOtpEntity));

      final SignInOtpEntity successEntity = SignInOtpEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'verdict': SignInOtpEntity.verdictSuccess,
          'data': <String, dynamic>{
            'access_token': 'test_access_token',
          },
        },
      ));

      // Simulate successful verify OTP
      state.handleSignInVerifyOtp(VerifyOtpCompleted(successEntity));

      // Verify navigation back
      verify(() => getIt<CommonNavigator>().pop(any())).called(1);
    });

    testWidgets('should handle incorrect OTP verdict in signIn verify OTP',
        (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester);
      final SignInOtpPageState state = getPageState(tester);

      final SignInOtpEntity incorrectOtpEntity = SignInOtpEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'verdict': SignInOtpEntity.verdictIncorrectOtp,
          'user_message': 'Incorrect OTP',
          'data': <String, dynamic>{},
        },
      ));

      // Simulate incorrect OTP verify
      state.handleSignInVerifyOtp(VerifyOtpCompleted(incorrectOtpEntity));

      expect(state.errorText, 'Mã OTP không đúng, vui lòng kiểm tra và nhập lại.');
    });

    testWidgets('should handle expired OTP verdict in signIn verify OTP',
        (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester);
      final SignInOtpPageState state = getPageState(tester);

      final SignInOtpEntity expiredOtpEntity = SignInOtpEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'verdict': SignInOtpEntity.verdictExpiredOTP,
          'user_message': 'Expired OTP',
          'data': <String, dynamic>{},
        },
      ));

      // Simulate expired OTP verify
      state.handleSignInVerifyOtp(VerifyOtpCompleted(expiredOtpEntity));

      expect(state.errorText, 'Mã OTP không đúng, vui lòng kiểm tra và nhập lại.');
    });

    testWidgets('should handle limit exceeded verdict in signIn verify OTP',
        (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester);
      final SignInOtpPageState state = getPageState(tester);

      final SignInOtpEntity limitExceededEntity = SignInOtpEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'verdict': SignInOtpEntity.verdictLimitExceeded,
          'user_message': 'Limit exceeded',
          'data': <String, dynamic>{},
        },
      ));

      // Simulate limit exceeded verify OTP
      state.handleSignInVerifyOtp(VerifyOtpCompleted(limitExceededEntity));

      expect(state.errorText, 'Mã OTP không đúng, vui lòng kiểm tra và nhập lại.');
    });

    testWidgets('should handle invalid token in signIn verify OTP', (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester);
      final SignInOtpPageState state = getPageState(tester);

      final SignInOtpEntity invalidTokenEntity = SignInOtpEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.INVALID_TOKEN,
        response: <String, dynamic>{
          'verdict': SignInOtpEntity.verdictSuccess,
          'data': <String, dynamic>{},
        },
      ));

      // Mock EvoAuthenticationHelper
      when(() => EvoAuthenticationHelper().showDialogSessionTokenExpired(type: any(named: 'type')))
          .thenAnswer((_) async {});

      // Simulate invalid token verify OTP
      state.handleSignInVerifyOtp(VerifyOtpCompleted(invalidTokenEntity));

      // Verify session token expired dialog is shown
      verify(() => EvoAuthenticationHelper()
          .showDialogSessionTokenExpired()).called(1);
    });
  });

  group('VerifyOtpPage ResetPin ResendOtp handling', () {
    testWidgets('should handle successful resetPin resend OTP', (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester, verifyOtpType: VerifyOtpType.resetPin);
      final SignInOtpPageState state = getPageState(tester);

      final ResetPinEntity successEntity = ResetPinEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'verdict': ResetPinEntity.verdictSuccess,
          'data': <String, dynamic>{
            'otp_resend_secs': 120,
            'session_token': 'new_reset_session_token',
          },
        },
      ));

      // Simulate successful resend OTP
      state.handleResetPinResendOtp(ResendOtpCompleted(successEntity));

      expect(state.otpResendSecs, 120);
      expect(state.sessionToken, 'new_reset_session_token');
    });

    testWidgets('should handle locked resource verdict in resetPin resend OTP',
        (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester, verifyOtpType: VerifyOtpType.resetPin);
      final SignInOtpPageState state = getPageState(tester);

      final ResetPinEntity lockedResourceEntity = ResetPinEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'verdict': ResetPinEntity.verdictLockedResource,
          'user_message': 'Resource locked message',
          'data': <String, dynamic>{},
        },
      ));

      // Simulate locked resource resend OTP
      state.handleResetPinResendOtp(ResendOtpCompleted(lockedResourceEntity));

      // Verify dialog is shown
      verify(() => EvoDialogHelper().showDialogConfirm(
            title: any(named: 'title'),
            content: any(named: 'content'),
            dialogId: EvoDialogId.otpBlockedErrorDialog,
            isDismissible: false,
            textPositive: EvoStrings.moveToHome,
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
    });

    testWidgets('should handle limit exceeded verdict in resetPin resend OTP',
        (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester, verifyOtpType: VerifyOtpType.resetPin);
      final SignInOtpPageState state = getPageState(tester);

      final ResetPinEntity limitExceededEntity = ResetPinEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'verdict': ResetPinEntity.verdictLimitExceeded,
          'user_message': 'Reset pin limit exceeded',
          'data': <String, dynamic>{},
        },
      ));

      // Simulate limit exceeded resend OTP
      state.handleResetPinResendOtp(ResendOtpCompleted(limitExceededEntity));

      // Verify dialog is shown
      verify(() => EvoDialogHelper().showDialogConfirm(
            title: any(named: 'title'),
            content: any(named: 'content'),
            dialogId: EvoDialogId.otpBlockedErrorDialog,
            isDismissible: false,
            textPositive: EvoStrings.moveToHome,
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
    });
  });

  group('VerifyOtpPage ResetPin VerifyOtp handling', () {
    testWidgets('should handle successful resetPin verify OTP', (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester, verifyOtpType: VerifyOtpType.resetPin);
      final SignInOtpPageState state = getPageState(tester);

      final ResetPinEntity successEntity = ResetPinEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'verdict': ResetPinEntity.verdictSuccess,
          'data': <String, dynamic>{},
        },
      ));

      // Simulate successful verify OTP
      state.handleResetPinVerifyOtp(VerifyOtpCompleted(successEntity));

      // Verify navigation back
      verify(() => getIt<CommonNavigator>().pop(any())).called(1);
    });

    testWidgets('should handle invalid token in resetPin verify OTP', (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester, verifyOtpType: VerifyOtpType.resetPin);
      final SignInOtpPageState state = getPageState(tester);

      final ResetPinEntity invalidTokenEntity = ResetPinEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.INVALID_TOKEN,
        response: <String, dynamic>{
          'verdict': ResetPinEntity.verdictSuccess,
          'data': <String, dynamic>{},
        },
      ));

      // Mock EvoAuthenticationHelper
      when(() => EvoAuthenticationHelper().showDialogSessionTokenExpired(type: any(named: 'type')))
          .thenAnswer((_) async {});

      // Simulate invalid token verify OTP
      state.handleResetPinVerifyOtp(VerifyOtpCompleted(invalidTokenEntity));

      // Verify session token expired dialog is shown
      verify(() => EvoAuthenticationHelper()
          .showDialogSessionTokenExpired(type: VerifyOtpType.resetPin)).called(1);
    });
  });

  group('VerifyOtpPage helper methods', () {
    testWidgets('should return correct title for signIn dialog block', (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester);
      final SignInOtpPageState state = getPageState(tester);

      final String title = state.titleDialogBlock();
      expect(title, EvoStrings.limitOtp);
    });

    testWidgets('should return correct title for resetPin dialog block', (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester, verifyOtpType: VerifyOtpType.resetPin);
      final SignInOtpPageState state = getPageState(tester);

      final String title = state.titleDialogBlock();
      expect(title, EvoStrings.titleLimitResetPin);
    });

    testWidgets('should return correct content for signIn dialog block', (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester);
      final SignInOtpPageState state = getPageState(tester);

      final String content = state.contentDialogBlock();
      expect(content, EvoStrings.descLimitOtp);
    });

    testWidgets('should return correct content for resetPin dialog block',
        (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester, verifyOtpType: VerifyOtpType.resetPin);
      final SignInOtpPageState state = getPageState(tester);

      final String content = state.contentDialogBlock();
      expect(content, EvoStrings.errorLimitResetPin);
    });

    testWidgets('should handle showDialogBlock correctly', (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester);
      final SignInOtpPageState state = getPageState(tester);

      // Call showDialogBlock with custom content
      state.showDialogBlock(content: 'Custom error message');

      // Verify dialog is shown with correct parameters
      verify(() => EvoDialogHelper().showDialogConfirm(
            title: EvoStrings.limitOtp,
            content: 'Custom error message',
            dialogId: EvoDialogId.otpBlockedErrorDialog,
            isDismissible: false,
            textPositive: EvoStrings.moveToHome,
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
    });

    testWidgets('should handle handleOtpSuccess correctly', (WidgetTester tester) async {
      bool onPopSuccessCalled = false;
      VerifyOtpState? receivedState;

      await pumpVerifyOtpPage(
        tester,
        onPopSuccess: (VerifyOtpState state) {
          onPopSuccessCalled = true;
          receivedState = state;
        },
      );
      final SignInOtpPageState state = getPageState(tester);

      final VerifyOtpCompleted testState = VerifyOtpCompleted(signInOtpEntity);

      // Call handleOtpSuccess
      state.handleOtpSuccess(testState);

      // Verify navigation back and callback
      verify(() => getIt<CommonNavigator>().pop(any())).called(1);
      expect(onPopSuccessCalled, true);
      expect(receivedState, testState);
    });

    testWidgets('should handle handleSessionTokenExpired correctly', (WidgetTester tester) async {
      await pumpVerifyOtpPage(tester);
      final SignInOtpPageState state = getPageState(tester);

      // Mock EvoAuthenticationHelper
      when(() => EvoAuthenticationHelper().showDialogSessionTokenExpired(type: any(named: 'type')))
          .thenAnswer((_) async {});

      // Call handleSessionTokenExpired
      state.handleSessionTokenExpired();

      // Verify session token expired dialog is shown
      verify(() => EvoAuthenticationHelper()
          .showDialogSessionTokenExpired()).called(1);
    });
  });
}
