import 'package:evoapp/feature/dop_native/widgets/dop_entry_point_widget.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/home_screen/non_user/v2/story/widgets/story_header_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/images.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/evo_action_handler.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../../util/flutter_test_config.dart';

void main() {
  late MockFeatureToggle mockFeatureToggle;
  late CommonImageProvider commonImageProvider;

  setUpAll(() {
    mockFeatureToggle = MockFeatureToggle();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterButtonStyle();
    registerFallbackValue(BoxFit.fill);
    getIt.registerLazySingleton<FeatureToggle>(() => mockFeatureToggle);
    commonImageProvider = getIt.get<CommonImageProvider>();
    EvoActionHandler.setInstanceForTesting(MockEvoActionHandler());
    when(() => commonImageProvider.asset(
          any(),
          fit: any(named: 'fit'),
          width: any(named: 'width'),
          height: any(named: 'height'),
        )).thenReturn(Container());

    when(() => EvoActionHandler().openAuthenticationScreen())
        .thenAnswer((_) => Future<void>.value());
  });

  tearDownAll(() {
    getIt.reset();
    EvoActionHandler.resetToOriginalInstance();
  });

  Widget createWidgetUnderTest() {
    return MaterialApp(
      home: Scaffold(
        body: StoryHeaderWidget(),
      ),
    );
  }

  testWidgets('StoryHeaderWidget displays icon, login button, and DOP native entry point',
      (WidgetTester tester) async {
    when(() => mockFeatureToggle.enableDOPNativeFeature).thenReturn(true);

    await tester.pumpWidget(createWidgetUnderTest());

    verify(() => commonImageProvider.asset(EvoImages.icEvo,
        width: any(named: 'width'), fit: any(named: 'fit'))).called(1);
    expect(find.text(EvoStrings.storyCtaLogin), findsOneWidget);
    expect(find.byType(DopNativeEntryPoint), findsOneWidget);
  });

  testWidgets('StoryHeaderWidget hides DOP native entry point when feature is disabled',
      (WidgetTester tester) async {
    when(() => mockFeatureToggle.enableDOPNativeFeature).thenReturn(false);

    await tester.pumpWidget(createWidgetUnderTest());

    verify(() => commonImageProvider.asset(EvoImages.icEvo,
        width: any(named: 'width'), fit: any(named: 'fit'))).called(1);
    expect(find.text(EvoStrings.storyCtaLogin), findsOneWidget);
    expect(find.byType(DopNativeEntryPoint), findsNothing);
  });

  testWidgets('StoryHeaderWidget login button triggers authentication screen',
      (WidgetTester tester) async {
    when(() => mockFeatureToggle.enableDOPNativeFeature).thenReturn(true);

    await tester.pumpWidget(createWidgetUnderTest());

    final Finder loginButton = find.text(EvoStrings.storyCtaLogin);
    expect(loginButton, findsOneWidget);

    await tester.tap(loginButton);
    await tester.pumpAndSettle();

    verify(() => EvoActionHandler().openAuthenticationScreen()).called(1);
  });
}
