import 'package:evoapp/data/repository/announcement_repo.dart';
import 'package:evoapp/data/request/reward_request.dart';
import 'package:evoapp/data/request/transaction_request.dart';
import 'package:evoapp/data/response/announcement_list_entity.dart';
import 'package:evoapp/data/response/transaction_list_entity.dart';
import 'package:evoapp/feature/announcement/announcement_screen.dart';
import 'package:evoapp/feature/announcement/reward/reward_announcement_screen.dart';
import 'package:evoapp/feature/announcement/transaction/transaction_announcement_screen.dart';
import 'package:evoapp/feature/announcement/utils/mock_file/mock_announcement_file_name.dart';
import 'package:evoapp/feature/announcement/utils/unread_announcement_checker.dart';
import 'package:evoapp/feature/announcement/widget/no_announcement_widget.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/evo_authentication_helper.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:evoapp/widget/evo_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../base/evo_page_state_base_test.dart';
import '../../base/evo_page_state_base_test_config.dart';
import '../../util/flutter_test_config.dart';
import 'reward/reward_announcement_cubit_test.dart';

class MockUnreadAnnouncementChecker extends Mock implements UnreadAnnouncementChecker {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  late CommonNavigatorObserver mockNavigatorObserver;
  late EvoSnackBar mockEvoSnackBar;

  setUpAll(() {
    registerFallbackValue(SnackBarType.neutral);
    registerFallbackValue(AnnouncementRequest(nextCursor: '', limit: null, status: ''));
    registerFallbackValue(TransactionRequest(limit: null, status: ''));
  });

  setUp(() {
    mockEvoSnackBar = MockEvoSnackBar();
    EvoAuthenticationHelper.setInstanceForTesting(MockEvoAuthenticationHelper());
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();
    mockNavigatorObserver = getIt.get<CommonNavigatorObserver>();
    when(() => mockNavigatorObserver.topStackIsAPageRoute()).thenAnswer((_) {
      return true;
    });

    getIt.registerSingleton<EvoSnackBar>(mockEvoSnackBar);
    getIt.registerSingleton<FeatureToggle>(MockFeatureToggle());
    getIt.registerSingleton<UnreadAnnouncementChecker>(MockUnreadAnnouncementChecker());
    when(() => getIt<EvoUtilFunction>().evoFormatCurrency(any(),
        currencySymbol: any(named: 'currencySymbol'))).thenAnswer((_) => '');
    when(() => getIt<UnreadAnnouncementChecker>().checkUnreadAnnouncement())
        .thenAnswer((_) async {});

    getIt.registerSingleton<AnnouncementRepo>(MockAnnouncementRepo());

    when(() => mockEvoSnackBar.show(
          any(),
          typeSnackBar: any(named: 'typeSnackBar'),
          durationInSec: any(named: 'durationInSec'),
          description: any(named: 'description'),
          marginBottomRatio: any(named: 'marginBottomRatio'),
        )).thenAnswer((_) async {
      return Future<bool?>.value();
    });

    setupMockImageProvider();

    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenAnswer((_) => 10);
  });

  tearDown(() {
    EvoAuthenticationHelper.resetToOriginalInstance();
    getIt.reset();
  });

  Future<void> mockAnnouncementRepoSuccessResponse(WidgetTester tester) async {
    await tester.runAsync(() async {
      final BaseResponse listEntityResponse =
          await getMockBaseResponse(getAnnouncementsMockFileName(''));
      when(() => getIt<AnnouncementRepo>()
              .getAnnouncements(any(), mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) async => AnnouncementListEntity.fromBaseResponse(listEntityResponse));

      final BaseResponse transactionListEntityResponse =
          await getMockBaseResponse(getAnnouncementTransactionsMockFileName());
      when(() => getIt<AnnouncementRepo>()
              .getTransactions(any(), mockConfig: any(named: 'mockConfig')))
          .thenAnswer(
              (_) async => TransactionListEntity.fromBaseResponse(transactionListEntityResponse));
    });
  }

  testWidgets('AnnouncementScreen test ui elements when user NOT logged in',
      (WidgetTester tester) async {
    getIt<AppState>().isUserLogIn = false;

    await tester.pumpWidget(MaterialApp(
      home: AnnouncementScreen(),
    ));

    final EvoAppBar appbar = tester.widget<EvoAppBar>(find.byType(EvoAppBar));
    expect((appbar.title as Text).data, equals(EvoStrings.announcementListTitle));

    expect(find.byType(NoAnnouncementWidget), findsOneWidget);

    //check didPop method
    final AnnouncementScreenState screenState = tester.state(find.byType(AnnouncementScreen));
    screenState.didPop();
    verify(() => getIt<UnreadAnnouncementChecker>().checkUnreadAnnouncement()).called(1);
    tester.pumpWidget(SizedBox());
  });

  testWidgets(
      'AnnouncementScreen test ui elements when user logged in and enableTransactionNotificationFeature = false',
      (WidgetTester tester) async {
    getIt<AppState>().isUserLogIn = true;
    when(() => getIt<FeatureToggle>().enableTransactionNotificationFeature)
        .thenAnswer((_) => false);
    await mockAnnouncementRepoSuccessResponse(tester);
    await tester.pumpWidget(MaterialApp(
      home: AnnouncementScreen(),
    ));
    await tester.pumpAndSettle();

    expect(find.byType(RewardAnnouncementScreen), findsOneWidget);

    //expect to not render tab bar
    expect(find.byType(TabBarView), findsNothing);

    final EvoAppBar appbar = tester.widget<EvoAppBar>(find.byType(EvoAppBar));
    expect((appbar.title as Text).data, equals(EvoStrings.announcementListTitle));
    // Clean up widgets
    await tester.pumpWidget(const SizedBox.shrink());
    await tester.pumpAndSettle();
  });

  testWidgets(
      'AnnouncementScreen test ui elements when user logged in and enableTransactionNotificationFeature = true',
      (WidgetTester tester) async {
    getIt<AppState>().isUserLogIn = true;
    when(() => getIt<FeatureToggle>().enableTransactionNotificationFeature).thenAnswer((_) => true);
    await mockAnnouncementRepoSuccessResponse(tester);
    await tester.pumpWidget(MaterialApp(
      home: AnnouncementScreen(),
    ));
    await tester.pumpAndSettle();

    //show tab bar view
    expect(find.byType(TabBarView), findsOneWidget);

    //default first tab
    expect(find.byType(RewardAnnouncementScreen), findsOneWidget);

    //change to second tab
    await tester.tap(find.byKey(Key(EvoStrings.transactionTitle)));
    await tester.pumpAndSettle();
    expect(find.byType(TransactionAnnouncementScreen), findsOneWidget);

    //back to first tab
    await tester.tap(find.byKey(Key(EvoStrings.bottomBarRewardLabel)));
    await tester.pumpAndSettle();
    expect(find.byType(RewardAnnouncementScreen), findsOneWidget);

    // Clean up widgets
    await tester.pumpWidget(const SizedBox.shrink());
    await tester.pumpAndSettle();
  });

  testWidgets('AnnouncementScreen test pushNamed method', (WidgetTester tester) async {
    AnnouncementScreen.pushNamed();

    verify(() => mockNavigatorContext.pushNamed(
          Screen.announcementListScreen.name,
          extra: captureAny(named: 'extra'),
        )).called(1);
  });
}
