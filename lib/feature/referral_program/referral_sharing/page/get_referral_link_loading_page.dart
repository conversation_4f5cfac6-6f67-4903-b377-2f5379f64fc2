import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../../resources/resources.dart';
import '../../../../util/ui_utils/evo_ui_utils.dart';
import '../../../../widget/animation/lottie_animation_widget.dart';
import '../../../../widget/evo_appbar.dart';

class GetReferralLinkLoadingPage extends StatelessWidget {
  final double _loadingHeightPercentage = 0.182;
  final double _loadingPaddingTopPercentage = 0.064;
  final double _loadingPaddingBottomPercentage = 0.079;

  const GetReferralLinkLoadingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: EvoAppBar(
          leading: CloseButton(
            color: evoColors.icon,
            onPressed: () {
              /// back to previous screen
              navigatorContext?.pop();
            },
          ),
        ),
        body: loadingWidget(context),
      ),
    );
  }

  Widget loadingWidget(BuildContext context) {
    return Center(
      child: Column(
        children: <Widget>[
          SizedBox(
            height: EvoUiUtils().calculateVerticalSpace(
              context: context,
              heightPercentage: _loadingPaddingTopPercentage,
            ),
          ),
          LottieAnimationWidget(
            EvoAnimation.animationHourglassLoading,
            size: EvoUiUtils().calculateVerticalSpace(
              context: context,
              heightPercentage: _loadingHeightPercentage,
            ),
          ),
          SizedBox(
            height: EvoUiUtils().calculateVerticalSpace(
              context: context,
              heightPercentage: _loadingPaddingBottomPercentage,
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              EvoStrings.referralLinkLoadingTitle,
              style: evoTextStyles.h300(),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              EvoStrings.referralLinkLoadingDescription,
              style: evoTextStyles.bodyMedium(evoColors.textPassive),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
