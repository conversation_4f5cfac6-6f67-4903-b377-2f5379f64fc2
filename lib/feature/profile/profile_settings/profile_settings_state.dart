part of 'profile_settings_cubit.dart';

abstract class ProfileSettingsState implements BlocState {}

class ProfileSettingsBioState extends ProfileSettingsState {
  final bool isEnableAuthByBiometrics;
  final bool isDeviceSupportBiometrics;
  final bool isBiometricTokenUnUsable;

  ProfileSettingsBioState({
    required this.isEnableAuthByBiometrics,
    required this.isDeviceSupportBiometrics,
    required this.isBiometricTokenUnUsable,
  });

  factory ProfileSettingsBioState.init() {
    return ProfileSettingsBioState(
      isEnableAuthByBiometrics: false,
      isDeviceSupportBiometrics: false,
      isBiometricTokenUnUsable: false,
    );
  }
}

class EnableAuthBioSuccess extends ProfileSettingsState {
  final bool isEnable;

  EnableAuthBioSuccess({required this.isEnable});
}

class VerifyByUsingBioError extends ProfileSettingsState {
  final BioAuthError? errCode;

  VerifyByUsingBioError(this.errCode);
}
