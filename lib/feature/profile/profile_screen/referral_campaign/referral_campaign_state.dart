part of 'referral_campaign_cubit.dart';

abstract class ReferralCampaignState extends BlocState {}

class ReferralCampaignInitial extends ReferralCampaignState {
  ReferralCampaignInitial();
}

class ReferralCampaignLoadedSuccess extends ReferralCampaignState {
  final CampaignListEntity? campaigns;

  ReferralCampaignLoadedSuccess({this.campaigns});
}

class ReferralCampaignLoadedFail extends ReferralCampaignState {
  final ErrorUIModel? error;

  ReferralCampaignLoadedFail({this.error});
}
