import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../prepare_for_app_initiation.dart';
import '../../resources/resources.dart';
import '../feature_toggle.dart';
import '../home_screen/home_widgets/home_app_bar/home_app_bar_configs.dart';
import 'v2/evo_alice_chatwoot_widget.dart';
import 'v3/evo_alice_v3_widget.dart';

class AliceUtils {
  static AliceUtils? _instance;

  static final AliceUtils _originalInstance = AliceUtils._internal();

  factory AliceUtils() {
    return _instance ??= _originalInstance;
  }

  AliceUtils._internal();

  // Method to replace the singleton instance (for testing only)
  @visibleForTesting
  static void setInstanceForTesting(AliceUtils instance) {
    _instance = instance;
  }

  // Method to reset the singleton instance (for testing only)
  @visibleForTesting
  static void resetToOriginalInstance() {
    _instance = _originalInstance;
  }

  Future<void> showAliceEvoChatBox() {
    final BuildContext? context = navigatorContext;
    final FeatureToggle featureToggle = getIt.get<FeatureToggle>();

    if (context == null || featureToggle.enableEvoAliceChat == AliceVersion.none) {
      return Future<void>.value();
    }

    final double screenHeight = context.screenHeight;
    final EdgeInsets screenPadding = context.screenPadding;
    final double paddingTop = screenPadding.top;

    final double maxHeight = screenHeight - paddingTop - HomeAppBarConfigs.toolbarHeight;

    final Widget aliceWidget = featureToggle.enableEvoAliceChat == AliceVersion.version_3
        ? const EvoAliceV3()
        : const EvoAliceChatwoot();

    return showModalBottomSheet<void>(
      context: context,
      backgroundColor: evoColors.background,
      isScrollControlled: true,
      enableDrag: false,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      constraints: BoxConstraints(
        maxHeight: maxHeight,
      ),
      builder: (_) {
        return SafeArea(
          child: Column(
            children: <Widget>[aliceWidget],
          ),
        );
      },
    );
  }

  /// Regex for any link start with domains:
  ///  - https://evocard.tpb.vn/
  ///  - https://www.goevo.vn/
  /// Refer: https://trustingsocial1.atlassian.net/browse/EMA-4184
  RegExp getSpecialLinkRegExp() {
    return RegExp(r'^https://(www\.)?(goevo\.vn|evocard\.tpb\.vn)(?:/.*)?(?:\?.*)?$');
  }
}
