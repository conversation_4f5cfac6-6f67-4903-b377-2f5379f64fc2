import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/widgets.dart';

import '../../../../base/evo_page_state_base.dart';
import '../../../../data/repository/authentication_repo.dart';
import '../../../../data/response/sign_in_entity.dart';
import '../../../../data/response/sign_in_otp_entity.dart';
import '../../../../model/evo_dialog_id.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../../util/functions.dart';
import '../../../../util/ui_utils/evo_dialog_helper.dart';
import '../../../../util/ui_utils/evo_ui_utils.dart';
import '../../../../widget/evo_appbar.dart';
import '../../../../widget/evo_appbar_leading_button.dart';
import '../../../../widget/evo_text_field_widget.dart';
import '../../../create_evo_card/create_evo_card_page.dart';
import '../../../deep_link/deep_link_handler.dart';
import '../../../deep_link/deep_link_utils.dart';
import '../../../deep_link/model/deep_link_model.dart';
import '../../../ekyc/ekyc_flow_callback.dart';
import '../../../ekyc/ekyc_flow_failed_reason.dart';
import '../../../ekyc/model/ekyc_for_flow_type.dart';
import '../../../ekyc/model/face_otp_payload.dart';
import '../../../ekyc_v2/ekyc_v2_flow_callback.dart';
import '../../../ekyc_v2/ekyc_v2_flow_type.dart';
import '../../../ekyc_v2/face_auth/face_auth_handler.dart';
import '../../../ekyc_v2/face_auth/face_auth_handler_impl.dart';
import '../../../ekyc_v2/face_auth/models/facial_auth_payload.dart';
import '../../../main_screen/main_screen.dart';
import '../../../pin/create_pin/create_pin_page.dart';
import '../../../pin/input_pin/input_pin_screen.dart';
import '../../../push_notification/notification_handler.dart';
import '../../../verify_otp/verify_otp_cubit.dart';
import '../../../verify_otp/verify_otp_page.dart';
import '../../utils/face_otp/face_otp_handler.dart';
import '../../utils/face_otp/face_otp_handler_impl.dart';
import 'input_phone_number_cubit.dart';
import 'widget/term_and_version_app_widget.dart';

enum InputPhoneEntryPoint { normal, fromDOE }

class InputPhoneNumberArg extends PageBaseArg {
  final String? lastPhoneNumberLogged;
  final bool? showPopupDOECompletedWithAnotherPhone;
  final InputPhoneEntryPoint? entryPoint;

  InputPhoneNumberArg({
    this.lastPhoneNumberLogged,
    this.showPopupDOECompletedWithAnotherPhone,
    this.entryPoint = InputPhoneEntryPoint.normal,
  });
}

class InputPhoneNumberPage extends PageBase {
  static void pushNamed({
    String? lastPhoneNumberLogged,
    bool? showPopupDOECompletedWithAnotherPhone,
    InputPhoneEntryPoint? entryPoint,
  }) {
    return navigatorContext?.pushNamed(
      Screen.inputPhoneNumberScreen.name,
      extra: InputPhoneNumberArg(
        lastPhoneNumberLogged: lastPhoneNumberLogged,
        showPopupDOECompletedWithAnotherPhone: showPopupDOECompletedWithAnotherPhone,
        entryPoint: entryPoint,
      ),
    );
  }

  static void goNamed({
    String? lastPhoneNumberLogged,
    bool? showPopupDOECompletedWithAnotherPhone,
    InputPhoneEntryPoint? entryPoint,
  }) {
    return navigatorContext?.goNamed(
      Screen.inputPhoneNumberScreen.name,
      extra: InputPhoneNumberArg(
        lastPhoneNumberLogged: lastPhoneNumberLogged,
        showPopupDOECompletedWithAnotherPhone: showPopupDOECompletedWithAnotherPhone,
        entryPoint: entryPoint,
      ),
    );
  }

  static void pushReplacementNamed({
    String? lastPhoneNumberLogged,
    bool? showPopupDOECompletedWithAnotherPhone,
    InputPhoneEntryPoint? entryPoint,
  }) {
    return navigatorContext?.pushReplacementNamed(
      Screen.inputPhoneNumberScreen.name,
      extra: InputPhoneNumberArg(
        lastPhoneNumberLogged: lastPhoneNumberLogged,
        showPopupDOECompletedWithAnotherPhone: showPopupDOECompletedWithAnotherPhone,
        entryPoint: entryPoint,
      ),
    );
  }

  final InputPhoneNumberArg? arg;

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.inputPhoneNumberScreen.routeName);

  const InputPhoneNumberPage({
    this.arg,
    super.key,
  });

  @override
  State<InputPhoneNumberPage> createState() => _InputPhoneNumberState();
}

class _InputPhoneNumberState extends EvoPageStateBase<InputPhoneNumberPage>
    with NotificationUserInteractionModule {
  String? _phoneNumber;
  late TextEditingController _phoneController;
  final FocusNode _phoneFocusNode = FocusNode();
  String? errorMessage;

  final InputPhoneNumberCubit _cubit = InputPhoneNumberCubit(getIt.get<AuthenticationRepo>());

  final FaceOtpHandler _faceOtpHandler = FaceOtpHandlerImpl(
    flowType: EkycFlowType.faceOtpSignIn,
  );
  final FaceAuthHandler _faceAuthHandler = FaceAuthHandlerImpl(
    flowType: EkycV2FlowType.signIn,
  );

  @override
  void initState() {
    super.initState();

    _phoneController = TextEditingController()
      ..addListener(() {
        _phoneNumber = _phoneController.text;
        _cubit.changePhoneNumber();
      });

    _phoneController.text = widget.arg?.lastPhoneNumberLogged ?? '';

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.arg?.showPopupDOECompletedWithAnotherPhone == true) {
        _showPopupDOECompletedWithAnotherPhone();
      } else {
        commonUtilFunction.delayAndRequestFocus(_phoneFocusNode);
      }
    });
  }

  Future<void> _showPopupDOECompletedWithAnotherPhone() async {
    EvoDialogHelper().showDialogConfirm(
      title: EvoStrings.doeCompletedWithAnotherPhone,
      content: EvoStrings.pleaseLoginToUseEvo,
      textPositive: EvoStrings.loginAgain,
      isDismissible: false,
      dialogId: EvoDialogId.doeCompletedWithAnotherPhoneDialog,
      onClickPositive: () {
        navigatorContext?.pop();
      },
    );
  }

  Future<bool> _handleBackButton() async {
    final AppState appState = getIt.get<AppState>();
    if (appState.actionAfterLogin != null) {
      await clearUnprocessedNotifications();
      appState.actionAfterLogin = null;
    }
    return evoUtilFunction.handleBackOrGoHomeScreen();
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<InputPhoneNumberCubit>(
      create: (_) => _cubit,
      child: BlocConsumer<InputPhoneNumberCubit, InputPhoneNumberState>(
        listener: (BuildContext previousState, InputPhoneNumberState currentState) async {
          _listenStateChanged(currentState);
        },
        builder: (BuildContext context, InputPhoneNumberState state) {
          return Scaffold(
            appBar: EvoAppBar(
              leading: EvoAppBarLeadingButton(
                onPressed: () => _handleBackButton(),
              ),
            ),
            backgroundColor: evoColors.background,
            body: SafeArea(
              child: PopScope(
                canPop: false,
                onPopInvokedWithResult: (bool didPop, _) {
                  if (didPop) {
                    return;
                  }

                  _handleBackButton();
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
                  child: CustomScrollView(
                    slivers: <Widget>[
                      SliverFillRemaining(
                        hasScrollBody: false,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Text(EvoStrings.loginTitle,
                                style: evoTextStyles.h600(evoColors.textActive)),
                            const SizedBox(height: 10),
                            Text(EvoStrings.loginDesc,
                                style: evoTextStyles.bodyLarge(evoColors.textPassive)),
                            const SizedBox(height: 20),
                            _phoneTextField(state),
                            const Spacer(),
                            const SizedBox(height: 50),
                            Align(
                              alignment: FractionalOffset.bottomCenter,
                              child: Column(
                                children: <Widget>[
                                  const TermAndVersionAppWidget(),
                                  CommonButton(
                                      onPressed:
                                          true == _phoneNumber?.isNotEmpty ? _onTapButton : null,
                                      isWrapContent: false,
                                      style: evoButtonStyles.primary(ButtonSize.xLarge),
                                      child: const Text(EvoStrings.continueBtn))
                                ],
                              ),
                            )
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _listenStateChanged(InputPhoneNumberState currentState) async {
    if (currentState is InputPhoneNumberLoading) {
      EvoUiUtils().showHudLoading();
      return;
    }

    EvoUiUtils().hideHudLoading();

    if (currentState is InputPhoneNumberPhoneSuccess) {
      final SignInEntity? entity = currentState.entity;
      switch (entity?.verdict) {
        case SignInEntity.verdictSuccess:
          commonLog('SignInEntity.verdictSuccess ${entity?.sessionToken}');
          VerifyOtpPage.pushNamed(
              phoneNumber: _phoneNumber,
              otpResendSecs: entity?.otpResendSecs,
              sessionToken: entity?.sessionToken,
              onPopSuccess: _handleOtpSuccess);
          break;
        case SignInEntity.verdictUserNotExisted:
          // TODO something verdictUserNotExisted
          break;
        case SignInEntity.verdictUnsupportedMobileNumber:
          // TODO something verdictUnsupportedMobileNumber
          break;
      }
    } else if (currentState is InputPhoneNumberFailed) {
      await _handleApiError(currentState);
    }
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }

  Widget _phoneTextField(InputPhoneNumberState state) {
    return EvoTextFieldWidget(
      focusNode: _phoneFocusNode,
      contextMenuBuilder: (BuildContext context, EditableTextState editableTextState) {
        return evoUtilFunction.createPasteClipboard(
          editableTextState: editableTextState,
          onPaste: handlePasteClipboard,
        );
      },
      controller: _phoneController,
      onChanged: (_) => _cubit.changePhoneNumber(),
      maxLength: maxLengthPhoneNumber,
      inputType: TextInputType.phone,
      errorText: errorMessage,
      hintText: EvoStrings.inputPhoneHint,
    );
  }

  Future<void> handlePasteClipboard(TextSelectionDelegate delegate) async {
    final ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
    data?.text?.let((String value) async {
      delegate.hideToolbar();
      final String tempPhone = evoUtilFunction.getPhoneFromPasteClipboard(value);
      if (CommonValidator().validatePhone(tempPhone)) {
        _phoneController.text = tempPhone;
        _phoneController.selection =
            TextSelection.fromPosition(TextPosition(offset: _phoneController.text.length));
      } else {
        _cubit.setInvalidPhoneError(EvoStrings.wrongPhoneFormat);
      }
    });
  }

  Future<void> _handleApiError(InputPhoneNumberFailed currentState) async {
    if (currentState.errorUIModel.verdict == SignInEntity.verdictLimitExceeded) {
      await EvoDialogHelper().showDialogConfirm(
        title: EvoStrings.limitOtp,
        content: currentState.errorUIModel.userMessage ?? EvoStrings.descLimitOtp,
        dialogId: EvoDialogId.inputPhoneNumberLimitOTPErrorDialog,
        isDismissible: false,
        textPositive: EvoStrings.moveToHome,
        onClickPositive: () {
          MainScreen.goNamed(isLoggedIn: false);
        },
      );

      return;
    }

    if (currentState.errorUIModel.verdict == SignInEntity.verdictDeletedUser) {
      _showDeactivatedAccount(currentState.errorUIModel);
      return;
    }

    if (currentState.errorUIModel.verdict == SignInEntity.verdictNoEKYCData) {
      CreateEvoCardPage.pushNamed();
      return;
    }

    if (currentState.errorUIModel.verdict == SignInEntity.verdictNotFinishedDOP) {
      final DeepLinkModel deepLinkModel = deepLinkUtils.generateDeepLinkModel(
        deepLinkValue: deepLinkUtils.getDOEDeepLinkNonRelaxRule(
          phoneNumber: _phoneNumber,
        ),
      );

      await getIt.get<DeepLinkHandler>().executeDeepLink(
            deepLink: deepLinkModel,
            isLoggedIn: appState.isUserLogIn,
          );
      return;
    }

    switch (currentState.errorUIModel.statusCode) {
      case CommonHttpClient.BAD_REQUEST:
        errorMessage = currentState.errorUIModel.userMessage;
        break;
      default:
        await handleEvoApiError(currentState.errorUIModel);
    }
  }

  void _onTapButton() {
    FocusManager.instance.primaryFocus?.unfocus();

    ///clear error text after call api
    errorMessage = null;
    _cubit.signIn(_phoneNumber!);
  }

  void _showDeactivatedAccount(ErrorUIModel? error) {
    EvoDialogHelper().showDialogConfirm(
      title: EvoStrings.loginDeactivatedAccountTitle,
      content: error?.userMessage ?? CommonStrings.otherGenericErrorMessage,
      dialogId: EvoDialogId.inputPhoneNumberDeactivatedAccountErrorDialog,
      textPositive: EvoStrings.close,
      positiveButtonStyle: evoButtonStyles.primary(ButtonSize.xLarge),
      titleTextStyle: evoTextStyles.h400(),
      contentTextStyle: evoTextStyles.bodyMedium(evoColors.textPassive),
      onClickPositive: () {
        navigatorContext?.pop();
      },
    );
  }

  void _handleOtpSuccess(VerifyOtpState state) {
    if (state is VerifyOtpCompleted && state.entity is SignInOtpEntity) {
      final SignInOtpEntity signInOtpEntity = state.entity as SignInOtpEntity;
      _handleChallengeType(signInOtpEntity: signInOtpEntity);
    }
  }

  EkycFlowCallback _createFaceOtpFlowCallback() {
    return EkycFlowCallback(
      flow: _faceOtpHandler.getFlowType,
      onSuccess: (_, EkycFlowPayload? payload) {
        navigatorContext?.pop();
        if (payload is FaceOtpPayload && payload.entity is SignInOtpEntity) {
          final SignInOtpEntity? signInOtpEntity = payload.entity as SignInOtpEntity?;
          _handleChallengeType(signInOtpEntity: signInOtpEntity);
        }
      },
      onFailed: (_, EkycFlowFailedReason reason, String? userMessage) {
        // Pop FaceOTP screen
        navigatorContext?.pop();
      },
    );
  }

  EkycV2FlowCallback _createFaceAuthFlowCallback() {
    return EkycV2FlowCallback(
      flowType: _faceAuthHandler.getFlowType,
      onSuccess: (EkycV2FlowPayload? payload) {
        navigatorContext?.pop();
        if (payload is FaceAuthPayload && payload.entity is SignInOtpEntity) {
          final SignInOtpEntity? signInOtpEntity = payload.entity as SignInOtpEntity?;
          _handleChallengeType(signInOtpEntity: signInOtpEntity);
        }
      },
      onError: (EkycV2FlowErrorReason errorReason) {
        // pop FaceAuth screen
        navigatorContext?.pop();
      },
    );
  }

  void _handleChallengeType({
    required SignInOtpEntity? signInOtpEntity,
  }) {
    signInOtpEntity?.challengeType?.let((String type) {
      if (type == SignInChallengeType.action.value) {
        CreateEvoCardPage.pushNamed(action: signInOtpEntity.action);
      } else if (type == SignInChallengeType.createPin.value) {
        CreatePinScreen.pushNamed(
            phoneNumber: _phoneController.text, sessionToken: signInOtpEntity.sessionToken);
      } else if (type == SignInChallengeType.verifyPin.value) {
        InputPinScreen.pushNamed(
            phoneNumber: _phoneController.text, sessionToken: signInOtpEntity.sessionToken);
      } else if (type == SignInChallengeType.faceOTP.value) {
        _faceOtpHandler.startFaceOtp(
          ekycSessionEntity: signInOtpEntity.ekycCredential,
          callback: _createFaceOtpFlowCallback(),
          isReplaceScreen: false,
        );
      } else if (type == SignInChallengeType.faceAuth.value) {
        _faceAuthHandler.startFaceAuth(
          sessionToken: signInOtpEntity.sessionToken,
          callback: _createFaceAuthFlowCallback(),
          isReplaceScreen: false,
        );
      }
    });
  }
}
