import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/pin_code/common_pin_theme.dart';
import 'package:flutter_common_package/widget/pin_code/pin_code_widget.dart';

import '../../../prepare_for_app_initiation.dart';
import '../../../resources/evo_pin_code_theme.dart';
import '../../../resources/resources.dart';
import '../../../widget/default_obscure_widget.dart';
import '../../../widget/default_obscure_widget_v2.dart';
import '../../../widget/evo_pin_code/evo_pin_code_config.dart';
import '../../feature_toggle.dart';
import 'confirm_pin_popup_cubit.dart';

abstract class ConfirmPinPopupCallback {
  void onInputPin(String? pin);
}

class ConfirmPinPopup extends StatefulWidget {
  final ConfirmPinPopupCallback? callback;

  const ConfirmPinPopup({super.key, this.callback});

  @override
  State<ConfirmPinPopup> createState() => ConfirmPinWidgetState();

  static Future<void> show({ConfirmPinPopupCallback? callback}) {
    final BuildContext? navigatorCtx = navigatorContext;
    if (navigatorCtx == null) {
      return Future<void>.value();
    }
    return showModalBottomSheet<void>(
        context: navigatorCtx,
        enableDrag: false,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (_) {
          return ConfirmPinPopup(callback: callback);
        });
  }
}

@visibleForTesting
class ConfirmPinWidgetState extends State<ConfirmPinPopup> {
  static const double padding = 0;

  @visibleForTesting
  final ConfirmPinPopupCubit enterPinPopupCubit = ConfirmPinPopupCubit();

  TextEditingController pinTextController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ConfirmPinPopupCubit>(
        create: (_) => enterPinPopupCubit,
        child: LayoutBuilder(
          builder: (BuildContext context, BoxConstraints constraints) {
            return Padding(
              padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom +
                      context.screenPadding.bottom +
                      padding),
              child: Stack(
                fit: StackFit.expand,
                children: <Widget>[
                  Column(
                    children: <Widget>[
                      Expanded(
                        child: GestureDetector(
                            onTap: () {
                              navigatorContext?.pop();
                            },
                            child: Container(
                              color: Colors.transparent,
                            )),
                      ),
                      getContent(),
                    ],
                  ),
                ],
              ),
            );
          },
        ));
  }

  @visibleForTesting
  Widget getContent() {
    return BlocBuilder<ConfirmPinPopupCubit, ConfirmPinPopupState>(
        builder: (BuildContext context, ConfirmPinPopupState state) {
      return Container(
        width: context.screenWidth,
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          borderRadius:
              BorderRadius.only(topLeft: Radius.circular(24), topRight: Radius.circular(24)),
          color: Colors.white,
        ),
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Text(
                EvoStrings.enterPinTitle,
                style: evoTextStyles.h600(evoColors.foreground).copyWith(height: 1.2),
              ),
              const SizedBox(height: 10),
              Text(
                EvoStrings.enterPinToAuthenticate,
                style: evoTextStyles.h400().copyWith(
                    color: evoColors.textPassive,
                    height: 1.5,
                    fontSize: 16,
                    fontWeight: FontWeight.w400),
              ),
              const SizedBox(height: 16),
              _buildPinCodeWidget(),
              Container(
                margin: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: <Widget>[
                    InkWell(
                      onTap: () {
                        enterPinPopupCubit.toggleObscurePinText();
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4.0),
                        child: evoImageProvider.asset(enterPinPopupCubit.obscurePinTextValue
                            ? EvoImages.icShowOffPin
                            : EvoImages.icShowOnPin),
                      ),
                    ),
                  ],
                ),
              )
            ]),
      );
    });
  }

  CommonPinCode _buildPinCodeWidget() {
    return getIt<FeatureToggle>().enableRevampUiFeature
        ? CommonPinCode(
            textController: pinTextController,
            onSubmit: onPinSubmit,
            animationDuration: Duration.zero,
            obscuringWidget:
                enterPinPopupCubit.obscurePinTextValue ? const DefaultObscureWidgetV2() : null,
            autoDismissKeyboard: false,
            pinTheme: EvoPinCodeTheme.buildDefaultPinCodeTheme(),
            showCursor: true,
            enableActiveFill: true,
            cursorHeight: 16,
            cursorWidth: 1,
            cursorColor: evoColorsV2.borderPrimary,
            textStyle: evoTextStylesV2.label(
              LabelTextType.xLarge,
              color: evoColorsV2.textTitle,
            ),
          )
        : CommonPinCode(
            textController: pinTextController,
            onSubmit: onPinSubmit,
            animationDuration: Duration.zero,
            obscuringWidget:
                enterPinPopupCubit.obscurePinTextValue ? const DefaultObscureWidget() : null,
            autoDismissKeyboard: false,
            pinTheme: CommonPinTheme(
              fieldHeight: EvoPinCodeConfig.defaultPinCodeFieldHeight,
            ),
          );
  }

  @visibleForTesting
  void onPinSubmit(String pin) {
    navigatorContext?.pop();
    widget.callback?.onInputPin(pin);
  }
}
