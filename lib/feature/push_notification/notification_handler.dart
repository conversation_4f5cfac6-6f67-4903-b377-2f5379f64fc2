import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/feature/onesignal/onesignal.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../data/repository/announcement_repo.dart';
import '../../data/response/action_entity.dart';
import '../../data/response/notification_entity.dart';
import '../../model/evo_action_model.dart';
import '../../prepare_for_app_initiation.dart';
import '../../resources/resources.dart';
import '../../util/evo_action_handler.dart';
import '../../util/mapper.dart';
import '../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../widget/evo_next_action/evo_next_action_widget.dart';
import '../announcement/model/announcement_info.dart';
import '../deep_link/deep_link_handler.dart';
import '../main_screen/main_screen.dart';
import '../webview/models/evo_webview_arg.dart';

mixin NotificationUserInteractionModule {
  Future<void> askingForNotificationPermission() async {
    // [EMA-2949] Turn off Open settings pop-up in EVO App
    // only show request notification permission for the first time, if user deny, not show it again
    final EvoLocalStorageHelper evoLocalStorageHelper = getIt.get<EvoLocalStorageHelper>();
    final bool hasShowedRequest = await evoLocalStorageHelper.hasShowRequestNotifyPermission();
    if (hasShowedRequest) {
      return;
    }

    // [EMA-4414] [NFC | Mobile] Streamline EVO App Launch for NFC Scanning
    // check if user is process the DOP Web with NFC step -> bypass request notification pop-up
    if (getIt<DeepLinkHandler>().isHandlingDOPWebNfcDeepLink()) {
      return;
    }

    commonLog('promptUserForPushNotificationPermission');
    return OneSignal.shared
        .promptUserForPushNotificationPermission(fallbackToSettings: true)
        .then((bool value) {
      commonLog('promptUserForPushNotificationPermission $value');
      evoLocalStorageHelper.setShowedRequestNotifyPermission(true);
    });
  }

  Future<void> setUpNotificationListeners() async {
    await listenNotificationArrivedIfAppInForeground();
    await listenNotificationOpened();
  }

  @visibleForTesting
  Future<void> listenNotificationArrivedIfAppInForeground() async {
    await OneSignal.shared
        .setNotificationWillShowInForegroundHandler((OSNotificationReceivedEvent event) {
      if (kDebugMode) {
        print('FOREGROUND HANDLER CALLED WITH: $event');
      }

      event.notification.additionalData?.let((Map<String, dynamic> it) async {
        final NotificationEntity notification = NotificationEntity.fromJson(it);

        _setupNotificationUI(event, notification);
        _updateAnnouncementStatusIndicator(notification);
      });
    });
  }

  void _setupNotificationUI(OSNotificationReceivedEvent event, NotificationEntity notification) {
    if (notification.isDisplay ?? true) {
      /// Display Notification
      event.complete(event.notification);
    } else {
      /// send null to not display
      event.complete(null);
    }
  }

  void _updateAnnouncementStatusIndicator(NotificationEntity notification) {
    if (notification.isDisplay ?? true) {
      final AppState appState = getIt.get<AppState>();
      appState.announcementInfo.updateStatus(AnnouncementStatus.hasUnreadItems);
    }
  }

  @visibleForTesting
  Future<void> listenNotificationOpened() async {
    await OneSignal.shared.setNotificationOpenedHandler((OSNotificationOpenedResult result) {
      result.notification.additionalData?.let((Map<String, dynamic> it) async {
        if (await _checkDecreeConsentStatus()) {
          MainScreen.removeUntilAndPushReplacementNamed(isLoggedIn: true);
        } else {
          final NotificationEntity notificationEntity = NotificationEntity.fromJson(it);
          _updateStatusNotificationTapped(notificationEntity);
          _onNotificationTapped(notificationEntity);
        }
      });
    });
  }

  Future<void> clearUnprocessedNotifications() async {
    return OneSignal.shared.setNotificationOpenedHandler((OSNotificationOpenedResult result) {
      /// Because Onesignal only clear the unprocessed notifications, if it's already handled
      /// so we need to set the handler to non-null and, do nothing in the handler
      /// Reference:
      /// https://github.com/OneSignal/OneSignal-Android-SDK/blob/2cbb251031a26ef61984b9cb7d728ef9c5277e92/OneSignalSDK/onesignal/src/main/java/com/onesignal/OneSignal.java#L803C1-L808C5
      commonLog('Run clearUnprocessedNotifications for clear the unprocessed notifications');
    });
  }

  Future<void> _onNotificationTapped(NotificationEntity notification) async {
    notification.action?.let((ActionEntity action) async {
      final EvoActionModel evoActionModel = action.toEvoActionModel();

      final EvoWebViewArg arg = EvoWebViewArg(
        title: EvoStrings.announcementDetailHeaderTitle,
        url: evoActionModel.args?.link,
        nextActionWidget: EvoNextActionWidget(
          nextAction: action.args?.nextAction?.toEvoActionModel(),
        ),
      );

      await EvoActionHandler().handle(evoActionModel, arg: arg, idAnnouncement: notification.id);
    });
  }

  void _updateStatusNotificationTapped(NotificationEntity notificationEntity) {
    final AnnouncementRepo announcementRepo = getIt.get<AnnouncementRepo>();
    announcementRepo.updateAnnouncementStatus(notificationEntity.id, NotificationEntity.statusRead,
        mockConfig: const MockConfig(enable: false, fileName: 'announcement_update_status.json'));
  }

  Future<bool> _checkDecreeConsentStatus() async {
    final EvoLocalStorageHelper secureStorageHelper = getIt.get<EvoLocalStorageHelper>();
    return await secureStorageHelper.getDecreeConsentStatus();
  }
}
