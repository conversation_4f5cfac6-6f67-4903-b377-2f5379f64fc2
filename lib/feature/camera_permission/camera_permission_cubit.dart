import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/util/permission/device_permission.dart';
import 'package:flutter_common_package/util/permission/permission_handler_mixin.dart';

part 'camera_permission_state.dart';

class CameraPermissionCubit extends CommonCubit<CameraPermissionState>
    with PermissionHandlerMixin
    implements PermissionHandlerCallback {
  CameraPermissionCubit() : super(CameraPermissionInitialState());

  void requestPermission() {
    requestPermissionWhenPageIsInit(
      devicePermission: TsDevicePermission.camera,
      callback: this,
    );
  }

  void checkPermissionOnResume() {
    checkPermissionStatusWhenAppOnResume(
      devicePermission: TsDevicePermission.camera,
      callback: this,
    );
  }

  @override
  void onDeniedPermission(TsDevicePermission permission) {
    emitStateNoDuplicateCurrentState(CameraPermissionDeniedState());
  }

  @override
  void onGrantedPermission(TsDevicePermission permission) {
    emitStateNoDuplicateCurrentState(CameraPermissionGrantedState());
  }

  void emitStateNoDuplicateCurrentState(CameraPermissionState newState) {
    if (state.runtimeType != newState.runtimeType) {
      emit(newState);
    }
  }

  void resetState() {
    emit(CameraPermissionInitialState());
  }
}
