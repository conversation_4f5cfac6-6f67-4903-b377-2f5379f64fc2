import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:grouped_list/grouped_list.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../data/repository/checkout_repo.dart';
import '../../../data/response/current_cashback_info_entity.dart';
import '../../../data/response/payment_result_transaction_entity.dart';
import '../../../model/type_load_list.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../widget/evo_divider_widget.dart';
import '../../../widget/evo_loading_widget.dart';
import '../../feature_toggle.dart';
import '../../payment/emi_option_screen/tenor_detail_info_bottom_sheet.dart';
import '../../payment/utils/payment_navigate_helper_mixin.dart';
import '../dialog/cashback_transaction_dialog.dart';
import '../transaction_history_empty_widget.dart';
import 'transaction_history_cubit.dart';
import 'transaction_history_item_widget.dart';
import 'transaction_history_revamp_ui_item_widget.dart';
import 'transaction_history_state.dart';
import 'widget/cashback_statistic_widget.dart';

class TransactionHistoryListWidget extends PageBase {
  const TransactionHistoryListWidget({
    super.key,
  });

  @override
  EvoPageStateBase<TransactionHistoryListWidget> createState() =>
      TransactionAnnouncementScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.transactionHistoryListForAuthorizedUserWidget.routeName);
}

@visibleForTesting
class TransactionAnnouncementScreenState extends EvoPageStateBase<TransactionHistoryListWidget>
    with PaymentNavigationHelperMixin {
  final RefreshController _refreshController = RefreshController();
  @visibleForTesting
  final TransactionHistoryCubit cubit = TransactionHistoryCubit(getIt.get<CheckOutRepo>());
  final FeatureToggle _featureToggle = getIt.get<FeatureToggle>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTransaction(isShowLoading: true);
    });
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return buildVisibilityDetectorPage(context);
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<TransactionHistoryCubit>(
      create: (_) => cubit,
      child: BlocConsumer<TransactionHistoryCubit, TransactionHistoryState>(
        listener: (BuildContext context, TransactionHistoryState state) {
          _onStateChanged(state);
        },
        buildWhen: (TransactionHistoryState previous, TransactionHistoryState current) {
          bool canBuildWhenError = false;
          if (current is TransactionHistoryErrorState) {
            canBuildWhenError = current.isRefresh;
          }

          return current is TransactionHistoryLoadingState ||
              current is TransactionHistoryLoadedState ||
              canBuildWhenError;
        },
        builder: (BuildContext context, TransactionHistoryState state) {
          if (state is TransactionHistoryLoadingState) {
            return const EvoLoadingWidget();
          }

          CurrentCashbackInfoEntity? cashback;
          List<PaymentResultTransactionEntity>? transactions;
          bool isLoadMore = false;

          if (state is TransactionHistoryLoadedState) {
            cashback = state.cashback;
            transactions = state.transactions;
            isLoadMore = state.isLoadMore;
          }

          return Padding(
            padding: EdgeInsets.only(bottom: context.screenPadding.bottom),
            child: RefreshableView(
              onRefresh: () {
                _loadTransaction();
              },
              controller: _refreshController,
              enablePullUp: isLoadMore,
              onLoading: () {
                _loadTransaction(loadingType: LoadListType.loadMore);
              },
              child: _buildTransactionListWidget(
                cashback: cashback,
                transactions: transactions,
              ),
            ),
          );
        },
      ),
    );
  }

  void _loadTransaction({
    bool isShowLoading = false,
    LoadListType loadingType = LoadListType.refresh,
  }) {
    cubit.loadTransactions(
      type: loadingType,
      isShowLoading: isShowLoading,
    );
  }

  Widget _buildTransactionListWidget({
    CurrentCashbackInfoEntity? cashback,
    List<PaymentResultTransactionEntity>? transactions,
  }) {
    return transactions == null || transactions.isEmpty == true
        ? const TransactionHistoryEmptyWidget()
        : _featureToggle.enableInstantCashbackFeature
            ? _buildTransactionListCashbackWidget(
                cashback: cashback,
                transactions: transactions,
              )
            : _buildTransactionListWithoutCashbackWidget(transactions);
  }

  Widget _buildTransactionListWithoutCashbackWidget(
      List<PaymentResultTransactionEntity> transactions) {
    const double storeImageSize = 72;
    const double imageSizeRightMargin = 10;
    const double itemPadding = 20;

    return ListView.separated(
      padding: const EdgeInsets.symmetric(vertical: 20),
      separatorBuilder: (BuildContext context, int index) => EvoDividerWidget(
        height: 0.5,
        color: evoColors.textHint,
        margin: const EdgeInsets.only(
          left: storeImageSize + imageSizeRightMargin + itemPadding,
          right: 20,
        ),
      ),
      itemBuilder: (BuildContext context, int index) {
        final PaymentResultTransactionEntity transactionEntity = transactions[index];
        return TransactionHistoryItemWidget(
          transaction: transactionEntity,
          imageSizeRightMargin: imageSizeRightMargin,
          itemPadding: itemPadding,
          imageSize: storeImageSize,
          onTap: () => _onTransactionItemClick(transactionEntity),
        );
      },
      itemCount: transactions.length,
    );
  }

  Widget _buildTransactionListCashbackWidget({
    required CurrentCashbackInfoEntity? cashback,
    required List<PaymentResultTransactionEntity> transactions,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: SingleChildScrollView(
        child: Column(
          children: <Widget>[
            CashbackStatisticWidget(
              cashback: cashback,
              onPressed: () {
                CashbackTransactionDialog.show(onError: handleEvoApiError);
              },
            ),
            GroupedListView<PaymentResultTransactionEntity, String>(
              shrinkWrap: true,
              sort: false,
              elements: transactions,
              groupBy: (PaymentResultTransactionEntity element) {
                return _isTransactionCashback(element)
                    ? element.updatedAtHeader ?? ''
                    : (element.createdAtHeader ?? '');
              },
              groupSeparatorBuilder: (String value) => Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  const SizedBox(height: 32),
                  Text(
                    '${EvoStrings.groupHeaderMonthTitle} $value',
                    style: evoTextStyles.bodyLarge(evoColors.textActive),
                  ),
                  const SizedBox(height: 16),
                  EvoDividerWidget(
                    height: 0.5,
                    color: evoColors.textHint,
                  ),
                ],
              ),
              groupItemBuilder:
                  (_, PaymentResultTransactionEntity element, bool groupStart, bool groupEnd) {
                return TransactionHistoryRevampUIItemWidget(
                  transaction: element,
                  onTap: () => _isTransactionCashback(element)
                      ? _openCashbackRuleBottomDialog()
                      : _onTransactionItemClick(element),
                );
              },
              separator: EvoDividerWidget(
                height: 1,
                color: evoColors.emiManagementBottomBorder,
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool _isTransactionCashback(PaymentResultTransactionEntity transaction) {
    return transaction.type == TransactionType.cashback;
  }

  Future<void> _onTransactionItemClick(PaymentResultTransactionEntity? transaction) async {
    handleNavigateToTransactionDetailScreen(transaction?.id);
  }

  void _openCashbackRuleBottomDialog() {
    final List<String> contentInfo = <String>[
      EvoStrings.refundInfoDescription,
      EvoStrings.refundInfoTimeDescription,
    ];

    TenorInfoBottomSheet.show(items: contentInfo, title: EvoStrings.refundInfoTitle);
  }

  void _onStateChanged(TransactionHistoryState state) {
    if (_refreshController.isRefresh) {
      _refreshController.refreshCompleted(resetFooterState: true);
    }
    if (_refreshController.isLoading) {
      _refreshController.loadComplete();
    }

    if (state is TransactionHistoryErrorState) {
      handleEvoApiError(state.errorUIModel);
      return;
    }
  }
}
