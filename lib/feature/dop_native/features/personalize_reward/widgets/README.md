# Expandable Reward List Widgets

This folder contains a set of widgets for creating an expandable list with smooth animations, based on the design requirements. The list supports single-item expansion and radio button selection.

## Components

### 1. `ExpandableRewardListItemModel`
Data model representing each list item with:
- `id`: Unique identifier
- `title`: Display title
- `description`: Expandable description text
- `logoIcons`: List of logo asset paths
- `isExpanded`: Current expansion state
- `isSelected`: Current selection state

### 2. `ExpandableRewardListItemWidget`
Individual list item widget featuring:
- Smooth expand/collapse animation
- Radio button selection
- Logo icons display (max 6 visible + more indicator)
- Tap to expand functionality
- Customizable animation duration

### 3. `ExpandableRewardListWidget`
Main list widget that manages:
- Multiple expandable items
- Single-item expansion logic
- Selection state management
- Scroll physics configuration
- Callback handling

### 4. `ExpandableRewardListIntegrationExample`
Integration example with existing data models:
- Converts DOPNativeMetadataItemEntity to ExpandableRewardListItemModel
- Handles real API data structure
- Provides utility methods for validation and filtering

### 5. `SampleExpandableRewardListUsage`
Complete example implementation showing:
- Sample data structure
- State management
- Event handling
- UI integration

## Usage

### Basic Implementation

```dart
import 'package:flutter/material.dart';
import 'widgets/widgets.dart';

class MyRewardScreen extends StatefulWidget {
  @override
  State<MyRewardScreen> createState() => _MyRewardScreenState();
}

class _MyRewardScreenState extends State<MyRewardScreen> {
  String? selectedItemId;
  String? expandedItemId;

  final List<ExpandableRewardListItemModel> items = [
    ExpandableRewardListItemModel(
      id: 'shopping',
      title: 'Mua sắm trực tuyến',
      description: 'Nhận ưu đãi khi mua sắm...',
      logoIcons: ['assets/logo1.png', 'assets/logo2.png'],
    ),
    // Add more items...
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ExpandableRewardListWidget(
        items: items,
        selectedItemId: selectedItemId,
        expandedItemId: expandedItemId,
        onItemSelected: (itemId) {
          setState(() {
            selectedItemId = itemId;
          });
        },
        onItemExpanded: (itemId, isExpanded) {
          setState(() {
            expandedItemId = isExpanded ? itemId : null;
          });
        },
      ),
    );
  }
}
```

### Advanced Usage with DOPNativeMetadataItemEntity

```dart
// Use the integration example widget
ExpandableRewardListIntegrationExample(
  entities: metadataEntities, // List<DOPNativeMetadataItemEntity>
  onItemSelected: (entity) {
    // Handle selection
    print('Selected: ${entity.code}');
  },
)

// Or convert manually
List<ExpandableRewardListItemModel> convertFromMetadata(
  List<DOPNativeMetadataItemEntity> entities,
) {
  return entities.map((entity) {
    return ExpandableRewardListItemModel(
      id: entity.code ?? '',
      title: entity.getAttributeByName('title') ?? '',
      description: entity.getAttributeByName('description') ?? '',
      logoIcons: _parseLogoIcons(entity.getAttributeByName('logos')),
    );
  }).toList();
}
```

## Features

### Animation
- Smooth expand/collapse with `SizeTransition`
- Configurable animation duration (default: 300ms)
- Curved animation with `Curves.easeInOut`
- Rotating arrow indicator

### Selection
- Single selection with radio buttons
- Visual feedback for selected state
- Callback for selection changes

### Expansion
- Only one item can be expanded at a time
- Automatic collapse of previously expanded item
- Tap anywhere on header to expand/collapse

### Logo Display
- Shows up to 6 logos in a row
- "+N" indicator for additional logos
- Error handling for missing images
- Consistent sizing and spacing

### Styling
- Follows DOP Native design system
- Consistent with existing app patterns
- Responsive layout
- Shadow and border radius

## Customization

### Animation Duration
```dart
ExpandableRewardListWidget(
  animationDuration: Duration(milliseconds: 500),
  // ...
)
```

### Scroll Physics
```dart
ExpandableRewardListWidget(
  physics: BouncingScrollPhysics(),
  // ...
)
```

### Shrink Wrap
```dart
ExpandableRewardListWidget(
  shrinkWrap: true,
  // ...
)
```

## Integration with Existing Code

The widgets are designed to integrate seamlessly with the existing DOP Native architecture:

1. **State Management**: Compatible with BLoC pattern
2. **Styling**: Uses `dopNativeTextStyles` and `dopNativeColors`
3. **Components**: Reuses `DOPNativeRadioButtonWidget`
4. **Data Models**: Can convert from `DOPNativeMetadataItemEntity`

## Testing

Refer to the existing test patterns in the project:
- Unit tests for models
- Widget tests for individual components
- Integration tests for complete flows

## Performance Considerations

- Efficient list rendering with `ListView.builder`
- Minimal rebuilds with proper state management
- Optimized animations with `SingleTickerProviderStateMixin`
- Image caching for logo assets
