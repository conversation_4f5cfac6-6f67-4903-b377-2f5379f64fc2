import 'package:flutter/material.dart';

import '../../../../../data/response/dop_native/dop_native_metadata_item_attribute_entity.dart';
import '../../../../../data/response/dop_native/dop_native_metadata_item_entity.dart';
import '../../../resources/dop_native_resources.dart';
import 'expandable_reward_list_item_model.dart';
import 'expandable_reward_list_widget.dart';

/// Integration example showing how to use ExpandableRewardListWidget 
/// with existing DOPNativeMetadataItemEntity data
class ExpandableRewardListIntegrationExample extends StatefulWidget {
  /// List of metadata entities from API
  final List<DOPNativeMetadataItemEntity> entities;
  
  /// Callback when an item is selected
  final void Function(DOPNativeMetadataItemEntity entity)? onItemSelected;

  const ExpandableRewardListIntegrationExample({
    required this.entities,
    this.onItemSelected,
    super.key,
  });

  @override
  State<ExpandableRewardListIntegrationExample> createState() => 
      _ExpandableRewardListIntegrationExampleState();
}

class _ExpandableRewardListIntegrationExampleState 
    extends State<ExpandableRewardListIntegrationExample> {
  String? selectedItemId;
  String? expandedItemId;
  
  late List<ExpandableRewardListItemModel> items;

  @override
  void initState() {
    super.initState();
    items = _convertFromMetadataEntities(widget.entities);
  }

  @override
  void didUpdateWidget(ExpandableRewardListIntegrationExample oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.entities != widget.entities) {
      items = _convertFromMetadataEntities(widget.entities);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: dopNativeColors.screenBackground,
      ),
      child: ExpandableRewardListWidget(
        items: items,
        selectedItemId: selectedItemId,
        expandedItemId: expandedItemId,
        onItemSelected: _handleItemSelected,
        onItemExpanded: _handleItemExpanded,
        physics: const BouncingScrollPhysics(),
        shrinkWrap: true,
      ),
    );
  }

  /// Convert DOPNativeMetadataItemEntity list to ExpandableRewardListItemModel list
  List<ExpandableRewardListItemModel> _convertFromMetadataEntities(
    List<DOPNativeMetadataItemEntity> entities,
  ) {
    return entities.map((entity) {
      return ExpandableRewardListItemModel(
        id: entity.code ?? '',
        title: entity.getAttributeByName(DOPNativeMetadataItemAttributeEntity.title) ?? 
               entity.name ?? 
               'Unknown Category',
        description: entity.getAttributeByName('description') ?? 
                    'No description available for this category.',
        logoIcons: _parseLogoIcons(entity.getAttributeByName('logos')),
      );
    }).toList();
  }

  /// Parse logo icons from metadata attribute
  /// Expected format: comma-separated asset paths or JSON array
  List<String> _parseLogoIcons(String? logosAttribute) {
    if (logosAttribute == null || logosAttribute.isEmpty) {
      return _getDefaultLogos();
    }

    try {
      // Try to split by comma first (simple format)
      if (logosAttribute.contains(',')) {
        return logosAttribute
            .split(',')
            .map((logo) => logo.trim())
            .where((logo) => logo.isNotEmpty)
            .toList();
      }
      
      // If no comma, treat as single logo
      return [logosAttribute.trim()];
    } catch (e) {
      // Fallback to default logos if parsing fails
      return _getDefaultLogos();
    }
  }

  /// Get default logos when no logos are provided
  List<String> _getDefaultLogos() {
    return [
      'assets/images/logos/default_1.png',
      'assets/images/logos/default_2.png',
      'assets/images/logos/default_3.png',
    ];
  }

  void _handleItemSelected(String itemId) {
    setState(() {
      selectedItemId = itemId;
    });
    
    // Find the corresponding entity and call the callback
    final entity = widget.entities.firstWhere(
      (e) => e.code == itemId,
      orElse: () => const DOPNativeMetadataItemEntity(),
    );
    
    widget.onItemSelected?.call(entity);
  }

  void _handleItemExpanded(String itemId, bool isExpanded) {
    setState(() {
      expandedItemId = isExpanded ? itemId : null;
    });
  }
}

/// Utility class for working with expandable reward lists
class ExpandableRewardListUtils {
  /// Create sample data for testing
  static List<DOPNativeMetadataItemEntity> createSampleEntities() {
    return [
      DOPNativeMetadataItemEntity(
        code: 'shopping_online',
        name: 'Online Shopping',
        attributes: [
          const DOPNativeMetadataItemAttributeEntity(
            name: DOPNativeMetadataItemAttributeEntity.title,
            value: 'Mua sắm trực tuyến',
          ),
          const DOPNativeMetadataItemAttributeEntity(
            name: 'description',
            value: 'Nhận ưu đãi khi mua sắm trực tuyến tại các trang thương mại điện tử hàng đầu.',
          ),
          const DOPNativeMetadataItemAttributeEntity(
            name: 'logos',
            value: 'assets/images/logos/shopee.png,assets/images/logos/lazada.png,assets/images/logos/tiki.png',
          ),
        ],
      ),
      DOPNativeMetadataItemEntity(
        code: 'dining_entertainment',
        name: 'Dining & Entertainment',
        attributes: [
          const DOPNativeMetadataItemAttributeEntity(
            name: DOPNativeMetadataItemAttributeEntity.title,
            value: 'Ẩm thực & Giải trí',
          ),
          const DOPNativeMetadataItemAttributeEntity(
            name: 'description',
            value: 'Thưởng thức các món ăn ngon với ưu đãi đặc biệt tại hàng trăm nhà hàng.',
          ),
          const DOPNativeMetadataItemAttributeEntity(
            name: 'logos',
            value: 'assets/images/logos/grab.png,assets/images/logos/gojek.png,assets/images/logos/baemin.png',
          ),
        ],
      ),
    ];
  }

  /// Validate that an entity has required attributes for display
  static bool isValidEntity(DOPNativeMetadataItemEntity entity) {
    return entity.code != null && 
           entity.code!.isNotEmpty &&
           (entity.getAttributeByName(DOPNativeMetadataItemAttributeEntity.title) != null ||
            entity.name != null);
  }

  /// Filter entities to only include valid ones
  static List<DOPNativeMetadataItemEntity> filterValidEntities(
    List<DOPNativeMetadataItemEntity> entities,
  ) {
    return entities.where(isValidEntity).toList();
  }
}
