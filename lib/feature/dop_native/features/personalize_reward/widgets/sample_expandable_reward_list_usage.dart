import 'package:flutter/material.dart';

import '../../../resources/dop_native_resources.dart';
import 'expandable_reward_list_item_model.dart';
import 'expandable_reward_list_widget.dart';

/// Sample usage widget demonstrating how to use the ExpandableRewardListWidget
/// This widget shows the implementation pattern based on the provided image
class SampleExpandableRewardListUsage extends StatefulWidget {
  const SampleExpandableRewardListUsage({super.key});

  @override
  State<SampleExpandableRewardListUsage> createState() => _SampleExpandableRewardListUsageState();
}

class _SampleExpandableRewardListUsageState extends State<SampleExpandableRewardListUsage> {
  String? selectedItemId;
  String? expandedItemId;

  // Sample data based on the image provided
  late final List<ExpandableRewardListItemModel> items;

  @override
  void initState() {
    super.initState();
    items = _createSampleItems();
  }

  List<ExpandableRewardListItemModel> _createSampleItems() {
    return [
      const ExpandableRewardListItemModel(
        id: 'shopping_online',
        title: 'Mua sắm trực tuyến',
        description: 'Nhận ưu đãi khi mua sắm trực tuyến tại các trang thương mại điện tử hàng đầu. Tích lũy điểm thưởng và nhận cashback cho mỗi giao dịch.',
        logoIcons: [
          'assets/images/logos/shopee.png',
          'assets/images/logos/lazada.png',
          'assets/images/logos/tiki.png',
          'assets/images/logos/sendo.png',
          'assets/images/logos/netflix.png',
          'assets/images/logos/spotify.png',
          'assets/images/logos/more.png',
        ],
      ),
      const ExpandableRewardListItemModel(
        id: 'dining_entertainment',
        title: 'Ẩm thực & Ăn uống',
        description: 'Thưởng thức các món ăn ngon với ưu đãi đặc biệt tại hàng trăm nhà hàng và quán ăn. Giảm giá lên đến 30% cho các bữa ăn.',
        logoIcons: [
          'assets/images/logos/grab.png',
          'assets/images/logos/gojek.png',
          'assets/images/logos/baemin.png',
          'assets/images/logos/now.png',
          'assets/images/logos/kfc.png',
          'assets/images/logos/lotteria.png',
        ],
      ),
      const ExpandableRewardListItemModel(
        id: 'digital_beauty',
        title: 'Thương mại điện tử & Làm đẹp',
        description: 'Khám phá thế giới làm đẹp với các sản phẩm chất lượng cao. Ưu đãi đặc biệt cho mỹ phẩm, skincare và các dịch vụ làm đẹp.',
        logoIcons: [
          'assets/images/logos/shopee.png',
          'assets/images/logos/guardian.png',
          'assets/images/logos/hasaki.png',
          'assets/images/logos/watsons.png',
          'assets/images/logos/beauty_box.png',
          'assets/images/logos/sociolla.png',
        ],
      ),
      const ExpandableRewardListItemModel(
        id: 'supermarket_education',
        title: 'Siêu thị & Giáo dục',
        description: 'Mua sắm thông minh tại các siêu thị lớn và đầu tư cho giáo dục. Nhận ưu đãi cho học phí, sách vở và các khóa học trực tuyến.',
        logoIcons: [
          'assets/images/logos/vinmart.png',
          'assets/images/logos/coopmart.png',
          'assets/images/logos/aeon.png',
          'assets/images/logos/lotte.png',
          'assets/images/logos/udemy.png',
          'assets/images/logos/coursera.png',
        ],
      ),
      const ExpandableRewardListItemModel(
        id: 'travel_entertainment',
        title: 'Du lịch & Giải trí',
        description: 'Khám phá thế giới với các ưu đãi du lịch hấp dẫn. Đặt vé máy bay, khách sạn và các tour du lịch với giá ưu đãi.',
        logoIcons: [
          'assets/images/logos/agoda.png',
          'assets/images/logos/booking.png',
          'assets/images/logos/traveloka.png',
          'assets/images/logos/expedia.png',
          'assets/images/logos/airbnb.png',
          'assets/images/logos/vietnam_airlines.png',
        ],
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: dopNativeColors.screenBackground,
      appBar: AppBar(
        title: const Text('Expandable Reward List'),
        backgroundColor: dopNativeColors.background,
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Header section
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Chọn danh mục ưu đãi',
                    style: dopNativeTextStyles.h400(
                      color: dopNativeColors.textActive,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Chọn một danh mục để xem chi tiết ưu đãi',
                    style: dopNativeTextStyles.bodySmall(
                      color: dopNativeColors.textPassive,
                    ),
                  ),
                ],
              ),
            ),

            // Expandable list
            Expanded(
              child: ExpandableRewardListWidget(
                items: items,
                selectedItemId: selectedItemId,
                expandedItemId: expandedItemId,
                onItemSelected: _handleItemSelected,
                onItemExpanded: _handleItemExpanded,
                physics: const BouncingScrollPhysics(),
              ),
            ),

            // Bottom section with selected info
            if (selectedItemId != null) _buildBottomSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomSection() {
    final selectedItem = items.findById(selectedItemId!);
    if (selectedItem == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: dopNativeColors.background,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Đã chọn: ${selectedItem.title}',
            style: dopNativeTextStyles.h300(
              color: dopNativeColors.textActive,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ID: ${selectedItem.id}',
            style: dopNativeTextStyles.bodyXSmall(
              color: dopNativeColors.textPassive,
            ),
          ),
        ],
      ),
    );
  }

  void _handleItemSelected(String itemId) {
    setState(() {
      selectedItemId = itemId;
    });

    // You can add additional logic here, such as:
    // - Calling an API
    // - Navigating to another screen
    // - Updating other state
    debugPrint('Item selected: $itemId');
  }

  void _handleItemExpanded(String itemId, bool isExpanded) {
    setState(() {
      // Only allow one item to be expanded at a time
      expandedItemId = isExpanded ? itemId : null;
    });

    debugPrint('Item ${isExpanded ? 'expanded' : 'collapsed'}: $itemId');
  }
}
