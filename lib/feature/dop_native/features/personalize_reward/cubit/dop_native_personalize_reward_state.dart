import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../../data/response/dop_native/dop_native_metadata_item_entity.dart';

class DOPNativeAcquisitionRewardState implements BlocState {}

class DOPNativeAcquisitionRewardInitial extends DOPNativeAcquisitionRewardState {}

class DOPNativeAcquisitionRewardLoading extends DOPNativeAcquisitionRewardState {}

class DOPNativeAcquisitionRewardLoaded extends DOPNativeAcquisitionRewardState {
  final List<DOPNativeMetadataItemEntity> entities;

  DOPNativeAcquisitionRewardLoaded(this.entities);
}

class DOPNativeAcquisitionRewardError extends DOPNativeAcquisitionRewardState {
  final ErrorUIModel? error;

  DOPNativeAcquisitionRewardError(this.error);
}

class DOPNativeAcquisitionRewardSelected extends DOPNativeAcquisitionRewardState {
  final int selectedIndex;

  DOPNativeAcquisitionRewardSelected(this.selectedIndex);
}

class DOPNativeAcquisitionRewardTermAndConditionPdfUrlReady
    extends DOPNativeAcquisitionRewardState {
  final String termAndConditionPdfUrl;

  DOPNativeAcquisitionRewardTermAndConditionPdfUrlReady(this.termAndConditionPdfUrl);
}

class DOPNativeAcquisitionRewardSubmitSuccess extends DOPNativeAcquisitionRewardState {}
