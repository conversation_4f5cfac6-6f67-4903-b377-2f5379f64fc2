import 'package:flutter/material.dart';

import '../../../resources/dop_native_resources.dart';
import '../../../resources/dop_native_ui_strings.dart';

class DOPNativeCICHoldingDescriptionWidget extends StatelessWidget {
  const DOPNativeCICHoldingDescriptionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Text.rich(
      TextSpan(
        style: dopNativeTextStyles.bodyLarge(dopNativeColors.textPassive),
        children: <InlineSpan>[
          TextSpan(text: DOPNativeStrings.dopNativeESuccessAutoPCBMWGPleaseWait),
          TextSpan(
            text: ' ${DOPNativeStrings.dopNativeESuccessMWGAutoPCBWaitingDuration} ',
            style: dopNativeTextStyles.h300(),
          ),
          TextSpan(text: DOPNativeStrings.dopNativeESuccessAutoPCBMWGStatusAutoUpdate),
          TextSpan(
            text: ' ${DOPNativeStrings.dopNativeESuccessMWGAutoPCBPollingInterval}',
            style: dopNativeTextStyles.h300(),
          ),
        ],
      ),
      textAlign: TextAlign.center,
    );
  }
}
