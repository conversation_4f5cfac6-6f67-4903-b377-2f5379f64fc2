import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../../prepare_for_app_initiation.dart';
import '../../../../../resources/global.dart';
import '../../../../../widget/countdown/countdown_widget_builder.dart';
import '../../../base/cubit/dop_native_application_state.dart';
import '../../../base/dop_native_countdown_mixin.dart';
import '../../../base/dop_native_page_state_base.dart';
import '../../../resources/dop_native_resources.dart';
import '../../../resources/dop_native_ui_strings.dart';
import '../../../util/card_status/cubit/dop_native_card_status_cubit.dart';
import '../../../util/card_status/cubit/dop_native_card_status_state.dart';
import '../../../util/dop_native_navigation_utils.dart';
import '../../../widgets/appbar/dop_native_appbar_widget.dart';
import '../../../widgets/card_activate/dop_native_card_activate_countdown.dart';
import '../../../widgets/card_activate/dop_native_card_status_title_widget.dart';
import '../../../widgets/card_activate/dop_native_card_status_widget.dart';
import '../../../widgets/dop_native_card_acquisition_related_info_widget.dart';
import '../dop_native_card_cic_blocked_screen.dart';

class DOPNativeUnderwritingInProgressScreen extends PageBase {
  const DOPNativeUnderwritingInProgressScreen({super.key});

  @override
  DOPNativePageStateBase<DOPNativeUnderwritingInProgressScreen> createState() =>
      _DOPNativeUnderwritingInProgressScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(
        name: Screen.dopNativeUnderwritingInProgressScreen.name,
      );

  static void pushNamed() {
    return navigatorContext?.pushNamed(Screen.dopNativeUnderwritingInProgressScreen.name);
  }
}

class _DOPNativeUnderwritingInProgressScreenState
    extends DOPNativePageStateBase<DOPNativeUnderwritingInProgressScreen>
    with DOPNativeCountdownMixin<DOPNativeUnderwritingInProgressScreen> {
  late final DOPNativeCardStatusCubit _cubit;
  @override
  late final CountdownController countdownController;

  @override
  void initState() {
    super.initState();
    countdownController = CountdownController(
      onEmitEventIfNeed: () async {
        await dopNativeApplicationStateCubit.getApplicationState(needToHandleSpecialEvent: true);

        /// Reset the emit event status after the API call is done
        countdownController.emittingEventStatus = EmittingEventStatus.isEmitted;
      },
      onDone: dopNativeApplicationStateCubit.getApplicationState,
    );
    _cubit = DOPNativeCardStatusCubit(
        dopNativeRepo: getIt.get<DOPNativeRepo>(),
        cardStatusUseCase: CardStatusUseCase.underwritingInProgress,
        appState: getIt.get<AppState>());

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _cubit.getCardStatus();
    });
  }

  @override
  void dispose() {
    countdownController.cancel();
    super.dispose();
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<DOPNativeCardStatusCubit>(
      create: (BuildContext context) => _cubit,
      child: BlocListener<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        listener: (BuildContext context, DOPNativeCardStatusState state) {
          _handleStateChanged(state);
        },
        child: Scaffold(
          backgroundColor: dopNativeColors.screenBackground,
          appBar: const DOPNativeAppBar(),
          body: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
              child: _buildContent(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContent() {
    return BlocBuilder<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
      buildWhen: (_, DOPNativeCardStatusState state) {
        return state is UnderwritingOfflineMerchantAndLinkCard ||
            state is UnderwritingNoneOfflineMerchantOrNoneLinkCard;
      },
      builder: (BuildContext context, DOPNativeCardStatusState state) {
        if (state is UnderwritingOfflineMerchantAndLinkCard ||
            state is UnderwritingNoneOfflineMerchantOrNoneLinkCard) {
          return Column(
            children: <Widget>[
              _buildCardStatusWidget(state),
              const SizedBox(height: 24),
              const DOPNativeCardAcquisitionRelatedInfoWidget(),
            ],
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  void _handleStateChanged(DOPNativeCardStatusState state) {
    if (state is GetCardStatusLoading) {
      showDOPLoading();
      return;
    }

    hideDOPLoading();

    if (state is GetCardStatusFailure) {
      handleDopEvoApiError(state.error);
      return;
    }

    if (state is GetCardStatusBlocked) {
      DOPNativeCardCICBlockedScreen.pushReplacementNamed();
      return;
    }

    if (state is UnderwritingOfflineMerchantAndLinkCard ||
        state is UnderwritingNoneOfflineMerchantOrNoneLinkCard) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        countdownController.start();
      });
      return;
    }
  }

  Widget _buildCardStatusWidget(DOPNativeCardStatusState state) {
    if (state is UnderwritingOfflineMerchantAndLinkCard) {
      return DOPNativeCardStatusWidget(
        title: DOPNativeStrings.dopNativeUnderWritingInProgressTitle,
        description: DOPNativeStrings.dopNativeUnderWritingInProgressDesc,
        bannerTitle: DOPNativeStrings.dopNativeCountDownBannerTitle,
        actionWidget: DOPNativeCardActivateCountdown(
          countdownController: countdownController,
        ),
      );
    } else if (state is UnderwritingNoneOfflineMerchantOrNoneLinkCard) {
      return const DOPNativeCardStatusTitleWidget(
        title: DOPNativeStrings.dopNativeUnderWritingInProgressTitle,
        description: DOPNativeStrings.dopNativeUnderWritingInProgressDesc,
      );
    }

    return const SizedBox.shrink();
  }

  @override
  void handleDOPNativeApplicationStateChanged(DOPNativeApplicationState state) {
    if (state is DOPNativeApplicationStateLoaded) {
      final String? currentStep = state.entity.currentStep;

      if (currentStep == DOPNativeNavigationStep.underwritingInProgress.value) {
        _cubit.getCardStatus();
        return;
      }
    }

    super.handleDOPNativeApplicationStateChanged(state);
  }
}
