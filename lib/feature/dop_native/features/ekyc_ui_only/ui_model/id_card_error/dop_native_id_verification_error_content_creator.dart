import 'dart:ui';

import '../../../../resources/dop_native_ui_strings.dart';
import '../../utils/dop_native_error_content_ui_model.dart';
import '../ekyc_error_ui_model.dart';
import 'dop_native_id_error_content_ui_model.dart';

class DOPNativeEKYCErrorStatusCreator {
  DOPNativeEKYCErrorStatusCreator._();

  static EKYCErrorContentUIModel create({
    required EkycErrorUIModel errorUIModel,
    VoidCallback? onRetry,
  }) {
    switch (errorUIModel.code) {
      case EkycErrorCode.cardTypeNotSupported:
        return InvalidIdCardUIModel(
          onRetry: onRetry,
        );

      case EkycErrorCode.otherEkycError:
      default:
        return InvalidImageCapturedUIModel(
          onRetry: onRetry,
          description: errorUIModel.message ?? DOPNativeStrings.dopNativeDefaultEkycErrorMessage,
        );
    }
  }
}
