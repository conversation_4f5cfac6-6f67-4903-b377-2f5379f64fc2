import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/common_cubit.dart';

import 'dop_native_ekyc_confirm_cta_state.dart';

enum FieldState { valid, invalid, noneCheck }

class DOPNativeEkycConfirmCTACubit extends CommonCubit<DOPNativeEkycConfirmCTAState> {
  @visibleForTesting
  FieldState residenceAddressFieldState = FieldState.invalid;
  @visibleForTesting
  FieldState streetFieldState = FieldState.invalid;

  @visibleForTesting
  bool get isEnable =>
      residenceAddressFieldState != FieldState.invalid && streetFieldState != FieldState.invalid;

  DOPNativeEkycConfirmCTACubit() : super(DOPNativeEkycConfirmCTAState(enable: false));

  void updateResidenceAddress(FieldState state) {
    residenceAddressFieldState = state;
    emit(DOPNativeEkycConfirmCTAState(enable: isEnable));
  }

  void updateStreetAddress(FieldState state) {
    streetFieldState = state;
    emit(DOPNativeEkycConfirmCTAState(enable: isEnable));
  }
}
