enum MockTestDOPNativeGetOcrUseCase {
  getOCRDataSuccess('dop_native_get_ocr_data_success.json'),
  getOCRDataSuccessWithoutResidentAddress(
      'dop_native_get_ocr_data_success_without_resident_address.json'),
  getOCRDataSuccessWithoutEmptyOldIDCard(
      'dop_native_get_ocr_data_success_with_empty_old_id_card.json');

  final String value;

  const MockTestDOPNativeGetOcrUseCase(this.value);
}

String getMockDOPNativeGetOcrFileNameByCase(MockTestDOPNativeGetOcrUseCase mockCase) {
  return mockCase.value;
}
