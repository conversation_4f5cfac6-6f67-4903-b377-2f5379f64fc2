import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/widgets.dart';

import '../../../../resources/resources.dart';
import '../../../../util/ui_utils/evo_ui_utils.dart';
import '../../base/dop_native_page_state_base.dart';
import '../../dop_native_constants.dart';
import '../../resources/dop_native_images.dart';
import '../../resources/dop_native_resources.dart';
import '../../resources/dop_native_ui_strings.dart';
import '../../util/dop_native_navigation_utils.dart';
import '../../widgets/appbar/dop_native_appbar_widget.dart';
import '../../widgets/dop_native_status_widget.dart';

class DOPNativeCIFNoBranchScreen extends PageBase {
  static void pushReplacementNamed() {
    return navigatorContext?.pushReplacementNamed(
      Screen.dopNativeCifNoBranchScreen.name,
    );
  }

  static void pushNamed() {
    return navigatorContext?.pushNamed(
      Screen.dopNativeCifNoBranchScreen.name,
    );
  }

  const DOPNativeCIFNoBranchScreen({
    super.key,
  });

  @override
  State<DOPNativeCIFNoBranchScreen> createState() => _DOPNativeCIFNoBranchState();

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.dopNativeCifNoBranchScreen.routeName);

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;
}

class _DOPNativeCIFNoBranchState extends DOPNativePageStateBase<DOPNativeCIFNoBranchScreen> {
  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: dopNativeColors.screenBackground,
      appBar: const DOPNativeAppBar(),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: DOPNativeStatusWidget(
            icon: DOPNativeImages.icDOPRejectUserRegister,
            title: DOPNativeStrings.cifConfirmNoBranchTitle,
            iconHeight: EvoUiUtils().calculateVerticalSpace(
              context: context,
              heightPercentage: DOPNativeConstants.statusIconHeightPercentage,
            ),
            description: DOPNativeStrings.cifConfirmNoBranchSubTitle,
            ctaWidget: CommonButton(
              isWrapContent: false,
              onPressed: () {
                dopNativeNavigationUtils.navigateToLandingPage();
              },
              style: dopNativeButtonStyles.primary(ButtonSize.medium),
              child: const Text(DOPNativeStrings.cifConfirmNoBranchCTA),
            ),
          ),
        ),
      ),
    );
  }
}
