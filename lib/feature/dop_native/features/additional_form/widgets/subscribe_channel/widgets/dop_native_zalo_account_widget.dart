import 'package:flutter/material.dart';

import '../../../../../../../resources/resources.dart';
import '../../../../../resources/dop_native_images.dart';
import '../../../../../resources/dop_native_resources.dart';
import '../../../../../resources/dop_native_ui_strings.dart';
import '../../../../../widgets/radio_button/dop_native_labeled_radio_box_widget.dart';
import '../utils/subscribe_channel_constants.dart';
import 'input_zalo_phone_widget.dart';

class DOPNativeZaloAccountWidget extends StatefulWidget {
  final void Function({String? phone, bool isValidPhone}) onPhoneValueChanged;
  final void Function(ZaloPhoneSelectType selectedType) onSelectZaloTypeChanged;

  const DOPNativeZaloAccountWidget({
    required this.onPhoneValueChanged,
    required this.onSelectZaloTypeChanged,
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _DOPNativeZaloAccountWidgetState();
}

class _DOPNativeZaloAccountWidgetState extends State<DOPNativeZaloAccountWidget> {
  final ValueNotifier<ZaloPhoneSelectType?> _selected =
      ValueNotifier<ZaloPhoneSelectType?>(ZaloPhoneSelectType.current);

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _onSelectItem(ZaloPhoneSelectType.current);
    });
    super.initState();
  }

  String _getZaloPhoneTypeTitle(ZaloPhoneSelectType type) {
    switch (type) {
      case ZaloPhoneSelectType.current:
        return DOPNativeStrings.dopNativeCurrentPhone;
      case ZaloPhoneSelectType.other:
        return DOPNativeStrings.dopNativeOtherPhone;
      default:
        return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          DOPNativeStrings.dopNativeYourZaloAccount,
          style: dopNativeTextStyles.h300(),
        ),
        const SizedBox(height: 16),
        DOPNativeLabeledRadioBoxWidget<ZaloPhoneSelectType>(
          label: _getZaloPhoneTypeTitle(ZaloPhoneSelectType.current),
          value: ZaloPhoneSelectType.current,
          selectedValue: _selected,
          onChanged: _onSelectItem,
        ),
        const SizedBox(height: 16),
        DOPNativeLabeledRadioBoxWidget<ZaloPhoneSelectType>(
          label: _getZaloPhoneTypeTitle(ZaloPhoneSelectType.other),
          value: ZaloPhoneSelectType.other,
          selectedValue: _selected,
          onChanged: _onSelectItem,
        ),
        ValueListenableBuilder<ZaloPhoneSelectType?>(
          valueListenable: _selected,
          builder: (BuildContext context, ZaloPhoneSelectType? code, _) {
            return Visibility(
              visible: _selected.value == ZaloPhoneSelectType.other,
              child: InputZaloPhoneWidget(onPhoneChanged: _onPhoneNumberChange),
            );
          },
        ),
        const SizedBox(height: 24),
        Row(
          children: <Widget>[
            evoImageProvider.asset(
              DOPNativeImages.icDopNativeInfo,
              height: 32,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                DOPNativeStrings.dopNativeZaloInfoReceiveDesc,
                style: dopNativeTextStyles.bodySmall(),
              ),
            )
          ],
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  void _onPhoneNumberChange({required String phone, required bool isValid}) {
    widget.onPhoneValueChanged(
      phone: phone,
      isValidPhone: isValid,
    );
  }

  void _onSelectItem(ZaloPhoneSelectType value) {
    _selected.value = value;
    widget.onSelectZaloTypeChanged(value);
  }
}
