import 'package:flutter/material.dart';
import 'package:flutter_markdown_plus/flutter_markdown_plus.dart';

import '../../../resources/resources.dart';
import '../../../util/ui_utils/evo_ui_utils.dart';
import '../resources/dop_native_resources.dart';

class DOPNativeStatusWidget extends StatelessWidget {
  final String icon;
  final String title;
  final String? description;
  final Widget? descriptionWidget;
  final Widget? noticeWidget;
  final Widget? ctaWidget;
  final Widget? secondaryCTAWidget;
  final double? iconHeight;
  final Axis buttonAxis;

  const DOPNativeStatusWidget({
    required this.title,
    required this.icon,
    this.description,
    this.ctaWidget,
    this.descriptionWidget,
    this.iconHeight,
    this.noticeWidget,
    this.secondaryCTAWidget,
    this.buttonAxis = Axis.vertical,
    super.key,
  });

  @visibleForTesting
  static const double topPaddingPercentage = 48 / 812;

  @visibleForTesting
  static const double defaultIconHeight = 93;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        SizedBox(
          height: EvoUiUtils().calculateVerticalSpace(
            context: context,
            heightPercentage: topPaddingPercentage,
          ),
        ),
        _buildIconWidget(),
        const SizedBox(height: 24),
        Text(
          title,
          style: dopNativeTextStyles.h500(),
          textAlign: TextAlign.center,
        ),
        ..._buildDescriptionWidget(context),
        ..._buildNoticeWidget(),
        switch (buttonAxis) {
          Axis.horizontal => _buildHorizontalCTAWidget(),
          Axis.vertical => _buildVerticalCTAWidget(),
        },
      ],
    );
  }

  Widget _buildIconWidget() {
    if (icon.isNotEmpty) {
      return evoImageProvider.asset(
        icon,
        height: iconHeight ?? defaultIconHeight,
      );
    }
    return const Offstage();
  }

  List<Widget> _buildDescriptionWidget(BuildContext context) {
    final Widget? descriptionWidget = this.descriptionWidget;
    if (descriptionWidget != null) {
      return <Widget>[
        const SizedBox(height: 8),
        descriptionWidget,
      ];
    } else if (description?.isNotEmpty == true) {
      return <Widget>[
        const SizedBox(height: 8),
        // refer ticket: https://trustingsocial1.atlassian.net/browse/EMA-6383
        // support mark-down for description
        // note: for end-line character, use double space at the end of the line. e.g.: "line 1  \nline 2"
        MarkdownBody(
          data: description ?? '',
          fitContent: false,
          styleSheet: MarkdownStyleSheet.fromTheme(Theme.of(context)).copyWith(
            p: dopNativeTextStyles.bodyLarge(evoColors.textPassive),
            strong: dopNativeTextStyles.h300(color: evoColors.textActive),
            textAlign: WrapAlignment.center,
          ),
        ),
      ];
    } else {
      return <Widget>[];
    }
  }

  Widget _buildVerticalCTAWidget() {
    final Widget? ctaWidget = this.ctaWidget;
    final Widget? ndCTAWidget = secondaryCTAWidget;
    final List<Widget> widgets = <Widget>[];

    if (ctaWidget != null) {
      widgets.addAll(<Widget>[
        const SizedBox(height: 24),
        ctaWidget,
      ]);
    }

    if (ndCTAWidget != null) {
      widgets.addAll(<Widget>[
        SizedBox(height: widgets.isEmpty ? 24 : 8),
        ndCTAWidget,
      ]);
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: widgets,
    );
  }

  Widget _buildHorizontalCTAWidget() {
    final Widget? ctaWidget = this.ctaWidget;
    final Widget? ndCTAWidget = secondaryCTAWidget;
    final List<Widget> widgets = <Widget>[];

    if (ndCTAWidget != null) {
      widgets.add(ndCTAWidget);
    }

    if (ctaWidget != null) {
      widgets.addAll(<Widget>[
        SizedBox(width: widgets.isEmpty ? 0 : 8),
        ctaWidget,
      ]);
    }

    return Padding(
      padding: EdgeInsets.only(top: 24, bottom: 20),
      child: Row(
        children: widgets,
      ),
    );
  }

  List<Widget> _buildNoticeWidget() {
    final Widget? noticeWidget = this.noticeWidget;
    if (noticeWidget != null) {
      return <Widget>[
        const SizedBox(
          height: 24,
        ),
        noticeWidget
      ];
    }

    return <Widget>[];
  }
}
