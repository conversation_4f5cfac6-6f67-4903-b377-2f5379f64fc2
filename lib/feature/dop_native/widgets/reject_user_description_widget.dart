import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../resources/resources.dart';
import '../resources/dop_native_resources.dart';
import '../resources/dop_native_ui_strings.dart';

/// widget showing the description of the rejection content
/// refer figma: https://www.figma.com/file/j8MS3RYzC28OPJZ8vDFU7O/EVO-App-Design?type=design&node-id=25625-30436&mode=design&t=e3BiIYC4MRNB5cC2-0
class RejectUserDescriptionWidget extends StatelessWidget {
  const RejectUserDescriptionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        children: <TextSpan>[
          TextSpan(
            text: DOPNativeStrings.rejectionContentDescription1,
            style: dopNativeTextStyles.bodyLarge(evoColors.textPassive),
          ),
          TextSpan(
            text: ' ${ContactInfo.dopSupportPhone.applyStringFormat(
              stringFormatType: StringFormatType.phone2digits,
              prefixGroup: 4,
            )} ',
            style: dopNativeTextStyles.bodyLarge(evoColors.error),
          ),
          TextSpan(
            text: DOPNativeStrings.rejectionContentDescription2,
            style: dopNativeTextStyles.bodyLarge(evoColors.textPassive),
          ),
        ],
      ),
    );
  }
}
