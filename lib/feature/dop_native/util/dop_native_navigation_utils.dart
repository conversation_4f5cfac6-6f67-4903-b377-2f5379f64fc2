import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../data/response/dop_native/dop_native_application_state_entity.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/global.dart';
import '../../main_screen/main_screen.dart';
import '../dop_native_constants.dart';
import '../features/cif_confirm/dop_native_cif_confirm_ui_version.dart';
import '../features/e_success/dop_native_e_success_ui_version.dart';
import '../features/introduction/dop_native_introduction_screen.dart';
import '../features/logging/dop_native_event_tracking_screen_id.dart';
import '../features/status_screen/dop_native_status_screen.dart';
import '../resources/dop_native_images.dart';
import '../resources/dop_native_resources.dart';
import '../resources/dop_native_ui_strings.dart';
import 'dop_functions.dart';

DOPNativeNavigationUtils get dopNativeNavigationUtils => getIt.get<DOPNativeNavigationUtils>();

enum DOPNativeNavigationStep {
  failure('failure'),
  otp('otp'),
  ekycIdCard('ekyc.id_card'),
  ekycConfirm('ekyc.confirm'),
  ekycSelfieFlash('ekyc.selfie.flash'),
  ekycSelfieActive('ekyc.selfie.active'),
  appraisingQuickApproval('${DOPNativeConstants.appraisingPrefix}.quick_approval'),
  appraisingSecondApproval('${DOPNativeConstants.appraisingPrefix}.second_approval'),
  appraisingThirdApproval('${DOPNativeConstants.appraisingPrefix}.third_approval'),
  appraisingFourthApproval('${DOPNativeConstants.appraisingPrefix}.fourth_approval'),
  appraisingNFCCheck('${DOPNativeConstants.appraisingPrefix}.nfc_check'),
  informSuccess('inform.success'),
  appFormAdditionalInfo('app_form.additional_info'),
  esignIntro('esign.intro'),
  esignReview('esign.review'),
  esignOtp('esign.otp'),
  appFormCardDesign('app_form.card_design'),
  appFormStatementDate('app_form.statement_date'),
  cifConfirm('cif.confirm'),
  appraisingCif('${DOPNativeConstants.appraisingPrefix}.cif'),
  locked('locked'),
  success('success'),
  underwritingInProgress('underwriting.in_progress'),
  underwritingCardIssued('underwriting.card_issued'),
  underwritingCanceled('underwriting.canceled'),
  underwritingRejected('underwriting.rejected'),
  cardStatusWaitingDelivery('card_status.waiting_delivery'),
  cardStatusInDelivery('card_status.in_delivery'),
  cardStatusCardDelivered('card_status.card_delivered'),
  cardStatusInformation('card_status.information'),
  ekycNFCScan('ekyc.nfc_scan'),
  appFormSalesman('app_form.saleman'),
  appFormSalesmanConfirm('app_form.saleman_confirm'),
  appFormAcquisitionReward('app_form.acquisition_reward'),
  appFormPersonalizeReward('app_form.personalize_reward');

  final String value;

  const DOPNativeNavigationStep(this.value);
}

class DOPNativeNavigationUtils {
  /// Refer to:
  /// - Mapping screen with current_step and ui_version: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3544744596/Appendix+Essential+source
  @visibleForTesting
  final Map<String, dynamic> screenConfigs = <String, dynamic>{
    /// The same with locked state, the failure state does not need to care about the UI version.
    DOPNativeNavigationStep.failure.value: Screen.dopNativeFailureScreen,
    DOPNativeNavigationStep.otp.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeVerifyOtpScreen,
    },
    DOPNativeNavigationStep.ekycIdCard.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeIdCardCaptureIntroductionScreen,
    },
    DOPNativeNavigationStep.ekycConfirm.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeEKYCConfirmScreen,
      'v9.4.1.1': Screen.dopNativeSingleEKYCConfirmScreen,
      'v9.4.2.0': Screen.dopNativeSingleEKYCConfirmScreen,
      'v9.4.3.0': Screen.dopNativeSingleEKYCConfirmScreen,
    },
    DOPNativeNavigationStep.ekycSelfieFlash.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeSelfieFlashIntroductionScreen,
    },
    DOPNativeNavigationStep.ekycSelfieActive.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeSelfieActiveIntroductionScreen,
    },
    DOPNativeNavigationStep.appraisingQuickApproval.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeThreeStepAppraisingVerificationScreen,
    },
    DOPNativeNavigationStep.appraisingSecondApproval.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeThreeStepAppraisingVerificationScreen,
    },
    DOPNativeNavigationStep.appraisingThirdApproval.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeThreeStepAppraisingVerificationScreen,
    },
    DOPNativeNavigationStep.appraisingFourthApproval.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeAppraisingVerificationScreen,
      'v9.4.2.0': Screen.dopNativeAppraisingVerificationScreen,
      'v9.4.3.0': Screen.dopNativeAppraisingVerificationScreen,

      /// For MWG lead source
      'v9.4.1.1': Screen.dopNativeFourthAppraisingScreen,
      'v9.4.2.1': Screen.dopNativeFourthAppraisingScreen,
      'v9.4.3.1': Screen.dopNativeFourthAppraisingScreen,
    },
    DOPNativeNavigationStep.appraisingNFCCheck.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeAppraisingVerificationScreen,
    },
    DOPNativeNavigationStep.informSuccess.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeInformSuccessSemiScreen,
      'v9.4.2.0': Screen.dopNativeInformSuccessSemiScreen,
      'v9.4.3.0': Screen.dopNativeInformSuccessSemiScreen,

      /// With Sophia call
      /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3984851072/Trigger+call
      'v9.4.1.2': Screen.dopNativeInformSuccessSophiaScreen,
      'v9.4.2.2': Screen.dopNativeInformSuccessSophiaScreen,
      'v9.4.3.2': Screen.dopNativeInformSuccessSophiaScreen,
    },
    DOPNativeNavigationStep.appFormAdditionalInfo.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeAppFormAdditionalInfoScreen,
      'v9.4.2.0': Screen.dopNativeAppFormAdditionalInfoScreen,
      'v9.4.3.0': Screen.dopNativeAppFormAdditionalInfoScreen,
    },
    DOPNativeNavigationStep.esignIntro.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeESignIntroScreen,
      'v9.4.2.0': Screen.dopNativeESignIntroScreen,
      'v9.4.3.0': Screen.dopNativeESignIntroScreen,

      /// For mwg lead source
      'v9.4.1.1': Screen.dopNativeESignIntroMWGScreen,
      'v9.4.2.1': Screen.dopNativeESignIntroMWGScreen,
      'v9.4.3.1': Screen.dopNativeESignIntroMWGScreen,
    },
    DOPNativeNavigationStep.esignReview.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeESignReviewScreen,
      'v9.4.2.0': Screen.dopNativeESignReviewScreen,
      'v9.4.3.0': Screen.dopNativeESignReviewScreen,
    },
    DOPNativeNavigationStep.esignOtp.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeESignOTPScreen,
      'v9.4.2.0': Screen.dopNativeESignOTPScreen,
      'v9.4.3.0': Screen.dopNativeESignOTPScreen,
    },
    DOPNativeNavigationStep.appFormCardDesign.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeAppFormCardDesignSemiScreen,
      'v9.4.2.0': Screen.dopNativeAppFormCardDesignAutoPCBScreen,
      'v9.4.3.0': Screen.dopNativeAppFormCardDesignAutoCICScreen,
    },
    DOPNativeNavigationStep.appFormStatementDate.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeAppFormStatementDateSemiScreen,
      'v9.4.2.0': Screen.dopNativeAppFormStatementDateAutoPCBScreen,
      'v9.4.3.0': Screen.dopNativeAppFormStatementDateAutoCICScreen,
    },
    DOPNativeNavigationStep.cifConfirm.value: <String, Screen>{
      DOPNativeCifConfirmUIVersion.difPhone.value: Screen.dopNativeCifConfirmScreen,
      DOPNativeCifConfirmUIVersion.difCif.value: Screen.dopNativeCifConfirmScreen,
      DOPNativeCifConfirmUIVersion.difNationId.value: Screen.dopNativeCifConfirmScreen,
      DOPNativeCifConfirmUIVersion.difInfo.value: Screen.dopNativeCifConfirmScreen,
      DOPNativeCifConfirmUIVersion.cifReopen.value: Screen.dopNativeCifConfirmScreen,
      DOPNativeCifConfirmUIVersion.cifNoBranch.value: Screen.dopNativeCifNoBranchScreen,
    },
    DOPNativeNavigationStep.appraisingCif.value: <String, Screen>{
      'v9.4.4.0': Screen.dopNativeAppraisingVerificationScreen,
      'v9.4.5.0': Screen.dopNativeAppraisingVerificationScreen,
      'v9.4.6.0': Screen.dopNativeAppraisingVerificationScreen,
      'v9.4.7.0': Screen.dopNativeAppraisingVerificationScreen,
      'v9.4.8.0': Screen.dopNativeAppraisingVerificationScreen,
    },
    DOPNativeNavigationStep.locked.value: Screen.dopNativeEKYCLimitExceedScreen,
    DOPNativeNavigationStep.success.value: <String, dynamic>{
      /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3869671619/CO-11264+DOE+Revamp+Auto_PCB+Flow+for+MWG+Offline#%5BEVO-App%5D
      DOPNativeESuccessUIVersion.autoPcb.value: <String, Screen>{
        DOPNativeApplicationStatus.pending.value: Screen.dopNativeESuccessScreen,
        DOPNativeApplicationStatus.pushing.value: Screen.dopNativeESuccessScreen,
        DOPNativeApplicationStatus.pushed.value: Screen.dopNativeESuccessScreen,
        DOPNativeApplicationStatus.skipped.value: Screen.dopNativeESuccessScreen,
        DOPNativeApplicationStatus.exceedFailure.value: Screen.dopNativeESuccessScreen,
        DOPNativeApplicationStatus.holding.value: Screen.dopNativeESuccessCICHoldingScreen,
      },
      DOPNativeESuccessUIVersion.autoCic.value: Screen.dopNativeESuccessScreen,
      DOPNativeESuccessUIVersion.semi.value: Screen.dopNativeESuccessSemiScreen,
    },
    DOPNativeNavigationStep.underwritingInProgress.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeUnderwritingInProgressScreen,
      'v9.4.2.0': Screen.dopNativeUnderwritingInProgressScreen,
      'v9.4.3.0': Screen.dopNativeUnderwritingInProgressScreen,
    },
    DOPNativeNavigationStep.underwritingCardIssued.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeUnderwritingCardIssuedScreen,
      'v9.4.2.0': Screen.dopNativeUnderwritingCardIssuedScreen,
      'v9.4.3.0': Screen.dopNativeUnderwritingCardIssuedScreen,
    },
    DOPNativeNavigationStep.underwritingCanceled.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeFailureScreen,
      'v9.4.2.0': Screen.dopNativeFailureScreen,
      'v9.4.3.0': Screen.dopNativeFailureScreen,
    },
    DOPNativeNavigationStep.underwritingRejected.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeFailureScreen,
      'v9.4.2.0': Screen.dopNativeFailureScreen,
      'v9.4.3.0': Screen.dopNativeFailureScreen,
    },
    DOPNativeNavigationStep.cardStatusWaitingDelivery.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeUnderwritingCardStatusScreen,
      'v9.4.2.0': Screen.dopNativeUnderwritingCardStatusScreen,
      'v9.4.3.0': Screen.dopNativeUnderwritingCardStatusScreen,
    },
    DOPNativeNavigationStep.cardStatusInDelivery.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeUnderwritingCardStatusScreen,
      'v9.4.2.0': Screen.dopNativeUnderwritingCardStatusScreen,
      'v9.4.3.0': Screen.dopNativeUnderwritingCardStatusScreen,
    },
    DOPNativeNavigationStep.cardStatusCardDelivered.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeUnderwritingCardStatusScreen,
      'v9.4.2.0': Screen.dopNativeUnderwritingCardStatusScreen,
      'v9.4.3.0': Screen.dopNativeUnderwritingCardStatusScreen,
    },
    DOPNativeNavigationStep.cardStatusInformation.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeCardStatusInformationScreen,
      'v9.4.2.0': Screen.dopNativeCardStatusInformationScreen,
      'v9.4.3.0': Screen.dopNativeCardStatusInformationScreen,
    },
    DOPNativeNavigationStep.ekycNFCScan.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeNFCReaderIntroductionScreen,
    },
    DOPNativeNavigationStep.appFormSalesman.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeSalesmanScreen,
    },
    DOPNativeNavigationStep.appFormSalesmanConfirm.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeSalesmanConfirmScreen,
      'v9.4.2.0': Screen.dopNativeSalesmanConfirmScreen,
      'v9.4.3.0': Screen.dopNativeSalesmanConfirmScreen,
    },
    DOPNativeNavigationStep.appFormAcquisitionReward.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativeAcquisitionRewardScreen,
      'v9.4.2.0': Screen.dopNativeAcquisitionRewardScreen,
      'v9.4.3.0': Screen.dopNativeAcquisitionRewardScreen,
    },
    DOPNativeNavigationStep.appFormPersonalizeReward.value: <String, Screen>{
      'v9.4.1.0': Screen.dopNativePersonalizeRewardScreen,
      'v9.4.2.0': Screen.dopNativePersonalizeRewardScreen,
      'v9.4.3.0': Screen.dopNativePersonalizeRewardScreen,
    },
  };

  @visibleForTesting
  Screen? getNavigationScreen({
    required String? currentScreen,
    required String? uiVersion,
    required String? currentStatus,
  }) {
    final dynamic configScreen = screenConfigs[currentScreen];
    if (configScreen is Screen) {
      return configScreen;
    }

    /// If new state is appraising state, but app not config yet
    /// We will navigate to a AppraisingVerification Common Screen to display
    if (configScreen == null && isAppraisingState(currentScreen)) {
      return Screen.dopNativeAppraisingVerificationScreen;
    }

    final dynamic uiVersionConfig = configScreen?[uiVersion];
    if (uiVersionConfig is Map) {
      /// fall back to [DOPNativeApplicationStatus.pending] when currentStatus is null
      /// To backward compatibility with old DOP BE version
      ///
      /// Will remove in the future
      return uiVersionConfig[currentStatus ?? DOPNativeApplicationStatus.pending.value] as Screen?;
    }

    return uiVersionConfig as Screen?;
  }

  @visibleForTesting
  bool isAppraisingState(String? currentScreen) =>
      (currentScreen ?? '').split('.').first == DOPNativeConstants.appraisingPrefix;

  Future<dynamic> navigateToScreen({
    required String? currentScreen,
    required String? uiVersion,
    required String? currentStatus,
  }) async {
    final Screen? screen = getNavigationScreen(
      currentScreen: currentScreen,
      uiVersion: uiVersion,
      currentStatus: currentStatus,
    );

    if (screen == null) {
      final String errorReason = 'undefined mapping for screen=$currentScreen, '
          'ui_version=$uiVersion, '
          'current_status=$currentStatus';
      navigateCommonErrorScreen(errorReason: errorReason);
      return;
    }

    navigatorContext?.pushReplacementNamed(screen.name);
  }

  @visibleForTesting
  void clearDOPNativeState() {
    dopUtilFunction.clearDOPSessionData();
    dopUtilFunction.clearDOPNativeAccessTokenHeader();
  }

  /// navigate to DOP Landing Page
  void navigateToLandingPage({DOPNativeIntroductionScreenArg? arg}) {
    clearDOPNativeState();
    navigatorContext?.popUntilNamed(Screen.mainScreen.name);
    DOPNativeIntroductionScreen.pushNamed(arg: arg);
  }

  /// pop util to DOP Landing Page
  void popUntilToLandingPage() {
    navigatorContext?.popUntilNamed(Screen.dopNativeIntroductionScreen.name);
  }

  void navigateCommonErrorScreen({
    String? errorReason,
  }) {
    DOPNativeStatusScreen.pushReplacementNamed(
      arg: DOPNativeStatusScreenArg(
        icon: DOPNativeImages.icOtpCodeError,
        title: DOPNativeStrings.dopNativeCommonErrorTitle,
        description: DOPNativeStrings.dopNativeCommonErrorDescription,
        eventTrackingScreenId: DOPNativeEventTrackingScreenId.dopNativeCommonErrorScreen,
        errorReason: errorReason,
        enableLogging: true,
        ctaWidget: CommonButton(
          isWrapContent: false,
          onPressed: () {
            dopNativeNavigationUtils.navigateToLandingPage();
          },
          style: dopNativeButtonStyles.primary(ButtonSize.medium),
          child: const Text(DOPNativeStrings.dopNativeCommonErrorButtonTitle),
        ),
      ),
    );
  }

  void navigateToHomePage() {
    final AppState appState = getIt.get<AppState>();
    final bool isUserLogIn = appState.isUserLogIn;

    clearDOPNativeState();
    navigatorContext?.goNamed(
      Screen.mainScreen.name,
      extra: MainScreenArg(isLoggedIn: isUserLogIn),
    );
  }
}
