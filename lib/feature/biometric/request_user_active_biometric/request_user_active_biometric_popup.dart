import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../model/evo_dialog_id.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/app_setting_util.dart';
import '../../../util/evo_snackbar.dart';
import '../../../util/functions.dart';
import '../../../util/ui_utils/evo_dialog_helper.dart';
import '../../biometric/biometric_token_module/biometrics_token_module.dart';
import '../model/biometric_ui_model.dart';
import '../utils/bio_auth_result.dart';
import '../utils/biometrics_authenticate.dart';
import 'request_user_active_biometric_handler_impl.dart';

class RequestUserActiveBiometricPopup with AppSettingUtil implements BiometricTokenModuleCallback {
  final AppState appState = getIt.get<AppState>();

  final BiometricsAuthenticate? biometricsAuthenticate = getIt.get<BiometricsAuthenticate>();

  final BiometricsTokenModule? biometricsTokenModule = getIt.get<BiometricsTokenModule>();

  RequestUserActiveBiometricPopup();

  @visibleForTesting
  bool isTappedActivateBiometric = false;

  @visibleForTesting
  void Function(RequestUserActivateBiometricStatus)? onActiveBiometric;

  Future<RequestUserActivateBiometricStatus> showDialogActiveBiometric() async {
    final Completer<RequestUserActivateBiometricStatus> completer =
        Completer<RequestUserActivateBiometricStatus>();

    onActiveBiometric = (RequestUserActivateBiometricStatus status) {
      isTappedActivateBiometric = false;
      completer.complete(status);
    };

    await show();

    return completer.future;
  }

  @visibleForTesting
  Future<void> show() async {
    final BiometricTypeUIModel bioTypeInfo = getIt.get<AppState>().bioTypeInfo;
    final String title = EvoStrings.titleActiveBiometric
        .replaceVariableByValue(<String>[bioTypeInfo.biometricTypeName]);
    final String content = EvoStrings.descriptionActiveBiometric
        .replaceVariableByValue(<String>[bioTypeInfo.biometricTypeName]);

    await showBottomSheetActivateBiometric(
      title: title,
      content: content,
      onActive: onTapActive,
    );

    if (!isTappedActivateBiometric) {
      onActiveBiometric?.call(RequestUserActivateBiometricStatus.fail);
    }
  }

  Future<void> onTapActive() async {
    isTappedActivateBiometric = true;

    final bool? hasEnrolledBiometric = await biometricsAuthenticate?.hasEnrolledBiometric();
    if (hasEnrolledBiometric == true) {
      await biometricsTokenModule?.enable(callback: this);
    } else {
      await showPopupAskGoToDeviceSecuritySetting(
        footer: getDefaultFooterSecuritySettingPopup(),
      );

      /// Return the status "un_qualified"
      /// Because the user's device does not have a biometric setup configured.
      onActiveBiometric?.call(RequestUserActivateBiometricStatus.unQualified);
    }
  }

  @override
  void onSuccess() {
    final BiometricTypeUIModel bioTypeInfo = getIt.get<AppState>().bioTypeInfo;
    final String alertMsg =
        '${EvoStrings.enableText} ${EvoStrings.authenticateText.toLowerCase()} ${bioTypeInfo.biometricTypeName} ${EvoStrings.statusSuccess.toLowerCase()}';
    getIt.get<EvoSnackBar>().show(alertMsg, durationInSec: SnackBarDuration.short.value);

    onActiveBiometric?.call(RequestUserActivateBiometricStatus.success);
  }

  @override
  void onError({
    required BiometricTokenModuleErrorType type,
    String? userMessage,
    ErrorUIModel? error,
    BioAuthError? bioError,
  }) {
    switch (type) {
      case BiometricTokenModuleErrorType.biometrics:
        evoUtilFunction.handleBioError(bioError);
        break;
      default:
        getIt.get<EvoSnackBar>().show(userMessage ?? CommonStrings.otherGenericErrorMessage,
            typeSnackBar: SnackBarType.error, durationInSec: SnackBarDuration.short.value);
        break;
    }

    onActiveBiometric?.call(RequestUserActivateBiometricStatus.fail);
  }

  @visibleForTesting
  Future<void> showBottomSheetActivateBiometric({
    required String title,
    required String content,
    required VoidCallback onActive,
    VoidCallback? onSkip,
  }) async {
    return await EvoDialogHelper().showDialogBottomSheet(
      dialogId: EvoDialogId.requestEnableActiveBiometricBottomSheet,
      content: content,
      textPositive: EvoStrings.active,
      textNegative: EvoStrings.tutorialSkipButtonText,
      title: title,
      isShowButtonClose: true,
      header: evoImageProvider.asset(EvoImages.imgActiveBiometric, fit: BoxFit.fitWidth),
      onClickNegative: () {
        onSkip?.call();

        /// dismiss popup
        navigatorContext?.pop();
      },
      onClickPositive: () {
        onActive();

        /// dismiss popup
        navigatorContext?.pop();
      },
    );
  }
}
