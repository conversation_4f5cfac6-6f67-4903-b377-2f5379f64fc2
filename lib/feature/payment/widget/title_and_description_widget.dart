import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/dash_separator.dart';

import '../../../resources/resources.dart';

class TitleAndDescriptionWidget extends StatelessWidget {
  final Widget titleWidget;
  final Widget descriptionWidget;
  final bool hasDashSeparator;

  const TitleAndDescriptionWidget({
    required this.titleWidget,
    required this.descriptionWidget,
    super.key,
    this.hasDashSeparator = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            children: <Widget>[
              Flexible(fit: FlexFit.tight, child: titleWidget),
              Flexible(
                fit: FlexFit.tight,
                child: Align(
                  alignment: Alignment.centerRight,
                  child: descriptionWidget,
                ),
              ),
            ],
          ),
        ),
        hasDashSeparator
            ? DashSeparator(
                color: evoColors.divider,
              )
            : const SizedBox.shrink(),
      ],
    );
  }
}
