import '../../../../data/response/emi_package_entity.dart';
import '../../../../data/response/order_session_entity.dart';
import '../../../../data/response/voucher_entity.dart';
import '../../models/emi_option_ui_model.dart';

abstract class EmiOptionScreenState {}

class EmiOptionScreenInitialState extends EmiOptionScreenState {}

class EmiOrderInfoLoadedState extends EmiOptionScreenState {
  EmiOptionUiModel orderUiOptionModel;

  EmiOrderInfoLoadedState({required this.orderUiOptionModel});
}

class UpdateSelectedTenorState extends EmiOptionScreenState {
  final OrderSessionEntity? orderSessionEntity;
  final EmiPackageEntity? selectedEmiPackage;
  final VoucherEntity? selectedVoucher;

  UpdateSelectedTenorState({
    required this.orderSessionEntity,
    required this.selectedEmiPackage,
    this.selectedVoucher,
  });
}

class GetEmiOrderFromCacheSuccessState extends EmiOptionScreenState {
  final OrderSessionEntity? orderSession;
  final EmiPackageEntity? emiPackage;
  final VoucherEntity? selectedVoucher;

  GetEmiOrderFromCacheSuccessState({
    this.orderSession,
    this.emiPackage,
    this.selectedVoucher,
  });
}

class NeededUpdateEmiOrderState extends EmiOptionScreenState {
  final OrderSessionEntity? orderSession;
  final EmiPackageEntity? emiPackage;
  final VoucherEntity? selectedVoucher;

  NeededUpdateEmiOrderState({
    this.orderSession,
    this.emiPackage,
    this.selectedVoucher,
  });
}
