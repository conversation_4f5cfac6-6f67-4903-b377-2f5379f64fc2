import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../data/repository/checkout_repo.dart';
import '../../../../data/response/store_info_entity.dart';
import '../../../../data/response/vn_pay_qr_info_entity.dart';
import '../../../../resources/resources.dart';
import '../../check_display_pay_with_emi/check_display_pay_with_emi_cubit.dart';
import '../../qrcode_scanner/model/qr_code_type.dart';
import '../../widget/pay_with_emi_widget.dart';
import '../base_payment_input_amount_screen.dart';

class FullPaymentInputAmountArg extends PageBaseArg {
  final StoreInfoEntity? store;
  final String? productCode;
  final VNPayQrInfoEntity? vnPayQRInfo;
  final bool shouldShowEMIUnqualifiedBottomSheet;

  FullPaymentInputAmountArg({
    this.store,
    this.productCode,
    this.vnPayQRInfo,
    this.shouldShowEMIUnqualifiedBottomSheet = false,
  });
}

class FullPaymentInputAmountScreen extends BasePaymentInputAmountScreen {
  static void pushNamed({
    final StoreInfoEntity? store,
    final String? productCode,
    final VNPayQrInfoEntity? vnPayQRInfo,
    final bool shouldShowEMIUnqualifiedBottomSheet = false,
  }) {
    return navigatorContext?.pushNamed(
      Screen.paymentInputAmount.name,
      extra: FullPaymentInputAmountArg(
        store: store,
        productCode: productCode,
        vnPayQRInfo: vnPayQRInfo,
        shouldShowEMIUnqualifiedBottomSheet: shouldShowEMIUnqualifiedBottomSheet,
      ),
    );
  }

  const FullPaymentInputAmountScreen({
    required this.shouldShowEMIUnqualifiedBottomSheet,
    super.key,
    super.store,
    super.productCode,
    super.vnPayQRInfo,
  });

  final bool shouldShowEMIUnqualifiedBottomSheet;

  @override
  State<FullPaymentInputAmountScreen> createState() => FullPaymentInputAmountState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.paymentInputAmount.routeName);
}

@visibleForTesting
class FullPaymentInputAmountState
    extends BasePaymentInputAmountScreenState<FullPaymentInputAmountScreen> {
  @override
  Widget buildBodyContent() {
    return Column(
      children: <Widget>[
        buildPaymentInputAmountInfo(),
        buildEMIButton(),
        buildNextButtonWithPoweredBy(),
      ],
    );
  }

  @visibleForTesting
  Widget buildEMIButton() {
    return BlocBuilder<CheckDisplayPayWithEMICubit, CheckDisplayWithEMIState>(
      builder: (BuildContext context, CheckDisplayWithEMIState state) {
        if (state is PayWithEMIIsDisplayed) {
          return Padding(
            padding: const EdgeInsets.only(top: 20, bottom: 28),
            child: PayWithEMIWidget(
              onPayWithEMI: onPaymentWithEMI,
            ),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  @override
  void onAmountInputChanged(String? amountStr) {
    checkDisplayPayWithEMICubit.checkDisplayPayWithEMI(
      amountStr: amountStr,
      productCode: widget.productCode,
      merchantId: widget.store?.merchantId,
    );

    super.onAmountInputChanged(amountStr);
  }

  @visibleForTesting
  void onPaymentWithEMI() {
    paymentInputAmountCubit.validateSubmittedAmount(
      amountStr: amountInputController.text,
      paymentService: PaymentService.emi,
      productCode: widget.productCode,
    );
  }

  @override
  void initState() {
    super.initState();
    if (widget.shouldShowEMIUnqualifiedBottomSheet) {
      //handle the case that the product and merchant is not valid for emi, show bottom sheet warning
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showNotQualifyPayWithEMIWhenCreateOrder(
          description: QrProductCode.descriptionForEmiNotSupported(widget.productCode),
          title: EvoStrings.emiNotSupportTitle,
          onOutrightPurchasePayment: () {
            //just dismiss the popup and allow user to continue the payment
            navigatorContext?.pop();
          },
          isDismissible: false,
        );
      });
    }
  }
}
