import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../request/payment_promotion_request.dart';
import '../request/voucher_request.dart';
import '../response/campaign_list_entity.dart';
import '../response/payment_promotion_entity.dart';
import '../response/voucher_earning_entity.dart';
import '../response/voucher_list_entity.dart';
import 'base_repo.dart';
import 'campaign_repo.dart';

class CampaignRepoImpl extends BaseRepo implements CampaignRepo {
  CampaignRepoImpl(super.client);

  static const String offersUrl = 'offers';
  static const String vouchersUrl = 'vouchers';
  static const String vouchersQualificationUrl = '$vouchersUrl/qualification';
  static const String campaignsUrl = 'campaigns';

  String earnVoucherFromCampaignUrl(String? campaignId) => '$campaignsUrl/$campaignId/reserve';

  @override
  Future<CampaignListEntity> getOffers({required FlowType flowType, MockConfig? mockConfig}) async {
    final BaseResponse baseResponse = await client.get(offersUrl,
        params: <String, dynamic>{'flow_type': flowType.value}, mockConfig: mockConfig);
    return commonUtilFunction.serialize(() => CampaignListEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        CampaignListEntity.unserializable();
  }

  @override
  Future<VoucherListEntity> getVouchers({VoucherRequest? request, MockConfig? mockConfig}) async {
    final Map<String, dynamic> params = <String, dynamic>{};
    if (request?.status != null) {
      params.putIfAbsent('status', () => request?.status);
    }
    params.putIfAbsent('page_id', () => request?.pageId ?? 1);
    params.putIfAbsent('per_page', () => request?.perPage ?? 100);

    final BaseResponse baseResponse =
        await client.get(vouchersUrl, params: params, mockConfig: mockConfig);
    final VoucherListEntity voucherListEntity = commonUtilFunction.serialize(
            () => VoucherListEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        VoucherListEntity.unserializable();
    return voucherListEntity;
  }

  @override
  Future<PaymentPromotionEntity> getQualificationVoucher(
      {PaymentPromotionRequest? request, MockConfig? mockConfig}) async {
    final Map<String, dynamic> data = <String, dynamic>{};

    data.putIfAbsent('page_id', () => request?.pageId ?? 1);
    data.putIfAbsent('per_page', () => request?.perPage ?? 100);
    data.putIfAbsent('session_id', () => request?.sessionId);

    final BaseResponse baseResponse =
        await client.post(vouchersQualificationUrl, data: data, mockConfig: mockConfig);
    final PaymentPromotionEntity paymentPromotionEntity = commonUtilFunction.serialize(
            () => PaymentPromotionEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        PaymentPromotionEntity.unserializable();
    return paymentPromotionEntity;
  }

  @override
  Future<VoucherEarningEntity> earnVoucherFromCampaign({
    required String? campaignId,
    MockConfig? mockConfig,
  }) async {
    final BaseResponse baseResponse = await client.post(
      earnVoucherFromCampaignUrl(campaignId),
      mockConfig: mockConfig,
    );
    final VoucherEarningEntity voucherEarningEntity = commonUtilFunction.serialize(
          () => VoucherEarningEntity.fromBaseResponse(baseResponse),
          originalData: baseResponse,
        ) ??
        VoucherEarningEntity.unserializable();
    return voucherEarningEntity;
  }
}
