import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import 'current_cashback_info_entity.dart';

class RecordsCashbackEntity extends BaseEntity {
  final List<CurrentCashbackInfoEntity>? records;

  RecordsCashbackEntity({
    this.records,
  });

  RecordsCashbackEntity.unserializable()
      : records = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  RecordsCashbackEntity.fromBaseResponse(BaseResponse super.response)
      : records = (response.data?['records'] as List<dynamic>?)
            ?.map((dynamic e) => CurrentCashbackInfoEntity.fromJson(e as Map<String, dynamic>))
            .toList(),
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'records': records?.map((CurrentCashbackInfoEntity v) => v.toJson()).toList()
    });
    return json;
  }
}
