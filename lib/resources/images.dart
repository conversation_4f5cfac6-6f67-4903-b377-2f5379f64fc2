import 'package:flutter/material.dart';

class EvoImages {
  @visibleForTesting
  static const String png = '.png';
  @visibleForTesting
  static const String svg = '.svg';

  @visibleForTesting
  static const String assetPath = 'assets/images/';

  //PNG
  static const String icDefaultAvatar = '${assetPath}ic_default_avatar$png';
  static const String imgOpenEvoCard = '${assetPath}img_open_evo_card$png';
  static const String imgFeedback = '${assetPath}img_feedback$png';
  static const String imgMaleAvatar = '${assetPath}ic_avatar_male$png';
  static const String imgFemaleAvatar = '${assetPath}ic_avatar_female$png';
  static const String icPreBannerImage = '${assetPath}ic_pre_banner_image$png';
  static const String icPreThumbnailImage = '${assetPath}ic_pre_thumbnail_image$png';
  static const String imgActiveBiometric = '${assetPath}img_active_biometric$png';
  static const String bgScanQrCode = '${assetPath}bg_scan_qr_invalid$png';
  static const String imgConfirmDeactivateAccount = '${assetPath}img_deactivate_user$png';
  static const String imgIntroduction1 = '${assetPath}img_introduction_1$png';
  static const String imgIntroduction2 = '${assetPath}img_introduction_2$png';
  static const String imgIntroduction3 = '${assetPath}img_introduction_3$png';
  static const String imgNonAuthorizedUserScan = '${assetPath}img_non_authorized_user_scan$png';
  static const String imgUserCardNotSupport = '${assetPath}img_user_card_not_support$png';
  static const String imgCantCheckLinkedCard = '${assetPath}img_cant_check_linked_card$png';
  static const String imgDopError = '${assetPath}img_dop_error$png';
  static const String imgDopIncomplete = '${assetPath}img_dop_incomplete$png';
  static const String imgManualLinkCardCard = '${assetPath}img_manual_link_card$png';
  static const String imgDopSuccess = '${assetPath}img_dop_success$png';
  static const String imgManualCardWaiting = '${assetPath}img_manual_card_waiting$png';
  static const String imgLimitationAccess = '${assetPath}img_limit_access$png';
  static const String imgFaceOtpError = '${assetPath}img_face_otp_error$png';
  static const String imgFaceOtpSessionExpired = '${assetPath}img_face_otp_session_expired$svg';
  static const String imgEkycError = '${assetPath}img_ekyc_error$png';
  static const String imgFeedbackScreen = '${assetPath}img_feedback_screen$png';
  static const String imagePreFaceOtpManualLinkCard = '${assetPath}img_pre_face_otp$png';
  static const String bgTransactionHistoryNoLogin =
      '${assetPath}bg_transaction_history_no_login$png';
  static const String bgAppUpdate = '${assetPath}bg_app_update$png';
  static const String imgNoPromotion = '${assetPath}no_promotion_image$png';
  static const String imgDeleteAccountSuccess = '${assetPath}img_delete_account_success$png';
  static const String imgReSelectPromotion = '${assetPath}img_reselect_promotion$png';
  static const String imgVoucherEarningSuccess = '${assetPath}img_voucher_earning_success$png';
  static const String imgTransactionTooSoon = '${assetPath}img_transaction_too_soon$png';

  // Referral
  static const String icReferralProfile = '${assetPath}ic_referral_profile$png';
  static const String bgReferralQRCode = '${assetPath}bg_referral_qrcode$png';
  static const String imageUserNotInCampaign = '${assetPath}img_user_not_in_campaign$png';

  // EMI
  static const String imageEmiNotSupport = '${assetPath}img_emi_not_support$png';

  // Revamp - Non-login user
  static const String bgNonUserLoading = '${assetPath}bg_non_user_loading$png';

  // Pos limit enable guide
  static const String imgPosEnableStep1 = '${assetPath}img_pos_enable_step_1$png';
  static const String imgPosEnableStep2 = '${assetPath}img_pos_enable_step_2$png';
  static const String imgPosEnableStep3 = '${assetPath}img_pos_enable_step_3$png';
  static const String imgPosEnableStep4 = '${assetPath}img_pos_enable_step_4$png';

  // Remind POS limit enable guide
  static const String imgRemindPosEnableIntro = '${assetPath}img_remind_pos_enable_intro$png';
  static const String imgRemindPosEnableStep1 = '${assetPath}img_remind_pos_enable_step_1$png';
  static const String imgRemindPosEnableStep2 = '${assetPath}img_remind_pos_enable_step_2$png';
  static const String imgRemindPosEnableStep3 = '${assetPath}img_remind_pos_enable_step_3$png';
  static const String imgRemindPosEnableStep4 = '${assetPath}img_remind_pos_enable_step_4$png';
  static const String imgRemindPosEnableStep5 = '${assetPath}img_remind_pos_enable_step_5$png';

  //SVG
  static const String icBottomBarHome = '${assetPath}ic_bottom_bar_home$svg';
  static const String icBottomBarHistory = '${assetPath}ic_bottom_bar_history$svg';
  static const String icBottomBarReward = '${assetPath}ic_bottom_bar_reward$svg';
  static const String icBottomProfile = '${assetPath}ic_bottom_bar_profile$svg';
  static const String icScanner = '${assetPath}ic_scanner$svg';
  static const String icGift = '${assetPath}ic_gift$svg';
  static const String icGuideSearch = '${assetPath}ic_guide_search$svg';
  static const String icHistory = '${assetPath}ic_history$svg';
  static const String icSetting = '${assetPath}ic_setting$svg';
  static const String icArrowRight = '${assetPath}ic_arrow_right$svg';
  static const String icEdit = '${assetPath}ic_edit$svg';
  static const String icEvo = '${assetPath}ic_evo$svg';
  static const String icVisaLogo = '${assetPath}ic_visa_logo$svg';
  static const String icEyeShow = '${assetPath}ic_eye_show$svg';
  static const String icEyeHide = '${assetPath}ic_eye_hide$svg';
  static const String logoEvoSvg = '${assetPath}evo_logo$svg';
  static const String icClearText = '${assetPath}ic_clear_text$svg';
  static const String icArrowLeft = '${assetPath}ic_arrow_left$svg';
  static const String icMore = '${assetPath}ic_more$svg';
  static const String icEyeVisibilityOff = '${assetPath}ic_eye_visibility_off$svg';
  static const String icEyeVisibilityOn = '${assetPath}ic_eye_visibility_on$svg';
  static const String icCalendar = '${assetPath}ic_calendar$svg';
  static const String icDelete = '${assetPath}ic_delete$svg';
  static const String icShowOnPin = '${assetPath}ic_show_on_pin$svg';
  static const String icShowOffPin = '${assetPath}ic_show_off_pin$svg';
  static const String icCard = '${assetPath}ic_card$svg';
  static const String icDone = '${assetPath}ic_done$svg';
  static const String icTransport = '${assetPath}ic_transport$svg';
  static const String icBiometric = '${assetPath}ic_biometric$svg';
  static const String icChecked = '${assetPath}ic_checked$svg';
  static const String icResetPin = '${assetPath}ic_reset_pin$svg';
  static const String icCircleCheck = '${assetPath}ic_circle_check$svg';
  static const String icClear = '${assetPath}ic_clear$svg';
  static const String icToastError = '${assetPath}ic_toast_error$svg';
  static const String icAccessTime = '${assetPath}ic_access_time$svg';
  static const String icTimer = '${assetPath}ic_timer$svg';
  static const String icNotifyNotYet = '${assetPath}ic_notify_not_yet$svg';
  static const String icPaymentPending = '${assetPath}ic_payment_pending$svg';
  static const String icPaymentError = '${assetPath}ic_payment_error$svg';
  static const String icPaymentSuccess = '${assetPath}ic_payment_success$svg';
  static const String icErrorWebView = '${assetPath}ic_error_web_view$svg';
  static const String icSnackBarWarning = '${assetPath}ic_snack_bar_warning$svg';
  static const String icSnackBarNeutral = '${assetPath}ic_information$svg';
  static const String icFaceId = '${assetPath}ic_face_id$svg';
  static const String imgFaceOtpGuide = '${assetPath}img_face_otp_guide$svg';
  static const String icFingerId = '${assetPath}ic_finger_id$svg';
  static const String icFaceFingerId = '${assetPath}ic_face_finger_id$svg';
  static const String icSettingFaceFingerId = '${assetPath}ic_setting_face_finger$svg';
  static const String icSettingFaceId = '${assetPath}ic_setting_face$svg';
  static const String icSettingFingerId = '${assetPath}ic_setting_finger$svg';
  static const String evoScanQRMaskView = '${assetPath}evo_qr_mask$svg';
  static const String icQrCode = '${assetPath}ic_fi_rr_qrcode$svg';
  static const String icNotify = '${assetPath}ic_notify$svg';
  static const String icFAQ = '${assetPath}ic_faq$svg';
  static const String icActionReminder = '${assetPath}ic_action_reminder$svg';
  static const String icPaymentNoPromotion = '${assetPath}ic_payment_no_promotion$svg';
  static const String icPaymentInvalidPromotion = '${assetPath}ic_payment_invalid_promotion$svg';
  static const String icPaymentSelectPromotion = '${assetPath}ic_payment_select_promotion$svg';
  static const String icPaymentPromotionTime = '${assetPath}ic_payment_promotion_time$svg';
  static const String icPaymentPromotionInvalid = '${assetPath}ic_payment_promotion_invalid$svg';
  static const String icTransactionHistoryProcessing =
      '${assetPath}ic_transaction_history_processing$svg';
  static const String icTransactionHistorySuccess =
      '${assetPath}ic_transaction_history_success$svg';
  static const String icTransactionHistoryFailure =
      '${assetPath}ic_transaction_history_failure$svg';
  static const String bgTransactionHistoryEmpty = '${assetPath}bg_transaction_history_empty$svg';
  static const String icPinCodeError = '${assetPath}ic_error$svg';
  static const String imgSplashScreen = '${assetPath}img_splash_screen$svg';
  static const String icTPBankLogo = '${assetPath}ic_tpbank_logo$svg';
  static const String icClose = '${assetPath}ic_close$svg';
  static const String icCloseThin = '${assetPath}ic_close_thin$svg';
  static const String icAccountCircle = '${assetPath}ic_account_circle$svg';
  static const String icMasks = '${assetPath}ic_masks$svg';
  static const String icBrightnessMedium = '${assetPath}ic_brightness_medium$svg';
  static const String icDownloadPolicy = '${assetPath}ic_download_policy$svg';
  static const String icCardStatusNotice = '${assetPath}ic_card_status_notice$svg';
  static const String icCardStatusReload = '${assetPath}ic_card_status_reload$svg';
  static const String icCardStatusCheck = '${assetPath}ic_card_status_check$svg';
  static const String icDeleteAccountReasonCheck = '${assetPath}ic_delete_account_reason_check$svg';
  static const String imgWelcomeBack = '${assetPath}img_welcome_back$svg';
  static const String icScanError = '${assetPath}ic_scan_error$svg';
  static const String icCopy = '${assetPath}ic_copy$svg';
  static const String icInputAmountNote = '${assetPath}ic_input_amount_note$svg';
  static const String icEmiStar = '${assetPath}ic_emi_star$svg';
  static const String icPayWithEMI = '${assetPath}ic_pay_with_emi$svg';
  static const String icErrorEmi = '${assetPath}ic_error_emi$svg';
  static const String icSuccessEmi = '${assetPath}ic_success_emi$svg';
  static const String icProcessingEmi = '${assetPath}ic_processing_emi$svg';
  static const String icSmallRightEmi = '${assetPath}ic_small_right_emi$svg';
  static const String icCreditCard = '${assetPath}ic_credit_card$svg';
  static const String icInfo = '${assetPath}ic_info$svg';
  static const String icMenuUserEmiPayment = '${assetPath}ic_menu_user_emi_payment$svg';
  static const String icEmiPaymentNoPromotion = '${assetPath}ic_emi_payment_no_promotion$svg';
  static const String icEmiPaymentHasPromotion = '${assetPath}ic_emi_payment_has_promotion$svg';
  static const String icEmiPaymentInvalidPromotion =
      '${assetPath}ic_emi_payment_invalid_promotion$svg';

  static const String icMWGPaymentNoPromotion = '${assetPath}ic_promotion_unselected$svg';
  static const String icMWGPaymentHasPromotion = '${assetPath}ic_promotion_selected$svg';
  static const String icMWGPaymentInvalidPromotion =
      '${assetPath}ic_promotion_invalid_promotion$svg';

  static const String icPaymentArrowRight = '${assetPath}ic_payment_arrow_right$svg';
  static const String icAddPaymentMethod = '${assetPath}ic_add_payment_method$svg';
  static const String icChatWithAlice = '${assetPath}ic_chat_with_alice$svg';
  static const String icDOPNativeScan = '${assetPath}ic_dop_native_scan$svg';
  static const String icCheck = '${assetPath}ic_check$svg';
  static const String icInfoOutline = '${assetPath}ic_info_outline$svg';
  static const String icInfoThinOutline = '${assetPath}ic_info_thin_outline$svg';
  static const String icRefresh = '${assetPath}ic_refresh$svg';

  // EMI Management
  static const String icEmiManagement = '${assetPath}ic_emi_management$svg';
  static const String imgEmiEmptyState = '${assetPath}img_emi_empty_state$svg';

  // Activated POS Limit
  static const String imgActivatedCardGuide = '${assetPath}activated_card$png';
  static const String imgPOSLimitGuide = '${assetPath}pos_limit$png';
  static const String icActiveCardArrowRight = '${assetPath}ic_arrow_right_active_card$svg';

  // Activated Card Guidance
  static const String imgActivatedCardGuide1 = '${assetPath}img_active_card_guide_1$png';
  static const String imgActivatedCardGuide2 = '${assetPath}img_active_card_guide_2$png';
  static const String imgActivatedCardGuide3 = '${assetPath}img_active_card_guide_3$png';
  static const String imgActivatedCardGuide4 = '${assetPath}img_active_card_guide_4$png';
  static const String imgActivatedCardGuide5 = '${assetPath}img_active_card_guide_5$png';
  static const String imgActivatedCardGuide6 = '${assetPath}img_active_card_guide_6$png';
  static const String imgActivatedCardGuide7 = '${assetPath}img_active_card_guide_7$png';

  // Pos Limit Guidance
  static const String imgPosLimitGuide1 = '${assetPath}set_pos_limit_guide_1$png';
  static const String imgPosLimitGuide2 = '${assetPath}set_pos_limit_guide_2$png';
  static const String imgPosLimitGuide3 = '${assetPath}set_pos_limit_guide_3$png';
  static const String imgPosLimitGuide4 = '${assetPath}set_pos_limit_guide_4$png';
  static const String imgPosLimitGuide5 = '${assetPath}set_pos_limit_guide_5$png';

  // Insufficient Credit Limit
  static const String imgInsufficientCreditLimit = '${assetPath}img_credit_limit_insufficient$png';

  // Icon for revamp UI
  static const String icCloseWhite = '${assetPath}ic_close_white$svg';
  static const String icSnackBarSuccess = '${assetPath}ic_snack_bar_success$svg';
  static const String icSnackBarError = '${assetPath}ic_snack_bar_error$svg';
  static const String icSnackBarInfo = '${assetPath}ic_snack_bar_info$svg';
  static const String icSnackBarWarningV2 = '${assetPath}ic_snack_bar_warning_v2$svg';
}
