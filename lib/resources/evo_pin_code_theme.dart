// Copyright (c) 2025 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/pin_code/common_pin_code_field_shape.dart';
import 'package:flutter_common_package/widget/pin_code/common_pin_theme.dart';

import 'resources.dart';
import '../widget/evo_pin_code/evo_pin_code_config.dart';

class EvoPinCodeTheme {
  static CommonPinTheme buildDefaultPinCodeTheme({bool hasError = false}) {
    return CommonPinTheme(
      fieldHeight: EvoPinCodeConfig.defaultPinCodeFieldHeightV2,
      selectedColor: evoColorsV2.borderActive,
      inactiveColor: hasError ? evoColorsV2.statusDanger : evoColorsV2.borderContainer,
      activeColor: hasError ? evoColorsV2.statusDanger : evoColorsV2.borderLine,
      shape: CommonPinCodeFieldShape.box,
      borderRadius: BorderRadius.circular(8),
      activeFillColor: evoColorsV2.backgroundNeutralContainer,
      inactiveFillColor: evoColorsV2.backgroundNeutralContainer,
      selectedFillColor: evoColorsV2.backgroundNeutralContainer,
      borderWidth: 1.5,
      activeBorderWidth: 1.5,
      selectedBorderWidth: 1.5,
      inactiveBorderWidth: 1.5,
      disabledBorderWidth: 1.5,
      errorBorderWidth: 1.5,
    );
  }
}
