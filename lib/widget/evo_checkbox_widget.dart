// Copyright (c) 2025 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';

import '../resources/resources.dart';

/// A custom checkbox widget with optional title and tap callback.
class EvoCheckboxWidget extends StatelessWidget {
  /// Creates an [EvoCheckboxWidget].
  ///
  /// [value] determines if the checkbox is checked.
  /// [onTap] is the callback when the checkbox is tapped.
  /// [title] is an optional label displayed next to the checkbox.
  const EvoCheckboxWidget({
    required this.value,
    this.onTap,
    super.key,
    this.title,
    this.enable = true,
  });

  /// Whether the checkbox is checked.
  final bool value;

  /// Callback when the checkbox is tapped.
  final VoidCallback? onTap;

  /// Optional label displayed next to the checkbox.
  final String? title;

  /// Whether the checkbox is enabled.
  final bool enable;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: enable ? onTap : null,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      child: Row(
        children: <Widget>[
          _buildCheckbox(),
          if (title?.isNotEmpty ?? false) _buildTitle(),
        ],
      ),
    );
  }

  /// Builds the checkbox component
  Widget _buildCheckbox() {
    return Container(
      width: 20,
      height: 20,
      padding: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: _getBorderColor(),
          width: 1.5,
        ),
        color: _getBackgroundColor(),
      ),
      child: _buildCheckIcon(),
    );
  }

  /// Builds the checkbox icon (check mark)
  Widget _buildCheckIcon() {
    if (!enable) {
      return const SizedBox();
    }
    return value ? evoImageProvider.asset(EvoImages.icCheck) : const SizedBox();
  }

  /// Builds the title component if provided
  Widget _buildTitle() {
    final String titleStr = title ?? '';
    return Padding(
      padding: const EdgeInsets.only(left: 12),
      child: Text(
        titleStr,
        style: evoTextStylesV2.body(
          BodyTextType.medium,
          color: enable ? evoColorsV2.checkBoxRadioDefault : evoColorsV2.checkBoxRadioDisable,
        ),
      ),
    );
  }

  /// Gets the appropriate border color based on state
  Color _getBorderColor() {
    if (!enable) {
      return evoColorsV2.borderLine;
    }
    return value ? evoColorsV2.borderPrimary : evoColorsV2.borderContainer;
  }

  /// Gets the appropriate background color based on state
  Color _getBackgroundColor() {
    if (!enable) {
      return evoColorsV2.backgroundNeutralDisable;
    }
    return value ? evoColorsV2.backgroundPrimaryElement : evoColorsV2.backgroundNeutralContainer;
  }
}
