// Copyright (c) 2025 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';

import '../resources/resources.dart';

/// A standardized radio button widget that follows the company's design system.
///
/// The [EvoRadioWidget] widget provides a consistent radio button implementation
/// that can be used across all projects. It handles the visual representation
/// and interaction logic for radio button selections.
class EvoRadioWidget<T> extends StatelessWidget {
  /// Whether this radio button is currently selected.
  final bool isSelected;

  /// The value represented by this radio button.
  final T value;

  /// Callback that is called when the radio button is tapped.
  /// The callback provides the [value] of the selected radio button.
  final void Function(T)? onChange;

  /// Whether this radio button is enabled.
  /// When false, the radio button will be greyed out and won't respond to taps.
  final bool enable;

  /// The title displayed next to the radio button.
  final String title;

  /// Creates a standardized radio button widget.
  ///
  /// The [title] and [value] parameters are required.
  /// The [enable] parameter defaults to true, making the radio button interactive.
  /// The [isSelected] parameter defaults to false.
  /// The [onChange] callback is called when the radio button is tapped and enabled.
  const EvoRadioWidget({
    required this.title,
    required this.value,
    super.key,
    this.enable = true,
    this.isSelected = false,
    this.onChange,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: enable
          ? () {
              onChange?.call(value);
            }
          : null,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          _itemRadio(),
          const SizedBox(width: 12),
          _itemTitle(),
        ],
      ),
    );
  }

  /// Builds the radio button circle with appropriate styling based on selection state.
  /// Uses the company's design system colors for consistent appearance.
  Widget _itemRadio() {
    return Container(
      width: 20,
      height: 20,
      padding: const EdgeInsets.all(1.5),
      decoration: enable
          ? BoxDecoration(
              color: isSelected ? null : evoColorsV2.backgroundNeutralContainer,
              border: Border.all(
                color: isSelected ? evoColorsV2.borderPrimary : evoColorsV2.borderContainer,
                width: 1.5,
              ),
              shape: BoxShape.circle,
            )
          : BoxDecoration(
              color: evoColorsV2.backgroundNeutralDisable,
              border: Border.all(
                color: evoColorsV2.borderLine,
                width: 1.5,
              ),
              shape: BoxShape.circle,
            ),
      child: isSelected
          ? Container(
              decoration: BoxDecoration(
                color: evoColorsV2.backgroundPrimaryElement,
                shape: BoxShape.circle,
              ),
            )
          : null,
    );
  }

  /// Builds the title text with appropriate styling based on selection state.
  /// Uses the company's design system text styles and colors.
  Widget _itemTitle() {
    return Text(title,
        style: evoTextStylesV2.bodyMedium(
          enable
              ? (evoColorsV2.checkBoxRadioDefault)
              : evoColorsV2.checkBoxRadioDisable,
        ));
  }
}
