import 'package:flutter/material.dart';

import '../resources/resources.dart';
import '../util/evo_snackbar.dart';

class CustomSnackBarWidgetV2 extends StatelessWidget {
  final String text;
  final String? description;
  final VoidCallback? onClose;

  const CustomSnackBarWidgetV2({
    required this.text,
    this.description,
    this.onClose,
    this.typeSnackBar = SnackBarType.success,
    super.key,
  });

  final SnackBarType? typeSnackBar;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 12.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.0),
        color: background,
      ),
      child: Row(
        children: <Widget>[
          leadingIcon,
          itemTitleAndDescription(),
          InkWell(
            onTap: onClose,
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: evoImageProvider.asset(EvoImages.icCloseWhite, width: 20, height: 20),
            ),
          )
        ],
      ),
    );
  }

  @visibleForTesting
  Widget itemTitleAndDescription() {
    final String? desc = description;

    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              text,
              style: evoTextStylesV2.label(LabelTextType.medium, color: textColor),
            ),
            desc != null
                ? Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Text(
                      desc,
                      style: evoTextStylesV2.body(BodyTextType.small, color: textColor),
                    ),
                  )
                : SizedBox.shrink(),
          ],
        ),
      ),
    );
  }

  @visibleForTesting
  Widget get leadingIcon {
    final String? icon;

    switch (typeSnackBar) {
      case SnackBarType.success:
        icon = EvoImages.icSnackBarSuccess;
        break;
      case SnackBarType.error:
        icon = EvoImages.icSnackBarError;
        break;
      case SnackBarType.warning:
        icon = EvoImages.icSnackBarWarningV2;
        break;
      case SnackBarType.neutral:
        icon = EvoImages.icSnackBarInfo;
        break;
      default:
        return SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(right: 12.0),
      child: evoImageProvider.asset(
        icon,
        width: 20,
        height: 20,
      ),
    );
  }

  @visibleForTesting
  Color get background {
    switch (typeSnackBar) {
      case SnackBarType.success:
        return evoColorsV2.backgroundPrimaryElement;
      case SnackBarType.error:
        return evoColorsV2.backgroundStatusFailElement;
      case SnackBarType.warning:
        return evoColorsV2.backgroundStatusWarningElement;
      case SnackBarType.neutral:
        return evoColorsV2.backgroundNeutralBackground;
      default:
        return evoColorsV2.backgroundNeutralBackground;
    }
  }

  @visibleForTesting
  Color get textColor {
    switch (typeSnackBar) {
      case SnackBarType.warning:
        return evoColorsV2.textTitle;
      default:
        return evoColorsV2.textWhite;
    }
  }
}
