import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

T? _ambiguate<T>(T? value) => value;

class EvoOverlayEntry extends OverlayEntry {
  EvoOverlayEntry({
    required super.builder,
  });

  @override
  void markNeedsBuild() {
    final SchedulerBinding? schedulerBinding = _ambiguate(SchedulerBinding.instance);

    if (schedulerBinding == null) {
      return;
    }

    if (schedulerBinding.schedulerPhase == SchedulerPhase.persistentCallbacks) {
      schedulerBinding.addPostFrameCallback((_) {
        super.markNeedsBuild();
      });
    } else {
      super.markNeedsBuild();
    }
  }
}
