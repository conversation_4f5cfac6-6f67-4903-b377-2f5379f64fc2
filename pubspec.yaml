name: evoapp
description: Evo app

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 3.17.0+3

environment:
  sdk: ">=3.5.4 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter

  # Flutter common package of Trusting Social Mobile team
  flutter_common_package:
    git:
      url: https://github.com/tsocial/flutter-common-package.git
      ref: v4.10.4

  # Flutter client SDK for Alice using Chatwoot
  alice_flutter_sdk_chatwoot:
    git:
      url: https://github.com/tsocial/alice-flutter-sdk-chatwoot.git
      ref: v0.0.8-ts_v2

  # Switch NFC SDK Provider - choose one provider and comment code of the other

  # FPT -> 4 plugins below: trust_vision_plugin, nfc_plugin_flutter, nfc_manager, flutter_nfc_compatibility
  trust_vision_plugin: # TS SDK without NFC Reader
    git:
      url: https://github.com/tsocial/tv_flutter_sdk_evo.git
      ref: 5851d0b26cd8ff1eaba8aa9abe738c2559292012

  nfc_plugin_flutter: # FPT SDK
    git:
      url: https://github.com/tsocial/parevo-flutter-ekyc-fpt-sdk.git
      ref: f9aca86e0c2be74ee3a65fb6a13e58ea343134a7

  # TrustVision -> 1 plugin below: trust_vision_plugin
  #  trust_vision_plugin:
  #    git:
  #      url: https://github.com/tsocial/tv_flutter_sdk_evo.git
  #      ref: c2d7cb44946b8ded107c5c77a0c9434a2b93abf4

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: 1.0.8

  carousel_slider: 5.0.0
  barcode_widget: 2.0.4
  flutter_native_splash: 2.4.3

  ## When upgrade that lib, check the ticket: https://trustingsocial1.atlassian.net/browse/EMA-5203
  go_router: 15.1.2
  jwt_decoder: 2.0.1
  local_auth:
    git:
      url: https://github.com/tsocial/ts-flutter-local-auth
      path: local_auth
      ref: 2.2.0-TS

  app_settings: 5.1.1
  firebase_remote_config: 5.0.4
  ts_bio_detect_changed:
    git:
      url: https://github.com/tsocial/flutter-bio-detect-change.git
      ref: v1.0.1
  lottie: 3.2.0
  mobile_scanner: 5.2.3
  sliver_app_bar_builder: 1.1.0
  smooth_page_indicator: 1.2.0+3
  flutter_jailbreak_detection: 1.10.0
  flutter_exit_app: 1.1.4
  share_plus: 7.2.2
  flutter_markdown_plus: 1.0.0+1
  just_the_tooltip: 0.0.12
  collection: 1.18.0
  image_picker: 1.1.2
  flutter_widget_from_html_core: 0.15.2
  simple_gradient_text: 1.3.0
  appsflyer_sdk: 6.14.3
  rxdart: 0.28.0

  # DOP Native Collect Location - comment to ignore this library if you want to toggle off
  geolocator: 13.0.2

  # Application is crashed when it calls the function RecaptchaEnterprise.initClient() with the version >= 18.5.0 <= 18.5.1
  # So we choose the version 18.4.2
  recaptcha_enterprise_flutter: 18.4.2

  pdfrx: 1.0.94
  video_player: 2.9.2
  grouped_list: 6.0.0

  # TODO: migrate to pub.dev version when the author published to pub.dev
  flutter_nfc_reader:
    git:
      url: https://github.com/dotintent/flutter-nfc-reader
      ref: 0c04380de938214504fd3826d95a42e56d9264a9

  # detect when user shake the phone, to show debug menu
  shake_detector: 0.1.2
  # capture network request/response then show network monitor screen
  chucker_flutter: 1.6.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: 2.4.13
  json_serializable: 6.9.0
  flutter_lints: 5.0.0
  bloc_test: 9.1.7
  mocktail: 1.0.4
  fake_async: 1.3.1

flutter_native_splash:
  color: "#09B364"
  image: assets/images/img_splash_screen.png
  android: true
  ios: true
  android_disable_fullscreen: true
  android_12:
    image: assets/images/img_splash_screen.png
    color: "#09B364"

# The following section is specific to Flutter.
flutter:

  uses-material-design: true

  assets:
    - assets/images/
    - assets/animations/
    - assets/dop_native_images/
    - assets/dop_native_animations/
    #DYNAMIC_ASSETS_START
    # To avoid security risks, this folder will not be included in the Release build.
    # Do not remove the comment DYNAMIC_ASSETS_START & DYNAMIC_ASSETS_END as it is used to identify
    # the folder in the script /.github/remove_mock_file.sh
    - assets/mock/
    #DYNAMIC_ASSETS_END

  fonts:
    - family: PlusJakartaSans
      fonts:
        - asset: assets/fonts/PlusJakartaSans-Light.ttf
          weight: 300
        - asset: assets/fonts/PlusJakartaSans-Regular.ttf
          weight: 400
        - asset: assets/fonts/PlusJakartaSans-Medium.ttf
          weight: 500
        - asset: assets/fonts/PlusJakartaSans-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/PlusJakartaSans-Bold.ttf
          weight: 700

    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
          weight: 400
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700

  generate: true # for localizing messages
